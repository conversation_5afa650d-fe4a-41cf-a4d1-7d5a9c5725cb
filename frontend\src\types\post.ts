/**
 * 说说可见性枚举
 */
export type PostVisibility = 'public' | 'private'

/**
 * 用户信息接口（说说中的作者信息）
 */
export interface PostAuthor {
  id: number
  username: string
  email?: string
}

/**
 * 说说接口定义
 */
export interface Post {
  /**
   * 说说ID
   */
  id: number
  
  /**
   * 说说内容
   */
  content: string
  
  /**
   * 图片URL数组
   */
  images?: string[]
  
  /**
   * 可见性设置
   */
  visibility: PostVisibility
  
  /**
   * 位置信息
   */
  location?: string
  
  /**
   * 作者ID
   */
  authorId: number
  
  /**
   * 创建时间
   */
  createdAt: string
  
  /**
   * 更新时间
   */
  updatedAt: string
  
  /**
   * 作者信息
   */
  author?: PostAuthor
  
  /**
   * 点赞数量
   */
  likeCount?: number
  
  /**
   * 评论数量
   */
  commentCount?: number
  
  /**
   * 当前用户是否已点赞
   */
  isLiked?: boolean
}

/**
 * 创建说说的请求参数
 */
export interface PostCreateRequest {
  /**
   * 说说内容（1-1000字符）
   */
  content: string
  
  /**
   * 图片URL数组（最多9张）
   */
  images?: string[]
  
  /**
   * 可见性设置（默认public）
   */
  visibility?: PostVisibility
  
  /**
   * 位置信息（可选）
   */
  location?: string
}

/**
 * 更新说说的请求参数
 */
export interface PostUpdateRequest {
  /**
   * 更新的说说内容
   */
  content?: string
  
  /**
   * 更新的图片URL数组
   */
  images?: string[]
  
  /**
   * 更新的可见性设置
   */
  visibility?: PostVisibility
  
  /**
   * 更新的位置信息
   */
  location?: string
}

/**
 * 说说查询参数接口
 */
export interface PostParams {
  /**
   * 页码
   */
  page?: number
  
  /**
   * 每页条数
   */
  limit?: number
  
  /**
   * 可见性筛选
   */
  visibility?: PostVisibility | 'all'
  
  /**
   * 作者ID筛选
   */
  authorId?: number
  
  /**
   * 搜索关键词
   */
  search?: string
  
  /**
   * 排序字段
   */
  sort?: string
  
  /**
   * 排序方向
   */
  order?: 'asc' | 'desc'
}

/**
 * 分页信息接口
 */
export interface PostPagination {
  /**
   * 当前页码
   */
  page: number
  
  /**
   * 每页条数
   */
  limit: number
  
  /**
   * 总条数
   */
  total: number
  
  /**
   * 总页数
   */
  totalPages: number
}

/**
 * 说说列表响应数据接口
 */
export interface PostsResponse {
  /**
   * 请求是否成功
   */
  success: boolean
  
  /**
   * 说说列表数据
   */
  posts: Post[]
  
  /**
   * 分页信息
   */
  pagination: PostPagination
}

/**
 * 单个说说响应数据接口
 */
export interface PostResponse {
  /**
   * 请求是否成功
   */
  success: boolean
  
  /**
   * 说说数据
   */
  post: Post
}

/**
 * 说说操作的通用响应接口
 */
export interface PostActionResponse {
  /**
   * 请求是否成功
   */
  success: boolean
  
  /**
   * 响应消息
   */
  message?: string
  
  /**
   * 操作后的说说数据（可选）
   */
  post?: Post
}

/**
 * 点赞操作响应接口
 */
export interface PostLikeResponse {
  /**
   * 请求是否成功
   */
  success: boolean
  
  /**
   * 响应消息
   */
  message?: string
  
  /**
   * 操作类型
   */
  action: 'liked' | 'unliked'
  
  /**
   * 当前点赞数量
   */
  likeCount: number
}

/**
 * 点赞用户信息接口
 */
export interface PostLikeUser {
  /**
   * 用户ID
   */
  id: number
  
  /**
   * 用户名
   */
  username: string
  
  /**
   * 点赞时间
   */
  likedAt: string
}

/**
 * 点赞列表响应接口
 */
export interface PostLikesResponse {
  /**
   * 请求是否成功
   */
  success: boolean
  
  /**
   * 点赞用户列表
   */
  likes: PostLikeUser[]
  
  /**
   * 分页信息
   */
  pagination: PostPagination
}

/**
 * 说说统计信息接口
 */
export interface PostStats {
  /**
   * 总说说数
   */
  total: number
  
  /**
   * 公开说说数
   */
  public: number
  
  /**
   * 私密说说数
   */
  private: number
  
  /**
   * 今日新增说说数
   */
  todayCount: number
}

/**
 * 说说验证错误接口
 */
export interface PostValidationError {
  /**
   * 错误字段
   */
  field: string
  
  /**
   * 错误消息
   */
  message: string
}

/**
 * 说说API错误响应接口
 */
export interface PostErrorResponse {
  /**
   * 请求是否成功
   */
  success: false
  
  /**
   * 错误信息
   */
  error: {
    /**
     * 错误代码
     */
    code: string
    
    /**
     * 错误消息
     */
    message: string
    
    /**
     * 验证错误详情（可选）
     */
    details?: PostValidationError[]
  }
}
