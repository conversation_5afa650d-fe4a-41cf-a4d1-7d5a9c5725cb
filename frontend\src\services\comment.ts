import { request } from '@/utils/request'
import type {
  Comment,
  CommentCreateRequest,
  CommentUpdateRequest,
  CommentStatusUpdateRequest,
  CommentParams,
  CommentsResponse,
  CommentResponse,
  CommentActionResponse,
  CommentStats
} from '@/types/comment'

/**
 * 评论服务模块，提供评论相关的API接口封装
 */
export const commentService = {
  /**
   * 获取评论列表
   * @param params 查询参数，包括分页、文章ID、状态筛选等
   * @returns 返回包含评论列表和分页信息的Promise对象
   */
  async getComments(params: CommentParams = {}): Promise<CommentsResponse> {
    try {
      const response = await request.get<CommentsResponse>('/comments', {
        params,
        loadingMessage: '正在加载评论列表...'
      })
      return {
        success: response.data.success || true,
        comments: response.data.comments || [],
        pagination: response.data.pagination || {
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 1
        }
      }
    } catch (error) {
      console.error('获取评论列表失败:', error)
      // 返回空数据而不是抛出错误
      return {
        success: false,
        comments: [],
        pagination: {
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 1
        }
      }
    }
  },

  /**
   * 获取单个评论详情
   * @param id 评论ID
   * @returns 返回指定评论的详细信息
   */
  async getComment(id: number): Promise<Comment> {
    const response = await request.get<CommentResponse>(`/comments/${id}`, {
      loadingMessage: '正在加载评论详情...'
    })
    return response.data.comment
  },

  /**
   * 创建新评论
   * @param commentData 评论数据对象，包含评论内容、文章ID等信息
   * @returns 返回创建成功的评论对象
   */
  async createComment(commentData: CommentCreateRequest): Promise<Comment> {
    // 前端验证
    if (!commentData.content || commentData.content.trim().length === 0) {
      throw new Error('评论内容不能为空')
    }
    if (commentData.content.length > 2000) {
      throw new Error('评论内容不能超过2000个字符')
    }
    if (!commentData.articleId || commentData.articleId <= 0) {
      throw new Error('无效的文章ID')
    }

    const response = await request.post<CommentResponse>('/comments', commentData, {
      loadingMessage: '正在发表评论...'
    })
    return response.data.comment
  },

  /**
   * 回复评论（创建评论的特殊情况）
   * @param content 回复内容
   * @param articleId 文章ID
   * @param parentId 父评论ID
   * @returns 返回创建成功的回复评论对象
   */
  async replyToComment(content: string, articleId: number, parentId: number): Promise<Comment> {
    return this.createComment({
      content,
      articleId,
      parentId
    })
  },

  /**
   * 更新评论内容
   * @param id 要更新的评论ID
   * @param commentData 更新的评论数据
   * @returns 返回更新后的评论对象
   */
  async updateComment(id: number, commentData: CommentUpdateRequest): Promise<Comment> {
    // 前端验证
    if (!commentData.content || commentData.content.trim().length === 0) {
      throw new Error('评论内容不能为空')
    }
    if (commentData.content.length > 2000) {
      throw new Error('评论内容不能超过2000个字符')
    }

    const response = await request.put<CommentResponse>(`/comments/${id}`, commentData, {
      loadingMessage: '正在更新评论...'
    })
    return response.data.comment
  },

  /**
   * 删除指定评论
   * @param id 要删除的评论ID
   */
  async deleteComment(id: number): Promise<void> {
    await request.delete(`/comments/${id}`, {
      loadingMessage: '正在删除评论...'
    })
  },

  /**
   * 更新评论状态（管理员功能）
   * @param id 评论ID
   * @param statusData 状态更新数据
   * @returns 返回更新后的评论对象
   */
  async updateCommentStatus(id: number, statusData: CommentStatusUpdateRequest): Promise<Comment> {
    const response = await request.put<CommentResponse>(`/comments/${id}/status`, statusData, {
      loadingMessage: '正在更新评论状态...'
    })
    return response.data.comment
  },

  /**
   * 批准评论（管理员功能）
   * @param id 评论ID
   * @returns 返回更新后的评论对象
   */
  async approveComment(id: number): Promise<Comment> {
    return this.updateCommentStatus(id, { status: 'approved' })
  },

  /**
   * 拒绝评论（管理员功能）
   * @param id 评论ID
   * @returns 返回更新后的评论对象
   */
  async rejectComment(id: number): Promise<Comment> {
    return this.updateCommentStatus(id, { status: 'rejected' })
  },

  /**
   * 获取指定文章的评论列表
   * @param articleId 文章ID
   * @param params 查询参数
   * @returns 返回包含评论列表和分页信息的Promise对象
   */
  async getArticleComments(articleId: number, params: Omit<CommentParams, 'articleId'> = {}): Promise<CommentsResponse> {
    try {
      const response = await request.get<CommentsResponse>(`/articles/${articleId}/comments`, {
        params,
        loadingMessage: '正在加载文章评论...'
      })
      return {
        success: response.data.success || true,
        comments: response.data.comments || [],
        pagination: response.data.pagination || {
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 1
        }
      }
    } catch (error) {
      console.error('获取文章评论失败:', error)
      return {
        success: false,
        comments: [],
        pagination: {
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 1
        }
      }
    }
  },

  /**
   * 获取当前用户的评论列表
   * @param params 查询参数
   * @returns 返回包含用户评论列表和分页信息的Promise对象
   */
  async getUserComments(params: Omit<CommentParams, 'authorId'> = {}): Promise<CommentsResponse> {
    try {
      const response = await request.get<CommentsResponse>('/comments/user/me', {
        params,
        loadingMessage: '正在加载我的评论...'
      })
      return {
        success: (response as any).success || true,
        comments: (response as any).data?.comments || [],
        pagination: (response as any).data?.pagination || {
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 1
        }
      }
    } catch (error) {
      console.error('获取用户评论失败:', error)
      return {
        success: false,
        comments: [],
        pagination: {
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 1
        }
      }
    }
  },

  /**
   * 获取待审核评论列表（管理员功能）
   * @param params 查询参数
   * @returns 返回包含待审核评论列表和分页信息的Promise对象
   */
  async getPendingComments(params: Omit<CommentParams, 'status'> = {}): Promise<CommentsResponse> {
    try {
      const response = await request.get<CommentsResponse>('/comments/pending', {
        params,
        loadingMessage: '正在加载待审核评论...'
      })
      return {
        success: (response as any).success || true,
        comments: (response as any).data?.comments || [],
        pagination: (response as any).data?.pagination || {
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 1
        }
      }
    } catch (error) {
      console.error('获取待审核评论失败:', error)
      return {
        success: false,
        comments: [],
        pagination: {
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 1
        }
      }
    }
  },

  /**
   * 批量操作评论状态（管理员功能）
   * @param ids 评论ID数组
   * @param status 目标状态
   * @returns 返回操作结果
   */
  async batchUpdateCommentStatus(ids: number[], status: 'approved' | 'rejected'): Promise<CommentActionResponse> {
    const response = await request.post<CommentActionResponse>('/comments/batch-status', { ids, status }, {
      loadingMessage: `正在批量${status === 'approved' ? '批准' : '拒绝'}评论...`
    })
    return response.data
  },

  /**
   * 获取评论统计信息（管理员功能）
   * @returns 返回评论统计数据
   */
  async getCommentStats(): Promise<CommentStats> {
    const response = await request.get<{ data: { stats: CommentStats } }>('/comments/stats', {
      loadingMessage: '正在加载评论统计...'
    })
    return response.data.data.stats
  },

  /**
   * 验证评论内容
   * @param content 评论内容
   * @returns 返回验证结果和错误信息
   */
  validateCommentContent(content: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!content || content.trim().length === 0) {
      errors.push('评论内容不能为空')
    }

    if (content.length > 2000) {
      errors.push('评论内容不能超过2000个字符')
    }

    if (content.trim().length < 1) {
      errors.push('评论内容至少需要1个字符')
    }

    // 检查是否包含恶意脚本标签
    if (/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi.test(content)) {
      errors.push('评论内容包含不允许的脚本标签')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  },

  /**
   * 格式化评论时间显示
   * @param dateString 时间字符串
   * @returns 返回格式化后的时间显示
   */
  formatCommentTime(dateString: string): string {
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) {
      return '刚刚'
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60)
      return `${minutes}分钟前`
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600)
      return `${hours}小时前`
    } else if (diffInSeconds < 2592000) {
      const days = Math.floor(diffInSeconds / 86400)
      return `${days}天前`
    } else {
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    }
  },

  /**
   * 构建评论树结构（将扁平的评论列表转换为树形结构）
   * @param comments 扁平的评论列表
   * @returns 返回树形结构的评论列表
   */
  buildCommentTree(comments: Comment[]): Comment[] {
    const commentMap = new Map<number, Comment>()
    const rootComments: Comment[] = []

    // 创建评论映射
    comments.forEach(comment => {
      commentMap.set(comment.id, { ...comment, replies: [] })
    })

    // 构建树形结构
    comments.forEach(comment => {
      const commentWithReplies = commentMap.get(comment.id)!

      if (comment.parentId) {
        const parent = commentMap.get(comment.parentId)
        if (parent) {
          parent.replies = parent.replies || []
          parent.replies.push(commentWithReplies)
        }
      } else {
        rootComments.push(commentWithReplies)
      }
    })

    return rootComments
  },

  /**
   * 获取评论的回复数量（递归计算）
   * @param comment 评论对象
   * @returns 返回回复总数
   */
  getReplyCount(comment: Comment): number {
    if (!comment.replies || comment.replies.length === 0) {
      return 0
    }

    let count = comment.replies.length
    comment.replies.forEach(reply => {
      count += this.getReplyCount(reply)
    })

    return count
  }
}
