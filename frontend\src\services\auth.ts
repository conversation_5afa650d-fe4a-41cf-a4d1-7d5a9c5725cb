import { request } from '@/utils/request'
import type { User, LoginResponse, ProfileResponse, ApiError } from '@/types/auth'

/**
 * 认证服务类，提供用户认证相关功能
 */
class AuthService {
  /**
   * 获取认证请求头
   * @returns 包含认证信息的请求头对象
   */
  private getAuthHeaders() {
    const token = localStorage.getItem('token')
    return token ? { Authorization: `Bearer ${token}` } : {}
  }

  /**
   * 用户登录
   * @param username 用户名
   * @param password 密码
   * @returns 登录成功后的用户信息和令牌
   */
  async login(username: string, password: string): Promise<LoginResponse['data']> {
    const response = await request.post<LoginResponse>('/auth/login', {
      username,
      password
    }, {
      loadingMessage: '正在登录...'
    })

    if (response.data.success) {
      return response.data.data
    } else {
      throw new Error('Login failed')
    }
  }

  /**
   * 用户登出
   * @returns 无返回值的Promise
   */
  async logout(): Promise<void> {
    try {
      await request.post('/auth/logout', {}, {
        showError: false // 登出失败不显示错误
      })
    } catch (error) {
      // Logout should succeed even if the API call fails
      console.warn('Logout API call failed:', error)
    }
  }

  /**
   * 获取当前用户资料
   * @returns 用户资料信息
   */
  async getProfile(): Promise<ProfileResponse['data']> {
    const response = await request.get<ProfileResponse>('/auth/profile')

    if (response.data.success) {
      return response.data.data
    } else {
      throw new Error('Failed to get profile')
    }
  }

  /**
   * 验证当前令牌是否有效
   * @returns 令牌是否有效的布尔值
   */
  async validateToken(): Promise<boolean> {
    try {
      const response = await request.post<{ success: boolean; data: { valid: boolean } }>('/auth/validate', {}, {
        showError: false
      })
      return response.data.success && response.data.data.valid
    } catch (error) {
      return false
    }
  }

  /**
   * 刷新访问令牌
   * @param token 刷新令牌
   * @returns 新的用户信息和访问令牌
   */
  async refreshToken(token: string): Promise<LoginResponse['data']> {
    const response = await request.post<LoginResponse>('/auth/refresh', {
      token
    })

    if (response.data.success) {
      return response.data.data
    } else {
      throw new Error('Token refresh failed')
    }
  }
}

/**
 * 认证服务实例
 */
export const authService = new AuthService()