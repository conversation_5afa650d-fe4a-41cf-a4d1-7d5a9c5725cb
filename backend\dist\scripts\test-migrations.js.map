{"version": 3, "file": "test-migrations.js", "sourceRoot": "", "sources": ["../../src/scripts/test-migrations.ts"], "names": [], "mappings": ";;;AAEA,iDAA8C;AAC9C,8EAAyE;AAazE,MAAM,eAAe;IAGnB;QACE,IAAI,CAAC,MAAM,GAAG,IAAI,kCAAe,EAAE,CAAA;IACrC,CAAC;IAKO,KAAK,CAAC,YAAY;QACxB,MAAM,cAAc,GAAG,oBAAS,CAAC,iBAAiB,EAAE,CAAA;QACpD,OAAO,MAAM,cAAc,CAAC,aAAa,EAAE,CAAA;IAC7C,CAAC;IAKO,KAAK,CAAC,eAAe,CAAC,SAAiB;QAC7C,MAAM,cAAc,GAAG,oBAAS,CAAC,iBAAiB,EAAE,CAAA;QACpD,OAAO,MAAM,cAAc,CAAC,aAAa,CAAC,SAAS,CAAC,CAAA;IACtD,CAAC;IAKO,KAAK,CAAC,eAAe,CAAC,SAAiB;QAC7C,MAAM,cAAc,GAAG,oBAAS,CAAC,iBAAiB,EAAE,CAAA;QACpD,IAAI,CAAC;YACH,OAAO,MAAM,cAAc,CAAC,SAAS,CAAC,SAAS,CAAC,CAAA;QAClD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,YAAY,SAAS,SAAS,EAAE,KAAK,CAAC,CAAA;YACnD,OAAO,EAAE,CAAA;QACX,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,sBAAsB;QAClC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAA;QAE1B,MAAM,cAAc,GAAG;YACrB,OAAO,EAAE,OAAO,EAAE,aAAa,EAAE,YAAY,EAAE,MAAM;YACrD,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO;YACpD,eAAe,EAAE,YAAY,EAAE,YAAY,EAAE,kBAAkB;YAC/D,cAAc,EAAE,YAAY,EAAE,0BAA0B,EAAE,YAAY;SACvE,CAAA;QAED,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAA;QAE9C,OAAO,CAAC,GAAG,CAAC,aAAa,cAAc,CAAC,MAAM,EAAE,CAAC,CAAA;QACjD,OAAO,CAAC,GAAG,CAAC,aAAa,YAAY,CAAC,MAAM,EAAE,CAAC,CAAA;QAG/C,MAAM,aAAa,GAAG,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAA;QACnF,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,OAAO,CAAC,KAAK,CAAC,SAAS,EAAE,aAAa,CAAC,CAAA;YACvC,MAAM,IAAI,KAAK,CAAC,MAAM,aAAa,CAAC,MAAM,KAAK,CAAC,CAAA;QAClD,CAAC;QAGD,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAA;QACjF,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,WAAW,CAAC,CAAA;QACvC,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAA;IAC1B,CAAC;IAKO,KAAK,CAAC,mBAAmB;QAC/B,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAA;QAE3B,MAAM,eAAe,GAAG;YACtB,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE;YAC7D,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE;YAC/D,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,YAAY,EAAE;YACtE,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE;YAC5D,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE;YAC/D,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,UAAU,EAAE;YACnE,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE;YAC7D,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,OAAO,EAAE;YAC9D,EAAE,KAAK,EAAE,eAAe,EAAE,MAAM,EAAE,cAAc,EAAE,UAAU,EAAE,OAAO,EAAE;YACvE,EAAE,KAAK,EAAE,YAAY,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE;YAC/D,EAAE,KAAK,EAAE,YAAY,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE;YAC/D,EAAE,KAAK,EAAE,kBAAkB,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE;YACrE,EAAE,KAAK,EAAE,kBAAkB,EAAE,MAAM,EAAE,eAAe,EAAE,UAAU,EAAE,aAAa,EAAE;SAClF,CAAA;QAED,KAAK,MAAM,IAAI,IAAI,eAAe,EAAE,CAAC;YACnC,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;gBACtD,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;gBAEnC,IAAI,CAAC,MAAM,EAAE,CAAC;oBACZ,MAAM,IAAI,KAAK,CAAC,KAAK,IAAI,CAAC,MAAM,UAAU,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;gBACzD,CAAC;gBAED,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC,CAAA;YAC7E,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,aAAa,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,KAAK,CAAC,CAAA;gBAC9D,MAAM,KAAK,CAAA;YACb,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAA;IAC3B,CAAC;IAKO,KAAK,CAAC,eAAe;QAC3B,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAA;QAEzB,MAAM,gBAAgB,GAAG;YACvB,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,OAAO,CAAC,EAAE;YACtC,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,UAAU,CAAC,EAAE;YACzC,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,CAAC,MAAM,CAAC,EAAE;YACxC,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,CAAC,WAAW,CAAC,EAAE;YAC7C,EAAE,KAAK,EAAE,YAAY,EAAE,OAAO,EAAE,CAAC,MAAM,CAAC,EAAE;YAC1C,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,MAAM,CAAC,EAAE;YACpC,EAAE,KAAK,EAAE,YAAY,EAAE,OAAO,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE;YACxD,EAAE,KAAK,EAAE,kBAAkB,EAAE,OAAO,EAAE,CAAC,SAAS,EAAE,eAAe,CAAC,EAAE;SACrE,CAAA;QAED,KAAK,MAAM,SAAS,IAAI,gBAAgB,EAAE,CAAC;YACzC,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;gBAC3D,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;oBACpC,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC;wBAC9C,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC;wBAChD,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;oBAClB,OAAO,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAA;gBACnE,CAAC,CAAC,CAAA;gBAEF,IAAI,QAAQ,EAAE,CAAC;oBACb,OAAO,CAAC,GAAG,CAAC,aAAa,SAAS,CAAC,KAAK,IAAI,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;gBAC9E,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,IAAI,CAAC,cAAc,SAAS,CAAC,KAAK,IAAI,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;gBAChF,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,cAAc,SAAS,CAAC,KAAK,EAAE,EAAE,KAAK,CAAC,CAAA;YACtD,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAA;IACzB,CAAC;IAKO,KAAK,CAAC,iBAAiB;QAC7B,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAA;QAE3B,IAAI,CAAC;YAEH,MAAM,oBAAS,CAAC,KAAK,CAAC;;;OAGrB,CAAC,CAAA;YAGF,MAAM,oBAAS,CAAC,KAAK,CAAC;;;OAGrB,CAAC,CAAA;YAGF,MAAM,oBAAS,CAAC,KAAK,CAAC;;;OAGrB,CAAC,CAAA;YAEF,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAA;YAGzB,MAAM,oBAAS,CAAC,KAAK,CAAC,qDAAqD,CAAC,CAAA;YAC5E,MAAM,oBAAS,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAA;YACnE,MAAM,oBAAS,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAA;YAEvE,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAA;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,CAAA;YACnC,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,QAAQ;QACZ,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAA;QAC3B,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;QAE5B,IAAI,CAAC;YAEH,MAAM,oBAAS,CAAC,YAAY,EAAE,CAAA;YAC9B,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAA;YAGxB,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAA;YAC3B,MAAM,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAA;YAGtB,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAA;YAC3B,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAA;YACnC,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAA;YAChC,MAAM,IAAI,CAAC,eAAe,EAAE,CAAA;YAC5B,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAA;YAE9B,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;YACnC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAA;YAGzB,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA;QAE5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAA;YACjC,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,YAAY;QAChB,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAA;QAE3B,IAAI,CAAC;YAEH,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAA;YACxB,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAA;YAGvB,MAAM,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAA;YACtB,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAA;QAE3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAA;YACjC,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;CACF;AAGD,KAAK,UAAU,IAAI;IACjB,MAAM,MAAM,GAAG,IAAI,eAAe,EAAE,CAAA;IAEpC,IAAI,CAAC;QACH,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAA;QAGvB,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE,CAAC;YAC7C,MAAM,MAAM,CAAC,YAAY,EAAE,CAAA;QAC7B,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAA;IAC7B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,CAAA;QACnC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IACjB,CAAC;YAAS,CAAC;QACT,MAAM,oBAAS,CAAC,KAAK,EAAE,CAAA;IACzB,CAAC;AACH,CAAC;AAGD,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,IAAI,EAAE,CAAA;AACR,CAAC"}