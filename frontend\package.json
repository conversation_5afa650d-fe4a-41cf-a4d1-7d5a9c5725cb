{"name": "personal-blog-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "type-check": "vue-tsc --noEmit", "preview": "vite preview", "test": "vitest --run", "test:watch": "vitest"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@types/sortablejs": "^1.15.8", "axios": "^1.6.0", "echarts": "^5.4.3", "element-plus": "^2.10.4", "highlight.js": "^11.11.1", "marked": "^16.1.1", "pinia": "^2.1.7", "quill": "^2.0.3", "sortablejs": "^1.15.6", "vue": "^3.4.0", "vue-echarts": "^6.6.1", "vue-quilly": "^1.1.4", "vue-router": "^4.2.5"}, "devDependencies": {"@types/node": "^20.8.10", "@vitejs/plugin-vue": "^4.4.0", "@vue/test-utils": "^2.4.1", "happy-dom": "^18.0.1", "sass": "^1.89.2", "typescript": "^5.2.2", "vite": "^4.4.5", "vitest": "^0.34.6", "vue-tsc": "^2.0.0"}}