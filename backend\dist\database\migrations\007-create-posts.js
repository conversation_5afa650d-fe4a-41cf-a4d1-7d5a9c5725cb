"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.createTable('posts', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false,
            comment: '说说ID'
        },
        content: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: false,
            comment: '说说内容'
        },
        images: {
            type: sequelize_1.DataTypes.JSON,
            allowNull: true,
            comment: '图片URL数组（JSON格式）'
        },
        visibility: {
            type: sequelize_1.DataTypes.ENUM('public', 'private'),
            allowNull: false,
            defaultValue: 'public',
            comment: '可见性'
        },
        location: {
            type: sequelize_1.DataTypes.STRING(200),
            allowNull: true,
            comment: '位置信息'
        },
        author_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            comment: '作者ID',
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '创建时间'
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '更新时间'
        }
    }, {
        charset: 'utf8mb4',
        collate: 'utf8mb4_unicode_ci',
        comment: '说说动态表'
    });
    await queryInterface.addIndex('posts', ['author_id'], {
        name: 'idx_posts_author_id'
    });
    await queryInterface.addIndex('posts', ['visibility'], {
        name: 'idx_posts_visibility'
    });
    await queryInterface.addIndex('posts', ['created_at'], {
        name: 'idx_posts_created_at'
    });
    await queryInterface.addIndex('posts', ['author_id', 'visibility'], {
        name: 'idx_posts_author_visibility'
    });
    await queryInterface.addIndex('posts', ['visibility', 'created_at'], {
        name: 'idx_posts_visibility_created'
    });
    console.log('✅ Created posts table with indexes');
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.dropTable('posts');
    console.log('✅ Dropped posts table');
};
exports.down = down;
//# sourceMappingURL=007-create-posts.js.map