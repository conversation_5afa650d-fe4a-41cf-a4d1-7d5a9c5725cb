#!/usr/bin/env ts-node

import { sequelize } from '../config/database'
import { MigrationRunner } from '../database/migrations/migration-runner'

/**
 * 迁移测试脚本
 * 用于测试迁移脚本的正确性和完整性
 */

interface TableInfo {
  tableName: string
  columnCount: number
  indexCount: number
}

class MigrationTester {
  private runner: MigrationRunner

  constructor() {
    this.runner = new MigrationRunner()
  }

  /**
   * 获取数据库中的所有表
   */
  private async getAllTables(): Promise<string[]> {
    const queryInterface = sequelize.getQueryInterface()
    return await queryInterface.showAllTables()
  }

  /**
   * 获取表的列信息
   */
  private async getTableColumns(tableName: string): Promise<any[]> {
    const queryInterface = sequelize.getQueryInterface()
    return await queryInterface.describeTable(tableName)
  }

  /**
   * 获取表的索引信息
   */
  private async getTableIndexes(tableName: string): Promise<any[]> {
    const queryInterface = sequelize.getQueryInterface()
    try {
      return await queryInterface.showIndex(tableName)
    } catch (error) {
      console.warn(`⚠️ 无法获取表 ${tableName} 的索引信息:`, error)
      return []
    }
  }

  /**
   * 验证表结构
   */
  private async validateTableStructure(): Promise<void> {
    console.log('🔍 验证表结构...')

    const expectedTables = [
      'users', 'roles', 'permissions', 'categories', 'tags',
      'settings', 'articles', 'posts', 'comments', 'media',
      'notifications', 'audit_logs', 'user_roles', 'role_permissions',
      'article_tags', 'post_likes', 'notification_preferences', 'migrations'
    ]

    const actualTables = await this.getAllTables()
    
    console.log(`📋 期望表数量: ${expectedTables.length}`)
    console.log(`📋 实际表数量: ${actualTables.length}`)

    // 检查缺失的表
    const missingTables = expectedTables.filter(table => !actualTables.includes(table))
    if (missingTables.length > 0) {
      console.error('❌ 缺失的表:', missingTables)
      throw new Error(`缺失 ${missingTables.length} 个表`)
    }

    // 检查多余的表
    const extraTables = actualTables.filter(table => !expectedTables.includes(table))
    if (extraTables.length > 0) {
      console.warn('⚠️ 多余的表:', extraTables)
    }

    console.log('✅ 表结构验证通过')
  }

  /**
   * 验证外键约束
   */
  private async validateForeignKeys(): Promise<void> {
    console.log('🔍 验证外键约束...')

    const foreignKeyTests = [
      { table: 'settings', column: 'user_id', references: 'users' },
      { table: 'articles', column: 'author_id', references: 'users' },
      { table: 'articles', column: 'category_id', references: 'categories' },
      { table: 'posts', column: 'author_id', references: 'users' },
      { table: 'comments', column: 'author_id', references: 'users' },
      { table: 'comments', column: 'article_id', references: 'articles' },
      { table: 'comments', column: 'post_id', references: 'posts' },
      { table: 'media', column: 'uploader_id', references: 'users' },
      { table: 'notifications', column: 'recipient_id', references: 'users' },
      { table: 'user_roles', column: 'user_id', references: 'users' },
      { table: 'user_roles', column: 'role_id', references: 'roles' },
      { table: 'role_permissions', column: 'role_id', references: 'roles' },
      { table: 'role_permissions', column: 'permission_id', references: 'permissions' }
    ]

    for (const test of foreignKeyTests) {
      try {
        const columns = await this.getTableColumns(test.table)
        const column = columns[test.column]
        
        if (!column) {
          throw new Error(`列 ${test.column} 不存在于表 ${test.table}`)
        }

        console.log(`✅ 外键验证通过: ${test.table}.${test.column} -> ${test.references}`)
      } catch (error) {
        console.error(`❌ 外键验证失败: ${test.table}.${test.column}`, error)
        throw error
      }
    }

    console.log('✅ 外键约束验证通过')
  }

  /**
   * 验证索引
   */
  private async validateIndexes(): Promise<void> {
    console.log('🔍 验证索引...')

    const importantIndexes = [
      { table: 'users', columns: ['email'] },
      { table: 'users', columns: ['username'] },
      { table: 'articles', columns: ['slug'] },
      { table: 'articles', columns: ['author_id'] },
      { table: 'categories', columns: ['slug'] },
      { table: 'tags', columns: ['slug'] },
      { table: 'user_roles', columns: ['user_id', 'role_id'] },
      { table: 'role_permissions', columns: ['role_id', 'permission_id'] }
    ]

    for (const indexTest of importantIndexes) {
      try {
        const indexes = await this.getTableIndexes(indexTest.table)
        const hasIndex = indexes.some(index => {
          const indexColumns = Array.isArray(index.fields) 
            ? index.fields.map((f: any) => f.attribute || f)
            : [index.fields]
          return indexTest.columns.every(col => indexColumns.includes(col))
        })

        if (hasIndex) {
          console.log(`✅ 索引验证通过: ${indexTest.table}(${indexTest.columns.join(', ')})`)
        } else {
          console.warn(`⚠️ 索引可能缺失: ${indexTest.table}(${indexTest.columns.join(', ')})`)
        }
      } catch (error) {
        console.warn(`⚠️ 无法验证索引: ${indexTest.table}`, error)
      }
    }

    console.log('✅ 索引验证完成')
  }

  /**
   * 测试数据插入
   */
  private async testDataInsertion(): Promise<void> {
    console.log('🔍 测试数据插入...')

    try {
      // 测试插入用户
      await sequelize.query(`
        INSERT INTO users (username, email, password_hash, created_at, updated_at) 
        VALUES ('test_user', '<EMAIL>', 'hashed_password', NOW(), NOW())
      `)

      // 测试插入角色
      await sequelize.query(`
        INSERT INTO roles (name, description, created_at, updated_at) 
        VALUES ('test_role', 'Test role', NOW(), NOW())
      `)

      // 测试插入分类
      await sequelize.query(`
        INSERT INTO categories (name, slug, created_at, updated_at) 
        VALUES ('Test Category', 'test-category', NOW(), NOW())
      `)

      console.log('✅ 数据插入测试通过')

      // 清理测试数据
      await sequelize.query(`DELETE FROM categories WHERE slug = 'test-category'`)
      await sequelize.query(`DELETE FROM roles WHERE name = 'test_role'`)
      await sequelize.query(`DELETE FROM users WHERE username = 'test_user'`)

      console.log('✅ 测试数据清理完成')
    } catch (error) {
      console.error('❌ 数据插入测试失败:', error)
      throw error
    }
  }

  /**
   * 运行完整测试
   */
  async runTests(): Promise<void> {
    console.log('🧪 开始迁移测试...')
    console.log('=' .repeat(50))

    try {
      // 连接数据库
      await sequelize.authenticate()
      console.log('✅ 数据库连接成功')

      // 执行迁移
      console.log('\n📦 执行迁移...')
      await this.runner.up()

      // 验证表结构
      console.log('\n🔍 开始验证...')
      await this.validateTableStructure()
      await this.validateForeignKeys()
      await this.validateIndexes()
      await this.testDataInsertion()

      console.log('\n' + '=' .repeat(50))
      console.log('🎉 所有测试通过!')

      // 显示最终状态
      await this.runner.status()

    } catch (error) {
      console.error('\n❌ 测试失败:', error)
      throw error
    }
  }

  /**
   * 测试迁移回滚
   */
  async testRollback(): Promise<void> {
    console.log('🔄 测试迁移回滚...')

    try {
      // 执行回滚
      await this.runner.down()
      console.log('✅ 迁移回滚成功')

      // 重新执行迁移
      await this.runner.up()
      console.log('✅ 迁移重新执行成功')

    } catch (error) {
      console.error('❌ 回滚测试失败:', error)
      throw error
    }
  }
}

// 主函数
async function main() {
  const tester = new MigrationTester()
  
  try {
    await tester.runTests()
    
    // 如果指定了回滚测试参数
    if (process.argv.includes('--test-rollback')) {
      await tester.testRollback()
    }
    
    console.log('\n🎉 迁移测试完成!')
  } catch (error) {
    console.error('\n❌ 迁移测试失败:', error)
    process.exit(1)
  } finally {
    await sequelize.close()
  }
}

// 执行测试
if (require.main === module) {
  main()
}
