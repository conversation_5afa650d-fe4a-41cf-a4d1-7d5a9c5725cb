"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.createTable('notifications', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false,
            comment: '通知ID'
        },
        type: {
            type: sequelize_1.DataTypes.ENUM('comment', 'like', 'follow', 'system', 'article'),
            allowNull: false,
            comment: '通知类型'
        },
        title: {
            type: sequelize_1.DataTypes.STRING(200),
            allowNull: false,
            comment: '通知标题'
        },
        content: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: false,
            comment: '通知内容'
        },
        recipient_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            comment: '接收者ID',
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        sender_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true,
            comment: '发送者ID（系统通知时为空）',
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'SET NULL'
        },
        related_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true,
            comment: '相关对象ID（如文章ID、评论ID等）'
        },
        related_type: {
            type: sequelize_1.DataTypes.STRING(50),
            allowNull: true,
            comment: '相关对象类型（如article、comment等）'
        },
        is_read: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false,
            comment: '是否已读'
        },
        read_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: true,
            comment: '阅读时间'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '创建时间'
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '更新时间'
        }
    }, {
        charset: 'utf8mb4',
        collate: 'utf8mb4_unicode_ci',
        comment: '通知表'
    });
    await queryInterface.addIndex('notifications', ['recipient_id'], {
        name: 'idx_notifications_recipient_id'
    });
    await queryInterface.addIndex('notifications', ['sender_id'], {
        name: 'idx_notifications_sender_id'
    });
    await queryInterface.addIndex('notifications', ['type'], {
        name: 'idx_notifications_type'
    });
    await queryInterface.addIndex('notifications', ['is_read'], {
        name: 'idx_notifications_is_read'
    });
    await queryInterface.addIndex('notifications', ['recipient_id', 'is_read'], {
        name: 'idx_notifications_recipient_read'
    });
    await queryInterface.addIndex('notifications', ['related_type', 'related_id'], {
        name: 'idx_notifications_related'
    });
    console.log('✅ Created notifications table with indexes');
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.dropTable('notifications');
    console.log('✅ Dropped notifications table');
};
exports.down = down;
//# sourceMappingURL=010-create-notifications.js.map