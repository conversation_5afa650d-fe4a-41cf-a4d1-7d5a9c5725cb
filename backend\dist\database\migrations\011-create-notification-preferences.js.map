{"version": 3, "file": "011-create-notification-preferences.js", "sourceRoot": "", "sources": ["../../../src/database/migrations/011-create-notification-preferences.ts"], "names": [], "mappings": ";;;AAAA,yCAAqD;AAO9C,MAAM,EAAE,GAAG,KAAK,EAAE,cAA8B,EAAiB,EAAE;IACxE,MAAM,cAAc,CAAC,WAAW,CAAC,0BAA0B,EAAE;QAC3D,EAAE,EAAE;YACF,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,UAAU,EAAE,IAAI;YAChB,aAAa,EAAE,IAAI;YACnB,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,MAAM;SAChB;QACD,OAAO,EAAE;YACP,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,MAAM,EAAE,IAAI;YACZ,OAAO,EAAE,MAAM;YACf,UAAU,EAAE;gBACV,KAAK,EAAE,OAAO;gBACd,GAAG,EAAE,IAAI;aACV;YACD,QAAQ,EAAE,SAAS;YACnB,QAAQ,EAAE,SAAS;SACpB;QACD,mBAAmB,EAAE;YACnB,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,IAAI;YAClB,OAAO,EAAE,UAAU;SACpB;QACD,qBAAqB,EAAE;YACrB,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,IAAI;YAClB,OAAO,EAAE,UAAU;SACpB;QACD,kBAAkB,EAAE;YAClB,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,IAAI;YAClB,OAAO,EAAE,UAAU;SACpB;QACD,oBAAoB,EAAE;YACpB,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,IAAI;YAClB,OAAO,EAAE,UAAU;SACpB;QACD,oBAAoB,EAAE;YACpB,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,IAAI;YAClB,OAAO,EAAE,UAAU;SACpB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;YAC3B,OAAO,EAAE,MAAM;SAChB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;YAC3B,OAAO,EAAE,MAAM;SAChB;KACF,EAAE;QACD,OAAO,EAAE,SAAS;QAClB,OAAO,EAAE,oBAAoB;QAC7B,OAAO,EAAE,OAAO;KACjB,CAAC,CAAA;IAGF,MAAM,cAAc,CAAC,QAAQ,CAAC,0BAA0B,EAAE,CAAC,SAAS,CAAC,EAAE;QACrE,IAAI,EAAE,sCAAsC;QAC5C,MAAM,EAAE,IAAI;KACb,CAAC,CAAA;IAEF,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAA;AACtE,CAAC,CAAA;AA5EY,QAAA,EAAE,MA4Ed;AAEM,MAAM,IAAI,GAAG,KAAK,EAAE,cAA8B,EAAiB,EAAE;IAC1E,MAAM,cAAc,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAA;IAC1D,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAA;AACzD,CAAC,CAAA;AAHY,QAAA,IAAI,QAGhB"}