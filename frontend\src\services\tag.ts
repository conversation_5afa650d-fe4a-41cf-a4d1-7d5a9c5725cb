import { request } from '@/utils/request'
import type { Tag, TagsResponse, TagArticlesResponse } from '@/types/tag'

/**
 * 标签相关服务模块
 */
export const tagService = {
  /**
   * 获取所有标签列表
   * @returns Promise<Tag[]> 标签数组
   */
  async getTags(): Promise<Tag[]> {
    try {
      const response = await request.get<TagsResponse>('/tags', {
        loadingMessage: '正在加载标签...'
      })
      return response.data.tags || []
    } catch (error) {
      console.error('获取标签列表失败:', error)
      return [] // 返回空数组而不是抛出错误
    }
  },

  /**
   * 根据标签名称获取该标签下的文章列表
   * @param tagName 标签名称
   * @param page 页码，默认为1
   * @param limit 每页数量，默认为10
   * @returns Promise<TagArticlesResponse> 包含文章列表和分页信息的响应对象
   */
  async getTagArticles(tagName: string, page = 1, limit = 10): Promise<TagArticlesResponse> {
    const response = await request.get<TagArticlesResponse>(`/tags/${tagName}/articles`, {
      params: { page, limit },
      loadingMessage: '正在加载标签文章...'
    })
    return {
      success: response.data.success,
      articles: response.data.articles,
      pagination: response.data.pagination
    }
  },

  /**
   * 创建新标签
   * @param name 标签名称
   * @param slug 标签slug（可选，如不提供则自动生成）
   * @returns Promise<Tag> 创建成功的标签对象
   */
  async createTag(name: string, slug?: string): Promise<Tag> {
    const requestData: { name: string; slug?: string } = { name }
    if (slug !== undefined && slug.trim() !== '') {
      requestData.slug = slug.trim()
    }

    const response = await request.post<{ tag: Tag }>('/tags', requestData, {
      loadingMessage: '正在创建标签...'
    })
    return response.data.tag
  },

  /**
   * 更新指定ID的标签
   * @param id 标签ID
   * @param name 新的标签名称
   * @param slug 标签slug（可选，如不提供则自动生成）
   * @returns Promise<Tag> 更新后的标签对象
   */
  async updateTag(id: number, name: string, slug?: string): Promise<Tag> {
    const requestData: { name: string; slug?: string } = { name }
    if (slug !== undefined && slug.trim() !== '') {
      requestData.slug = slug.trim()
    }

    const response = await request.put<{ tag: Tag }>(`/tags/${id}`, requestData, {
      loadingMessage: '正在更新标签...'
    })
    return response.data.tag
  },

  /**
   * 删除指定ID的标签
   * @param id 标签ID
   * @returns Promise<void>
   */
  async deleteTag(id: number): Promise<void> {
    await request.delete(`/tags/${id}`, {
      loadingMessage: '正在删除标签...'
    })
  },

  /**
   * 批量删除标签
   * @param tagIds 要删除的标签ID数组
   * @returns Promise<{deletedCount: number, requestedCount: number}>
   */
  async batchDeleteTags(tagIds: number[]): Promise<{ deletedCount: number, requestedCount: number }> {
    const response = await request.post<{ deletedCount: number, requestedCount: number }>('/tags/batch-delete', { tagIds }, {
      loadingMessage: '正在批量删除标签...'
    })
    return {
      deletedCount: response.data.deletedCount,
      requestedCount: response.data.requestedCount
    }
  },

  /**
   * 获取标签统计信息
   * @returns Promise<{stats: any, tags: Tag[]}> 统计信息和标签列表
   */
  async getTagStats(): Promise<{ stats: any, tags: Tag[] }> {
    try {
      const response = await request.get<{ stats: any, tags: Tag[] }>('/tags/stats', {
        loadingMessage: '正在加载标签统计...'
      })
      return {
        stats: response.data.stats,
        tags: response.data.tags || []
      }
    } catch (error) {
      console.error('获取标签统计失败:', error)
      return { stats: {}, tags: [] }
    }
  },

  /**
   * 获取热门标签列表
   * @param limit 限制返回的标签数量，默认为10
   * @returns Promise<Tag[]> 热门标签数组
   */
  async getPopularTags(limit = 10): Promise<Tag[]> {
    try {
      const response = await request.get<{ tags: Tag[] }>('/tags/popular', {
        params: { limit },
        loadingMessage: '正在加载热门标签...'
      })
      return response.data.tags || []
    } catch (error) {
      console.error('获取热门标签失败:', error)
      // 如果API不支持，则从所有标签中筛选热门的
      try {
        const allTags = await this.getTags()
        return allTags
          .filter(tag => (tag.articleCount || 0) > 0)
          .sort((a, b) => (b.articleCount || 0) - (a.articleCount || 0))
          .slice(0, limit)
      } catch (fallbackError) {
        console.error('获取热门标签降级方案失败:', fallbackError)
        return []
      }
    }
  }
}