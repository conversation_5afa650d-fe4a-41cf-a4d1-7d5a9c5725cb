"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.up = up;
exports.down = down;
async function up(queryInterface) {
    console.log('🌱 创建媒体和通知种子数据...');
    const media = [
        {
            id: 1,
            filename: 'react-hooks-diagram.png',
            original_name: '<PERSON><PERSON> Hooks 生命周期图.png',
            mime_type: 'image/png',
            size: 245760,
            url: 'https://example.com/uploads/2024/01/react-hooks-diagram.png',
            thumbnail_url: 'https://example.com/uploads/2024/01/thumbs/react-hooks-diagram.png',
            width: 1200,
            height: 800,
            uploader_id: 2,
            category: 'image',
            tags: JSON.stringify(['React', 'Hooks', '教程', '图解']),
            description: 'React Hooks 生命周期和使用方法图解',
            is_public: true,
            created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        },
        {
            id: 2,
            filename: 'nodejs-performance-chart.jpg',
            original_name: 'Node.js 性能优化对比图.jpg',
            mime_type: 'image/jpeg',
            size: 189440,
            url: 'https://example.com/uploads/2024/01/nodejs-performance-chart.jpg',
            thumbnail_url: 'https://example.com/uploads/2024/01/thumbs/nodejs-performance-chart.jpg',
            width: 1000,
            height: 600,
            uploader_id: 2,
            category: 'image',
            tags: JSON.stringify(['Node.js', '性能优化', '图表', '对比']),
            description: 'Node.js 应用性能优化前后对比图表',
            is_public: true,
            created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
        },
        {
            id: 3,
            filename: 'typescript-types-demo.png',
            original_name: 'TypeScript 类型系统示例.png',
            mime_type: 'image/png',
            size: 156672,
            url: 'https://example.com/uploads/2024/01/typescript-types-demo.png',
            thumbnail_url: 'https://example.com/uploads/2024/01/thumbs/typescript-types-demo.png',
            width: 800,
            height: 1000,
            uploader_id: 2,
            category: 'image',
            tags: JSON.stringify(['TypeScript', '类型系统', '代码示例']),
            description: 'TypeScript 高级类型系统使用示例截图',
            is_public: true,
            created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
        },
        {
            id: 4,
            filename: 'css-grid-layout.png',
            original_name: 'CSS Grid 布局示例.png',
            mime_type: 'image/png',
            size: 98304,
            url: 'https://example.com/uploads/2024/01/css-grid-layout.png',
            thumbnail_url: 'https://example.com/uploads/2024/01/thumbs/css-grid-layout.png',
            width: 1200,
            height: 800,
            uploader_id: 3,
            category: 'image',
            tags: JSON.stringify(['CSS', 'Grid', '布局', '前端']),
            description: 'CSS Grid 布局实现的复杂网格示例',
            is_public: true,
            created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
        },
        {
            id: 5,
            filename: 'sunset-photo.jpg',
            original_name: '公司楼下夕阳.jpg',
            mime_type: 'image/jpeg',
            size: 512000,
            url: 'https://example.com/uploads/2024/01/sunset-photo.jpg',
            thumbnail_url: 'https://example.com/uploads/2024/01/thumbs/sunset-photo.jpg',
            width: 1920,
            height: 1080,
            uploader_id: 3,
            category: 'image',
            tags: JSON.stringify(['摄影', '夕阳', '生活', '风景']),
            description: '在公司楼下拍摄的美丽夕阳照片',
            is_public: true,
            created_at: new Date(Date.now() - 4 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 4 * 60 * 60 * 1000)
        },
        {
            id: 6,
            filename: 'docker-architecture.pdf',
            original_name: 'Docker 架构设计文档.pdf',
            mime_type: 'application/pdf',
            size: 1048576,
            url: 'https://example.com/uploads/2024/01/docker-architecture.pdf',
            uploader_id: 2,
            category: 'document',
            tags: JSON.stringify(['Docker', '架构', '文档', '容器化']),
            description: 'Docker 容器化架构设计详细文档',
            is_public: false,
            created_at: new Date(),
            updated_at: new Date()
        },
        {
            id: 7,
            filename: 'coding-tutorial.mp4',
            original_name: '编程教学视频.mp4',
            mime_type: 'video/mp4',
            size: 52428800,
            url: 'https://example.com/uploads/2024/01/coding-tutorial.mp4',
            thumbnail_url: 'https://example.com/uploads/2024/01/thumbs/coding-tutorial.jpg',
            uploader_id: 2,
            category: 'video',
            tags: JSON.stringify(['教程', '编程', '视频', '学习']),
            description: '编程基础教学视频，适合初学者',
            is_public: true,
            created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
        },
        {
            id: 8,
            filename: 'background-music.mp3',
            original_name: '编程背景音乐.mp3',
            mime_type: 'audio/mpeg',
            size: 5242880,
            url: 'https://example.com/uploads/2024/01/background-music.mp3',
            uploader_id: 3,
            category: 'audio',
            tags: JSON.stringify(['音乐', '背景音乐', '专注']),
            description: '适合编程时听的轻音乐',
            is_public: true,
            created_at: new Date(Date.now() - 6 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 6 * 60 * 60 * 1000)
        }
    ];
    await queryInterface.bulkInsert('media', media);
    const notifications = [
        {
            id: 1,
            type: 'interaction',
            title: '新的评论',
            content: 'jane_smith 评论了你的文章《React Hooks 完全指南：从入门到精通》',
            priority: 'medium',
            recipient_id: 2,
            sender_id: 3,
            related_type: 'article',
            related_id: 1,
            action_url: '/articles/1#comment-1',
            is_read: true,
            read_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000 + 60 * 60 * 1000),
            created_at: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000 + 60 * 60 * 1000)
        },
        {
            id: 2,
            type: 'interaction',
            title: '新的评论',
            content: 'mike_wilson 评论了你的文章《React Hooks 完全指南：从入门到精通》',
            priority: 'medium',
            recipient_id: 2,
            sender_id: 4,
            related_type: 'article',
            related_id: 1,
            action_url: '/articles/1#comment-2',
            is_read: true,
            read_at: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000 + 60 * 60 * 1000),
            created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000 + 60 * 60 * 1000)
        },
        {
            id: 3,
            type: 'interaction',
            title: '说说获得点赞',
            content: 'admin 点赞了你的说说',
            priority: 'low',
            recipient_id: 2,
            sender_id: 1,
            related_type: 'post',
            related_id: 1,
            action_url: '/posts/1',
            is_read: false,
            created_at: new Date(Date.now() - 5 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 5 * 60 * 60 * 1000)
        },
        {
            id: 4,
            type: 'interaction',
            title: '说说获得评论',
            content: 'jane_smith 评论了你的说说',
            priority: 'medium',
            recipient_id: 2,
            sender_id: 3,
            related_type: 'post',
            related_id: 1,
            action_url: '/posts/1#comment-6',
            is_read: false,
            created_at: new Date(Date.now() - 5 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 5 * 60 * 60 * 1000)
        },
        {
            id: 5,
            type: 'content',
            title: '新文章发布',
            content: 'john_doe 发布了新文章《TypeScript 高级类型系统详解》',
            priority: 'low',
            recipient_id: 3,
            sender_id: 2,
            related_type: 'article',
            related_id: 3,
            action_url: '/articles/3',
            is_read: true,
            read_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 60 * 60 * 1000),
            created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 60 * 60 * 1000)
        },
        {
            id: 6,
            type: 'system',
            title: '系统维护通知',
            content: '系统将于今晚 23:00-24:00 进行例行维护，期间可能影响部分功能使用',
            priority: 'high',
            recipient_id: 2,
            sender_id: 1,
            related_type: 'system',
            action_url: '/system/maintenance',
            is_read: false,
            created_at: new Date(Date.now() - 2 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 2 * 60 * 60 * 1000)
        },
        {
            id: 7,
            type: 'system',
            title: '系统维护通知',
            content: '系统将于今晚 23:00-24:00 进行例行维护，期间可能影响部分功能使用',
            priority: 'high',
            recipient_id: 3,
            sender_id: 1,
            related_type: 'system',
            action_url: '/system/maintenance',
            is_read: true,
            read_at: new Date(Date.now() - 1 * 60 * 60 * 1000),
            created_at: new Date(Date.now() - 2 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 1 * 60 * 60 * 1000)
        },
        {
            id: 8,
            type: 'system',
            title: '系统维护通知',
            content: '系统将于今晚 23:00-24:00 进行例行维护，期间可能影响部分功能使用',
            priority: 'high',
            recipient_id: 4,
            sender_id: 1,
            related_type: 'system',
            action_url: '/system/maintenance',
            is_read: false,
            created_at: new Date(Date.now() - 2 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 2 * 60 * 60 * 1000)
        }
    ];
    await queryInterface.bulkInsert('notifications', notifications);
    const notificationPreferences = [
        { user_id: 1, notification_type: 'interaction', channel: 'in_app', is_enabled: true, created_at: new Date(), updated_at: new Date() },
        { user_id: 1, notification_type: 'interaction', channel: 'email', is_enabled: true, created_at: new Date(), updated_at: new Date() },
        { user_id: 1, notification_type: 'content', channel: 'in_app', is_enabled: true, created_at: new Date(), updated_at: new Date() },
        { user_id: 1, notification_type: 'content', channel: 'email', is_enabled: false, created_at: new Date(), updated_at: new Date() },
        { user_id: 1, notification_type: 'system', channel: 'in_app', is_enabled: true, created_at: new Date(), updated_at: new Date() },
        { user_id: 1, notification_type: 'system', channel: 'email', is_enabled: true, created_at: new Date(), updated_at: new Date() },
        { user_id: 1, notification_type: 'marketing', channel: 'in_app', is_enabled: false, created_at: new Date(), updated_at: new Date() },
        { user_id: 1, notification_type: 'marketing', channel: 'email', is_enabled: false, created_at: new Date(), updated_at: new Date() },
        { user_id: 2, notification_type: 'interaction', channel: 'in_app', is_enabled: true, created_at: new Date(), updated_at: new Date() },
        { user_id: 2, notification_type: 'interaction', channel: 'email', is_enabled: true, created_at: new Date(), updated_at: new Date() },
        { user_id: 2, notification_type: 'content', channel: 'in_app', is_enabled: true, created_at: new Date(), updated_at: new Date() },
        { user_id: 2, notification_type: 'content', channel: 'email', is_enabled: true, created_at: new Date(), updated_at: new Date() },
        { user_id: 2, notification_type: 'system', channel: 'in_app', is_enabled: true, created_at: new Date(), updated_at: new Date() },
        { user_id: 2, notification_type: 'system', channel: 'email', is_enabled: false, created_at: new Date(), updated_at: new Date() },
        { user_id: 2, notification_type: 'marketing', channel: 'in_app', is_enabled: false, created_at: new Date(), updated_at: new Date() },
        { user_id: 2, notification_type: 'marketing', channel: 'email', is_enabled: false, created_at: new Date(), updated_at: new Date() },
        { user_id: 3, notification_type: 'interaction', channel: 'in_app', is_enabled: true, created_at: new Date(), updated_at: new Date() },
        { user_id: 3, notification_type: 'interaction', channel: 'email', is_enabled: false, created_at: new Date(), updated_at: new Date() },
        { user_id: 3, notification_type: 'content', channel: 'in_app', is_enabled: true, created_at: new Date(), updated_at: new Date() },
        { user_id: 3, notification_type: 'content', channel: 'email', is_enabled: false, created_at: new Date(), updated_at: new Date() },
        { user_id: 3, notification_type: 'system', channel: 'in_app', is_enabled: true, created_at: new Date(), updated_at: new Date() },
        { user_id: 3, notification_type: 'system', channel: 'email', is_enabled: true, created_at: new Date(), updated_at: new Date() },
        { user_id: 3, notification_type: 'marketing', channel: 'in_app', is_enabled: false, created_at: new Date(), updated_at: new Date() },
        { user_id: 3, notification_type: 'marketing', channel: 'email', is_enabled: false, created_at: new Date(), updated_at: new Date() },
        { user_id: 4, notification_type: 'interaction', channel: 'in_app', is_enabled: true, created_at: new Date(), updated_at: new Date() },
        { user_id: 4, notification_type: 'interaction', channel: 'email', is_enabled: true, created_at: new Date(), updated_at: new Date() },
        { user_id: 4, notification_type: 'content', channel: 'in_app', is_enabled: true, created_at: new Date(), updated_at: new Date() },
        { user_id: 4, notification_type: 'content', channel: 'email', is_enabled: true, created_at: new Date(), updated_at: new Date() },
        { user_id: 4, notification_type: 'system', channel: 'in_app', is_enabled: true, created_at: new Date(), updated_at: new Date() },
        { user_id: 4, notification_type: 'system', channel: 'email', is_enabled: true, created_at: new Date(), updated_at: new Date() },
        { user_id: 4, notification_type: 'marketing', channel: 'in_app', is_enabled: true, created_at: new Date(), updated_at: new Date() },
        { user_id: 4, notification_type: 'marketing', channel: 'email', is_enabled: false, created_at: new Date(), updated_at: new Date() }
    ];
    await queryInterface.bulkInsert('notification_preferences', notificationPreferences);
    console.log('✅ 媒体和通知种子数据创建完成');
    console.log(`   - 创建了 ${media.length} 个媒体文件`);
    console.log(`   - 创建了 ${notifications.length} 条通知`);
    console.log(`   - 创建了 ${notificationPreferences.length} 个通知偏好设置`);
}
async function down(queryInterface) {
    await queryInterface.bulkDelete('notification_preferences', {}, {});
    await queryInterface.bulkDelete('notifications', {}, {});
    await queryInterface.bulkDelete('media', {}, {});
}
//# sourceMappingURL=007-media-notifications.js.map