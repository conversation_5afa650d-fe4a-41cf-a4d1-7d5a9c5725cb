export declare class SeederRunner {
    private queryInterface;
    constructor();
    private ensureSeedersTable;
    private getExecutedSeeders;
    private recordSeeder;
    private removeSeederRecord;
    private getSeederFiles;
    private loadSeeder;
    seed(targetSeeder?: string): Promise<void>;
    unseed(steps?: number): Promise<void>;
    status(): Promise<void>;
    reset(): Promise<void>;
    clear(): Promise<void>;
}
//# sourceMappingURL=seeder-runner.d.ts.map