# 数据库迁移脚本

本目录包含了完整的数据库迁移脚本，用于创建和管理博客系统的数据库结构。

## 📋 概述

迁移脚本基于项目中的所有模型定义，包含以下功能模块：

### 🔐 用户管理系统
- **users**: 用户基本信息表
- **settings**: 用户个人设置表
- **user_roles**: 用户角色关联表

### 🛡️ 权限管理系统
- **roles**: 角色表
- **permissions**: 权限表
- **role_permissions**: 角色权限关联表

### 📝 内容管理系统
- **articles**: 文章表
- **categories**: 分类表（支持层级结构）
- **tags**: 标签表
- **article_tags**: 文章标签关联表
- **posts**: 说说动态表
- **post_likes**: 说说点赞表
- **comments**: 评论表（支持文章和说说评论）

### 📁 媒体管理系统
- **media**: 媒体文件表

### 🔔 通知系统
- **notifications**: 通知表
- **notification_preferences**: 通知偏好设置表

### 📊 系统管理
- **audit_logs**: 操作日志表

## 🚀 使用方法

### 1. 执行所有迁移
```bash
# 使用迁移运行器
npm run ts-node src/database/migrations/migration-runner.ts up

# 或使用简化脚本
npm run ts-node src/scripts/run-migrations.ts
```

### 2. 查看迁移状态
```bash
npm run ts-node src/database/migrations/migration-runner.ts status
```

### 3. 回滚迁移
```bash
# 回滚最近1个迁移
npm run ts-node src/database/migrations/migration-runner.ts down

# 回滚最近3个迁移
npm run ts-node src/database/migrations/migration-runner.ts down 3
```

### 4. 重置数据库
```bash
npm run ts-node src/database/migrations/migration-runner.ts reset
```

## 📁 文件结构

```
backend/src/database/migrations/
├── README.md                    # 本文档
├── 001-create-all-tables.ts     # 主迁移文件
├── migration-runner.ts          # 迁移运行器
└── ...                          # 其他迁移文件
```

## 🔧 迁移详情

### 表创建顺序
迁移脚本按照正确的依赖顺序创建表：

1. **基础表**（无外键依赖）
   - users, roles, permissions, categories, tags

2. **依赖表**（有外键依赖）
   - settings, articles, posts, comments, media, notifications, audit_logs

3. **关联表**（多对多关系）
   - user_roles, role_permissions, article_tags, post_likes, notification_preferences

### 索引策略
为提高查询性能，脚本会创建以下索引：

- **唯一索引**: 确保数据唯一性（如用户名、邮箱、slug等）
- **外键索引**: 提高关联查询性能
- **查询索引**: 针对常用查询字段（如状态、时间等）
- **复合索引**: 针对多字段查询优化

### 数据类型说明

- **字符串字段**: 根据实际需求设置合适长度
- **枚举字段**: 使用 ENUM 类型确保数据一致性
- **JSON字段**: 用于存储结构化数据（如图片数组、标签等）
- **时间字段**: 统一使用 DATETIME 类型
- **布尔字段**: 使用 BOOLEAN 类型，设置合理默认值

## ⚠️ 注意事项

### 数据库兼容性
- 脚本基于 Sequelize ORM 编写
- 支持 MySQL、PostgreSQL、SQLite 等主流数据库
- 使用标准 SQL 语法，确保跨数据库兼容性

### 外键约束
- 所有外键都设置了适当的 CASCADE 和 SET NULL 策略
- 删除操作会自动处理相关数据，避免孤立记录

### 性能考虑
- 为高频查询字段创建了索引
- 使用合适的数据类型减少存储空间
- 避免过度索引影响写入性能

### 安全性
- 敏感字段（如密码）使用哈希存储
- 用户输入字段设置了长度限制
- 枚举字段限制了可选值范围

## 🔄 迁移管理

### 迁移记录
系统会自动创建 `migrations` 表来跟踪已执行的迁移：
- 记录迁移名称和执行时间
- 防止重复执行同一迁移
- 支持迁移回滚操作

### 版本控制
- 迁移文件使用数字前缀排序
- 每个迁移都包含 up 和 down 方法
- 支持增量迁移和完整回滚

### 错误处理
- 迁移过程中的错误会被捕获和记录
- 失败的迁移不会被标记为已执行
- 支持从失败点继续执行

## 🛠️ 开发指南

### 添加新迁移
1. 在 migrations 目录创建新文件
2. 使用递增的数字前缀命名
3. 实现 up 和 down 方法
4. 测试迁移的正向和反向操作

### 修改现有表结构
1. 创建新的迁移文件
2. 使用 ALTER TABLE 语句修改结构
3. 确保提供回滚方案
4. 考虑数据迁移需求

### 最佳实践
- 每个迁移只做一件事
- 提供清晰的迁移描述
- 测试迁移在不同环境下的表现
- 备份重要数据后再执行迁移

## 📞 支持

如果在使用迁移脚本过程中遇到问题，请：

1. 检查数据库连接配置
2. 确认数据库用户权限
3. 查看迁移执行日志
4. 参考错误信息进行排查

更多技术支持请参考项目文档或联系开发团队。
