"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.createTable('notification_preferences', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false,
            comment: '偏好ID'
        },
        user_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            unique: true,
            comment: '用户ID',
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        email_notifications: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: true,
            comment: '是否接收邮件通知'
        },
        comment_notifications: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: true,
            comment: '是否接收评论通知'
        },
        like_notifications: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: true,
            comment: '是否接收点赞通知'
        },
        follow_notifications: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: true,
            comment: '是否接收关注通知'
        },
        system_notifications: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: true,
            comment: '是否接收系统通知'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '创建时间'
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '更新时间'
        }
    }, {
        charset: 'utf8mb4',
        collate: 'utf8mb4_unicode_ci',
        comment: '通知偏好表'
    });
    await queryInterface.addIndex('notification_preferences', ['user_id'], {
        name: 'idx_notification_preferences_user_id',
        unique: true
    });
    console.log('✅ Created notification_preferences table with indexes');
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.dropTable('notification_preferences');
    console.log('✅ Dropped notification_preferences table');
};
exports.down = down;
//# sourceMappingURL=011-create-notification-preferences.js.map