import api, { createApiRequest, handleApiError } from './api'
import type {
  Category,
  CategoriesResponse,
  CategoryResponse,
  CategoryArticlesResponse,
  CategoryStatsResponse,
  CategoryParams,
  CategoryArticleParams,
  CreateCategoryData,
  UpdateCategoryData,
  BatchDeleteResponse
} from '@/types/category'

/**
 * 分类服务模块，提供分类相关的API接口封装
 */
export const categoryService = {
  /**
   * 获取分类列表
   * @param params 查询参数，包括树结构、扁平列表、统计信息等选项
   * @returns 返回包含分类列表的Promise对象
   */
  async getCategories(params: CategoryParams = {}): Promise<Category[]> {
    try {
      const response = await createApiRequest(
        () => api.get('/categories', { params }),
        '正在加载分类列表...'
      )
      return (response as CategoriesResponse).data?.categories || []
    } catch (error) {
      console.error('获取分类列表失败:', error)
      return []
    }
  },

  /**
   * 获取单个分类详情
   * @param id 分类ID或slug
   * @returns 返回指定分类的详细信息
   */
  async getCategory(id: string | number): Promise<Category> {
    try {
      const response = await createApiRequest(
        () => api.get(`/categories/${id}`),
        '正在加载分类信息...'
      )
      return (response as CategoryResponse).data.category
    } catch (error) {
      handleApiError(error, '获取分类详情失败')
    }
  },

  /**
   * 获取分类下的文章列表
   * @param id 分类ID
   * @param params 查询参数，包括分页、状态筛选等
   * @returns 返回包含文章列表和分页信息的Promise对象
   */
  async getCategoryArticles(id: number, params: CategoryArticleParams = {}): Promise<CategoryArticlesResponse['data']> {
    try {
      const response = await createApiRequest(
        () => api.get(`/categories/${id}/articles`, { params }),
        '正在加载分类文章...'
      )
      return (response as CategoryArticlesResponse).data
    } catch (error) {
      handleApiError(error, '获取分类文章失败')
    }
  },

  /**
   * 获取分类统计信息
   * @param includeChildren 是否包含子分类的文章数量
   * @returns 返回包含统计信息的分类列表
   */
  async getCategoryStats(includeChildren = true): Promise<Array<{ category: Category; articleCount: number }>> {
    try {
      const response = await createApiRequest(
        () => api.get('/categories/stats', {
          params: { includeChildren }
        }),
        '正在加载分类统计...'
      )
      return (response as CategoryStatsResponse).data?.stats || []
    } catch (error) {
      console.error('获取分类统计失败:', error)
      return []
    }
  },

  /**
   * 创建新分类
   * @param categoryData 分类数据对象，包含分类的基本信息
   * @returns 返回创建成功的分类对象
   */
  async createCategory(categoryData: CreateCategoryData): Promise<Category> {
    try {
      const response = await createApiRequest(
        () => api.post('/categories', categoryData),
        '正在创建分类...'
      )
      return (response as CategoryResponse).data.category
    } catch (error) {
      handleApiError(error, '创建分类失败')
    }
  },

  /**
   * 更新分类信息
   * @param id 要更新的分类ID
   * @param categoryData 更新的分类数据
   * @returns 返回更新后的分类对象
   */
  async updateCategory(id: number, categoryData: UpdateCategoryData): Promise<Category> {
    try {
      const response = await createApiRequest(
        () => api.put(`/categories/${id}`, categoryData),
        '正在更新分类...'
      )
      return (response as CategoryResponse).data.category
    } catch (error) {
      handleApiError(error, '更新分类失败')
    }
  },

  /**
   * 删除指定分类
   * @param id 要删除的分类ID
   */
  async deleteCategory(id: number): Promise<void> {
    try {
      await createApiRequest(
        () => api.delete(`/categories/${id}`),
        '正在删除分类...'
      )
    } catch (error) {
      handleApiError(error, '删除分类失败')
    }
  },

  /**
   * 批量删除分类
   * @param ids 要删除的分类ID数组
   * @returns 返回删除结果信息
   */
  async deleteCategories(ids: number[]): Promise<BatchDeleteResponse['data']> {
    try {
      const response = await createApiRequest(
        () => api.delete('/categories/batch', { data: { ids } }),
        '正在批量删除分类...'
      )
      return (response as BatchDeleteResponse).data
    } catch (error) {
      handleApiError(error, '批量删除分类失败')
    }
  },

  /**
   * 获取分类树结构
   * @returns 返回层次化的分类树
   */
  async getCategoryTree(): Promise<Category[]> {
    return this.getCategories({ tree: true })
  },

  /**
   * 获取扁平化的分类列表（包含层级信息）
   * @returns 返回包含层级信息的扁平分类列表
   */
  async getCategoryFlatList(): Promise<Category[]> {
    return this.getCategories({ flat: true })
  },

  /**
   * 获取热门分类列表（按文章数量排序）
   * @param limit 限制返回的分类数量，默认为10
   * @returns 返回热门分类数组
   */
  async getPopularCategories(limit = 10): Promise<Category[]> {
    try {
      const stats = await this.getCategoryStats(true)
      return stats
        .filter(item => item.articleCount > 0)
        .sort((a, b) => b.articleCount - a.articleCount)
        .slice(0, limit)
        .map(item => ({
          ...item.category,
          articleCount: item.articleCount
        }))
    } catch (error) {
      console.error('获取热门分类失败:', error)
      return []
    }
  }
}
