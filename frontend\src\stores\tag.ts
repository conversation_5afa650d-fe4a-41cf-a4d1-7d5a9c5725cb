import { defineStore } from 'pinia'
import { ref } from 'vue'
import { tagService } from '@/services/tag'
import type { Tag } from '@/types/tag'

/**
 * 标签状态管理 store
 * 使用 Pinia 管理标签相关的状态，包括标签列表、加载状态和错误信息
 */
export const useTagStore = defineStore('tag', () => {
  // 标签列表
  const tags = ref<Tag[]>([])
  // 加载状态
  const loading = ref(false)
  // 错误信息
  const error = ref<string | null>(null)

  /**
   * 获取所有标签列表
   * @returns Promise<Tag[]> 返回标签列表的 Promise
   */
  const fetchTags = async () => {
    loading.value = true
    error.value = null

    try {
      const response = await tagService.getTags()
      tags.value = response
      return response
    } catch (err: any) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 创建新标签
   * @param name 标签名称
   * @param slug 标签slug（可选，如不提供则自动生成）
   * @returns Promise<Tag> 返回创建成功的标签对象的 Promise
   */
  const createTag = async (name: string, slug?: string) => {
    loading.value = true
    error.value = null

    try {
      const newTag = await tagService.createTag(name, slug)
      tags.value.push(newTag)
      return newTag
    } catch (err: any) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 更新指定标签
   * @param id 要更新的标签 ID
   * @param name 新的标签名称
   * @param slug 标签slug（可选，如不提供则自动生成）
   * @returns Promise<Tag> 更新后的标签对象
   */
  const updateTag = async (id: number, name: string, slug?: string) => {
    loading.value = true
    error.value = null

    try {
      const updatedTag = await tagService.updateTag(id, name, slug)
      const index = tags.value.findIndex(tag => tag.id === id)
      if (index !== -1) {
        tags.value[index] = updatedTag
      }
      return updatedTag
    } catch (err: any) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 删除指定标签
   * @param id 要删除的标签 ID
   * @returns Promise<void> 删除操作完成的 Promise
   */
  const deleteTag = async (id: number) => {
    loading.value = true
    error.value = null

    try {
      await tagService.deleteTag(id)
      tags.value = tags.value.filter(tag => tag.id !== id)
    } catch (err: any) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 批量删除标签
   * @param tagIds 要删除的标签 ID 数组
   * @returns Promise<{deletedCount: number, requestedCount: number}> 删除结果
   */
  const batchDeleteTags = async (tagIds: number[]) => {
    loading.value = true
    error.value = null

    try {
      const result = await tagService.batchDeleteTags(tagIds)
      // 从本地状态中移除已删除的标签
      tags.value = tags.value.filter(tag => !tagIds.includes(tag.id))
      return result
    } catch (err: any) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取标签统计信息
   * @returns Promise<{stats: any, tags: Tag[]}> 统计信息和标签列表
   */
  const getTagStats = async () => {
    loading.value = true
    error.value = null

    try {
      const result = await tagService.getTagStats()
      // 更新标签列表为带统计信息的版本
      tags.value = result.tags
      return result
    } catch (err: any) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 根据 slug 查找标签
   * @param slug 标签的唯一标识符
   * @returns Tag | undefined 返回匹配的标签对象或 undefined
   */
  const getTagBySlug = (slug: string) => {
    return tags.value.find(tag => tag.slug === slug)
  }

  /**
   * 获取热门标签列表
   * @param limit 限制返回的标签数量，默认为 10
   * @returns Promise<Tag[]> 返回热门标签列表的 Promise
   */
  const getPopularTags = async (limit = 10) => {
    loading.value = true
    error.value = null

    try {
      const popularTags = await tagService.getPopularTags(limit)
      return popularTags
    } catch (err: any) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    tags,
    loading,
    error,
    fetchTags,
    createTag,
    updateTag,
    deleteTag,
    batchDeleteTags,
    getTagBySlug,
    getPopularTags,
    getTagStats
  }
})