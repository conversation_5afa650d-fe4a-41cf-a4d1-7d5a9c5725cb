"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.up = up;
exports.down = down;
const sequelize_1 = require("sequelize");
async function up(queryInterface) {
    await queryInterface.createTable('users', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        username: {
            type: sequelize_1.DataTypes.STRING(50),
            allowNull: false,
            unique: true
        },
        email: {
            type: sequelize_1.DataTypes.STRING(100),
            allowNull: false,
            unique: true
        },
        password_hash: {
            type: sequelize_1.DataTypes.STRING(255),
            allowNull: false
        },
        is_active: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: true
        },
        email_verified: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false
        },
        email_verified_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: true
        },
        last_login_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: true
        },
        password_reset_token: {
            type: sequelize_1.DataTypes.STRING(255),
            allowNull: true
        },
        password_reset_expires: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: true
        },
        email_verification_token: {
            type: sequelize_1.DataTypes.STRING(255),
            allowNull: true
        },
        email_verification_expires: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: true
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        }
    });
    await queryInterface.createTable('roles', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        name: {
            type: sequelize_1.DataTypes.STRING(50),
            allowNull: false,
            unique: true
        },
        description: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true
        },
        is_active: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: true
        },
        is_system: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        }
    });
    await queryInterface.createTable('permissions', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        name: {
            type: sequelize_1.DataTypes.STRING(100),
            allowNull: false,
            unique: true
        },
        description: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true
        },
        resource: {
            type: sequelize_1.DataTypes.STRING(50),
            allowNull: false
        },
        action: {
            type: sequelize_1.DataTypes.STRING(50),
            allowNull: false
        },
        is_active: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: true
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        }
    });
    await queryInterface.createTable('categories', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        name: {
            type: sequelize_1.DataTypes.STRING(100),
            allowNull: false
        },
        slug: {
            type: sequelize_1.DataTypes.STRING(100),
            allowNull: false,
            unique: true
        },
        description: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true
        },
        parent_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: 'categories',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'SET NULL'
        },
        sort: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            defaultValue: 0
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        }
    });
    await queryInterface.createTable('tags', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        name: {
            type: sequelize_1.DataTypes.STRING(50),
            allowNull: false,
            unique: true
        },
        slug: {
            type: sequelize_1.DataTypes.STRING(50),
            allowNull: false,
            unique: true
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        }
    });
    await queryInterface.createTable('settings', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        user_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            unique: true,
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        display_name: {
            type: sequelize_1.DataTypes.STRING(100),
            allowNull: true
        },
        avatar: {
            type: sequelize_1.DataTypes.STRING(255),
            allowNull: true
        },
        bio: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true
        },
        website: {
            type: sequelize_1.DataTypes.STRING(255),
            allowNull: true
        },
        location: {
            type: sequelize_1.DataTypes.STRING(100),
            allowNull: true
        },
        theme: {
            type: sequelize_1.DataTypes.ENUM('light', 'dark', 'auto'),
            allowNull: false,
            defaultValue: 'auto'
        },
        language: {
            type: sequelize_1.DataTypes.STRING(10),
            allowNull: false,
            defaultValue: 'zh-CN'
        },
        timezone: {
            type: sequelize_1.DataTypes.STRING(50),
            allowNull: false,
            defaultValue: 'Asia/Shanghai'
        },
        items_per_page: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            defaultValue: 20
        },
        email_notifications: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: true
        },
        comment_notifications: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: true
        },
        system_notifications: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: true
        },
        profile_visibility: {
            type: sequelize_1.DataTypes.ENUM('public', 'private'),
            allowNull: false,
            defaultValue: 'public'
        },
        default_post_visibility: {
            type: sequelize_1.DataTypes.ENUM('public', 'private'),
            allowNull: false,
            defaultValue: 'public'
        },
        show_email: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false
        },
        two_factor_enabled: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        }
    });
    await queryInterface.createTable('articles', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        title: {
            type: sequelize_1.DataTypes.STRING(200),
            allowNull: false
        },
        slug: {
            type: sequelize_1.DataTypes.STRING(200),
            allowNull: false,
            unique: true
        },
        content: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: false
        },
        excerpt: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true
        },
        status: {
            type: sequelize_1.DataTypes.ENUM('draft', 'published'),
            allowNull: false,
            defaultValue: 'draft'
        },
        author_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        category_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: 'categories',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'SET NULL'
        },
        published_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: true
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        }
    });
    await queryInterface.createTable('posts', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        content: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: false
        },
        images: {
            type: sequelize_1.DataTypes.JSON,
            allowNull: true
        },
        visibility: {
            type: sequelize_1.DataTypes.ENUM('public', 'private'),
            allowNull: false,
            defaultValue: 'public'
        },
        location: {
            type: sequelize_1.DataTypes.STRING(100),
            allowNull: true
        },
        author_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        }
    });
    await queryInterface.createTable('comments', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        content: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: false
        },
        status: {
            type: sequelize_1.DataTypes.ENUM('pending', 'approved', 'rejected'),
            allowNull: false,
            defaultValue: 'pending'
        },
        article_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: 'articles',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        post_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: 'posts',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        author_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        parent_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: 'comments',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        }
    });
    await queryInterface.createTable('media', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        filename: {
            type: sequelize_1.DataTypes.STRING(255),
            allowNull: false
        },
        original_name: {
            type: sequelize_1.DataTypes.STRING(255),
            allowNull: false
        },
        mime_type: {
            type: sequelize_1.DataTypes.STRING(100),
            allowNull: false
        },
        size: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false
        },
        url: {
            type: sequelize_1.DataTypes.STRING(500),
            allowNull: false
        },
        thumbnail_url: {
            type: sequelize_1.DataTypes.STRING(500),
            allowNull: true
        },
        width: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true
        },
        height: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true
        },
        uploader_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        category: {
            type: sequelize_1.DataTypes.ENUM('image', 'video', 'audio', 'document'),
            allowNull: false
        },
        tags: {
            type: sequelize_1.DataTypes.JSON,
            allowNull: true
        },
        description: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true
        },
        is_public: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: true
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        }
    });
    await queryInterface.createTable('notifications', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        type: {
            type: sequelize_1.DataTypes.ENUM('interaction', 'content', 'system', 'marketing'),
            allowNull: false
        },
        title: {
            type: sequelize_1.DataTypes.STRING(200),
            allowNull: false
        },
        content: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true
        },
        priority: {
            type: sequelize_1.DataTypes.ENUM('high', 'medium', 'low'),
            allowNull: false,
            defaultValue: 'medium'
        },
        recipient_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        sender_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'SET NULL'
        },
        related_type: {
            type: sequelize_1.DataTypes.ENUM('article', 'post', 'comment', 'user', 'system'),
            allowNull: true
        },
        related_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true
        },
        action_url: {
            type: sequelize_1.DataTypes.STRING(500),
            allowNull: true
        },
        is_read: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false
        },
        read_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: true
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        }
    });
    await queryInterface.createTable('audit_logs', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        user_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'SET NULL'
        },
        action: {
            type: sequelize_1.DataTypes.STRING(100),
            allowNull: false
        },
        resource: {
            type: sequelize_1.DataTypes.STRING(100),
            allowNull: false
        },
        resource_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true
        },
        old_data: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true
        },
        new_data: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true
        },
        ip_address: {
            type: sequelize_1.DataTypes.STRING(45),
            allowNull: true
        },
        user_agent: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true
        },
        session_id: {
            type: sequelize_1.DataTypes.STRING(255),
            allowNull: true
        },
        status: {
            type: sequelize_1.DataTypes.ENUM('success', 'failed', 'pending'),
            allowNull: false,
            defaultValue: 'success'
        },
        error_message: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true
        },
        duration: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        }
    });
    await queryInterface.createTable('user_roles', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        user_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        role_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'roles',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        assigned_by: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'SET NULL'
        },
        assigned_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        }
    });
    await queryInterface.createTable('role_permissions', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        role_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'roles',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        permission_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'permissions',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        assigned_by: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'SET NULL'
        },
        assigned_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        }
    });
    await queryInterface.createTable('article_tags', {
        article_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'articles',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        tag_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'tags',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        }
    });
    await queryInterface.createTable('post_likes', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        post_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'posts',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        user_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        }
    });
    await queryInterface.createTable('notification_preferences', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false
        },
        user_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        notification_type: {
            type: sequelize_1.DataTypes.ENUM('interaction', 'content', 'system', 'marketing'),
            allowNull: false
        },
        channel: {
            type: sequelize_1.DataTypes.ENUM('in_app', 'email', 'push'),
            allowNull: false,
            defaultValue: 'in_app'
        },
        is_enabled: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: true
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW
        }
    });
    await queryInterface.addIndex('users', ['email']);
    await queryInterface.addIndex('users', ['username']);
    await queryInterface.addIndex('users', ['is_active']);
    await queryInterface.addIndex('users', ['email_verified']);
    await queryInterface.addIndex('roles', ['name']);
    await queryInterface.addIndex('roles', ['is_active']);
    await queryInterface.addIndex('roles', ['is_system']);
    await queryInterface.addIndex('permissions', ['name']);
    await queryInterface.addIndex('permissions', ['resource']);
    await queryInterface.addIndex('permissions', ['action']);
    await queryInterface.addIndex('permissions', ['resource', 'action']);
    await queryInterface.addIndex('permissions', ['is_active']);
    await queryInterface.addIndex('categories', ['slug']);
    await queryInterface.addIndex('categories', ['parent_id']);
    await queryInterface.addIndex('categories', ['sort']);
    await queryInterface.addIndex('tags', ['name']);
    await queryInterface.addIndex('tags', ['slug']);
    await queryInterface.addIndex('articles', ['slug']);
    await queryInterface.addIndex('articles', ['author_id']);
    await queryInterface.addIndex('articles', ['category_id']);
    await queryInterface.addIndex('articles', ['status']);
    await queryInterface.addIndex('articles', ['published_at']);
    await queryInterface.addIndex('articles', ['created_at']);
    await queryInterface.addIndex('posts', ['author_id']);
    await queryInterface.addIndex('posts', ['visibility']);
    await queryInterface.addIndex('posts', ['created_at']);
    await queryInterface.addIndex('comments', ['article_id']);
    await queryInterface.addIndex('comments', ['post_id']);
    await queryInterface.addIndex('comments', ['author_id']);
    await queryInterface.addIndex('comments', ['parent_id']);
    await queryInterface.addIndex('comments', ['status']);
    await queryInterface.addIndex('comments', ['created_at']);
    await queryInterface.addIndex('media', ['uploader_id']);
    await queryInterface.addIndex('media', ['category']);
    await queryInterface.addIndex('media', ['is_public']);
    await queryInterface.addIndex('media', ['created_at']);
    await queryInterface.addIndex('notifications', ['recipient_id']);
    await queryInterface.addIndex('notifications', ['sender_id']);
    await queryInterface.addIndex('notifications', ['type']);
    await queryInterface.addIndex('notifications', ['priority']);
    await queryInterface.addIndex('notifications', ['is_read']);
    await queryInterface.addIndex('notifications', ['created_at']);
    await queryInterface.addIndex('audit_logs', ['user_id']);
    await queryInterface.addIndex('audit_logs', ['action']);
    await queryInterface.addIndex('audit_logs', ['resource']);
    await queryInterface.addIndex('audit_logs', ['resource_id']);
    await queryInterface.addIndex('audit_logs', ['status']);
    await queryInterface.addIndex('audit_logs', ['created_at']);
    await queryInterface.addIndex('user_roles', ['user_id', 'role_id'], { unique: true });
    await queryInterface.addIndex('user_roles', ['user_id']);
    await queryInterface.addIndex('user_roles', ['role_id']);
    await queryInterface.addIndex('user_roles', ['assigned_by']);
    await queryInterface.addIndex('role_permissions', ['role_id', 'permission_id'], { unique: true });
    await queryInterface.addIndex('role_permissions', ['role_id']);
    await queryInterface.addIndex('role_permissions', ['permission_id']);
    await queryInterface.addIndex('role_permissions', ['assigned_by']);
    await queryInterface.addIndex('article_tags', ['article_id', 'tag_id'], { unique: true });
    await queryInterface.addIndex('article_tags', ['article_id']);
    await queryInterface.addIndex('article_tags', ['tag_id']);
    await queryInterface.addIndex('post_likes', ['post_id', 'user_id'], { unique: true });
    await queryInterface.addIndex('post_likes', ['post_id']);
    await queryInterface.addIndex('post_likes', ['user_id']);
    await queryInterface.addIndex('post_likes', ['created_at']);
    await queryInterface.addIndex('notification_preferences', ['user_id', 'notification_type', 'channel'], { unique: true });
    await queryInterface.addIndex('notification_preferences', ['user_id', 'is_enabled']);
    await queryInterface.addIndex('notification_preferences', ['notification_type', 'channel', 'is_enabled']);
    console.log('✅ 所有表和索引创建完成');
}
async function down(queryInterface) {
    console.log('🗑️ 开始删除所有表...');
    const tables = [
        'notification_preferences',
        'post_likes',
        'article_tags',
        'role_permissions',
        'user_roles',
        'audit_logs',
        'notifications',
        'media',
        'comments',
        'posts',
        'articles',
        'settings',
        'tags',
        'categories',
        'permissions',
        'roles',
        'users'
    ];
    for (const table of tables) {
        try {
            await queryInterface.dropTable(table);
            console.log(`✅ 删除表 ${table}`);
        }
        catch (error) {
            console.log(`⚠️ 删除表 ${table} 失败:`, error);
        }
    }
    console.log('✅ 所有表删除完成');
}
//# sourceMappingURL=001-create-all-tables.js.map