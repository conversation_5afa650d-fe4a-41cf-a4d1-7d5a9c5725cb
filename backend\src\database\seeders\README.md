# 数据库种子数据

本目录包含了完整的数据库种子数据脚本，用于为博客系统创建初始数据。

## 📋 概述

种子数据系统基于项目中的所有模型、控制器、路由分析设计，包含以下数据模块：

### 🔐 用户管理数据
- **用户账户**: 管理员、编辑者、普通用户
- **用户设置**: 个人资料、主题偏好、通知设置
- **用户角色**: 角色分配和权限管理

### 🛡️ 权限系统数据
- **角色定义**: 超级管理员、管理员、编辑者、普通用户
- **权限定义**: 53个细粒度权限，覆盖所有功能模块
- **角色权限关联**: 基于最小权限原则的权限分配

### 📝 内容管理数据
- **分类结构**: 3级层级分类，包含技术、生活、学习等主题
- **标签系统**: 55个标签，涵盖技术、方法论、学习、生活等领域
- **文章内容**: 5篇高质量示例文章，包含技术教程和经验分享
- **文章标签关联**: 合理的标签分配

### 🗨️ 社交功能数据
- **说说动态**: 8条真实的开发者日常分享
- **点赞互动**: 19个点赞记录，模拟用户互动
- **评论系统**: 13条评论，包含文章评论和说说评论

### 📁 媒体管理数据
- **多媒体文件**: 8个文件，包含图片、视频、音频、文档
- **文件分类**: 按类型和用途分类
- **权限控制**: 公开和私有文件权限设置

### 🔔 通知系统数据
- **通知消息**: 8条通知，包含互动、内容、系统通知
- **通知偏好**: 32个偏好设置，覆盖所有用户和通知类型

### 📊 系统管理数据
- **操作日志**: 15条审计日志，记录各种系统操作

## 🚀 使用方法

### 快速开始
```bash
# 执行所有种子数据
npm run seed:run

# 查看种子数据状态
npm run seed:status

# 测试种子数据完整性
npm run seed:test
```

### 高级操作
```bash
# 执行种子数据
npm run seed

# 回滚种子数据
npm run seed:unseed

# 重置种子数据
npm run seed:reset

# 清空种子数据
npm run seed:clear

# 测试回滚功能
npm run seed:test:rollback
```

## 📁 文件结构

```
backend/src/database/seeders/
├── README.md                           # 本文档
├── 001-users.ts                        # 用户和设置数据
├── 002-roles-permissions.ts            # 角色权限定义
├── 003-role-permissions-assignments.ts # 角色权限分配
├── 004-categories-tags.ts              # 分类标签数据
├── 005-articles.ts                     # 文章和标签关联
├── 006-posts-comments.ts               # 说说评论数据
├── 007-media-notifications.ts          # 媒体通知数据
├── 008-audit-logs.ts                   # 审计日志数据
├── seeder-runner.ts                    # 种子数据运行器
└── ...                                 # 其他种子数据文件
```

## 🔧 种子数据详情

### 执行顺序
种子数据按照正确的依赖顺序执行：

1. **001-users**: 创建用户和用户设置
2. **002-roles-permissions**: 创建角色和权限定义
3. **003-role-permissions-assignments**: 分配角色权限和用户角色
4. **004-categories-tags**: 创建分类和标签
5. **005-articles**: 创建文章和文章标签关联
6. **006-posts-comments**: 创建说说、点赞和评论
7. **007-media-notifications**: 创建媒体文件和通知
8. **008-audit-logs**: 创建审计日志

### 数据量统计
- **用户**: 4个用户（1个管理员，3个普通用户）
- **角色**: 4个角色，53个权限
- **分类**: 14个分类（3级层级结构）
- **标签**: 55个标签
- **文章**: 5篇文章，25个标签关联
- **说说**: 8条说说，19个点赞
- **评论**: 13条评论
- **媒体**: 8个文件
- **通知**: 8条通知，32个偏好设置
- **日志**: 15条审计日志

### 用户账户信息
| 用户名 | 邮箱 | 密码 | 角色 | 状态 |
|--------|------|------|------|------|
| admin | <EMAIL> | admin123 | 超级管理员 | 已验证 |
| john_doe | <EMAIL> | password123 | 编辑者 | 已验证 |
| jane_smith | <EMAIL> | password123 | 普通用户 | 已验证 |
| mike_wilson | <EMAIL> | password123 | 普通用户 | 未验证 |

## 🔄 种子数据管理

### 自动化特性
- 🔄 自动跟踪已执行种子数据
- 🔄 防止重复执行
- 🔄 支持增量执行
- 🔄 完整的回滚支持

### 错误处理
- ⚠️ 详细的错误日志
- ⚠️ 失败种子数据不标记为完成
- ⚠️ 支持从失败点继续
- ⚠️ 数据库连接验证

### 测试验证
- ✅ 数据完整性验证
- ✅ 关联关系验证
- ✅ 数据质量验证
- ✅ 统计信息显示
- ✅ 回滚功能测试

## ⚠️ 注意事项

### 数据安全
- 种子数据仅用于开发和测试环境
- 生产环境请谨慎使用，避免覆盖真实数据
- 建议在执行前备份现有数据

### 性能考虑
- 种子数据执行可能需要几分钟时间
- 大量数据插入时请确保数据库性能
- 建议在低峰期执行

### 依赖关系
- 确保数据库迁移已完成
- 确保所有表结构已创建
- 确保数据库连接配置正确

## 🛠️ 开发指南

### 添加新种子数据
1. 在 seeders 目录创建新文件
2. 使用递增的数字前缀命名
3. 实现 up 和 down 方法
4. 测试种子数据的正向和反向操作

### 修改现有种子数据
1. 创建新的种子数据文件
2. 使用 INSERT、UPDATE 或 DELETE 语句
3. 确保提供回滚方案
4. 考虑数据依赖关系

### 最佳实践
- 每个种子数据文件只负责一个功能模块
- 提供清晰的种子数据描述
- 测试种子数据在不同环境下的表现
- 确保种子数据的幂等性

## 📞 支持

如果在使用种子数据过程中遇到问题，请：

1. 检查数据库连接配置
2. 确认数据库用户权限
3. 查看种子数据执行日志
4. 运行测试脚本验证数据完整性

更多技术支持请参考项目文档或联系开发团队。

## 🎯 与业务逻辑的对应关系

### 控制器功能覆盖
种子数据完全覆盖了所有控制器的功能需求：

- **AuthController**: 用户认证、角色权限
- **ArticleController**: 文章管理、分类标签
- **PostController**: 说说管理、点赞功能
- **CommentController**: 评论管理、审核流程
- **MediaController**: 文件上传、权限控制
- **NotificationController**: 通知推送、偏好设置
- **UserController**: 用户管理、设置配置

### 路由接口支持
种子数据为所有API接口提供了测试数据：

- **GET /api/articles**: 返回5篇示例文章
- **GET /api/posts**: 返回8条说说动态
- **GET /api/comments**: 返回13条评论
- **GET /api/users**: 返回4个用户账户
- **GET /api/notifications**: 返回8条通知消息

### 业务场景模拟
种子数据模拟了真实的业务场景：

- **内容创作**: 技术文章、学习笔记、经验分享
- **社交互动**: 点赞、评论、回复
- **权限管理**: 角色分配、权限控制
- **系统管理**: 操作日志、审计追踪

这套种子数据系统为博客系统提供了完整的初始数据，支持所有功能模块的开发和测试。
