/**
 * 通知系统相关的TypeScript类型定义
 */

/**
 * 通知类型枚举
 */
export type NotificationType = 'interaction' | 'content' | 'system' | 'marketing'

/**
 * 通知优先级枚举
 */
export type NotificationPriority = 'high' | 'medium' | 'low'

/**
 * 通知渠道枚举
 */
export type NotificationChannel = 'in_app' | 'email' | 'push'

/**
 * 关联对象类型枚举
 */
export type RelatedType = 'article' | 'post' | 'comment' | 'user' | 'system'

/**
 * 通知发送者信息接口
 */
export interface NotificationSender {
  id: number
  username: string
}

/**
 * 通知基础接口
 */
export interface Notification {
  id: number
  type: NotificationType
  title: string
  content?: string
  priority: NotificationPriority
  recipientId: number
  senderId?: number
  sender?: NotificationSender
  relatedType?: RelatedType
  relatedId?: number
  actionUrl?: string
  isRead: boolean
  readAt?: string
  createdAt: string
  updatedAt: string
}

/**
 * 创建通知的请求接口
 */
export interface CreateNotificationRequest {
  type: NotificationType
  title: string
  content?: string
  priority?: NotificationPriority
  recipientId: number
  senderId?: number
  relatedType?: RelatedType
  relatedId?: number
  actionUrl?: string
}

/**
 * 通知列表查询参数接口
 */
export interface NotificationListParams {
  page?: number
  limit?: number
  type?: NotificationType
  is_read?: boolean
  priority?: NotificationPriority
}

/**
 * 通知列表响应接口
 */
export interface NotificationListResponse {
  notifications: Notification[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

/**
 * 未读通知数量响应接口
 */
export interface UnreadCountResponse {
  count: number
}

/**
 * 批量操作请求接口
 */
export interface BatchOperationRequest {
  notificationIds: number[]
}

/**
 * 批量操作响应接口
 */
export interface BatchOperationResponse {
  affectedCount: number
  deletedCount?: number
}

/**
 * 通知偏好设置接口
 */
export interface NotificationPreference {
  id: number
  userId: number
  notificationType: NotificationType
  channel: NotificationChannel
  isEnabled: boolean
  createdAt: string
  updatedAt: string
}

/**
 * 通知偏好设置项接口
 */
export interface NotificationPreferenceItem {
  notificationType: NotificationType
  channel: NotificationChannel
  isEnabled: boolean
}

/**
 * 更新通知偏好设置请求接口
 */
export interface UpdatePreferencesRequest {
  preferences: NotificationPreferenceItem[]
}

/**
 * 通知统计信息接口
 */
export interface NotificationStats {
  total: number
  unread: number
  byType: Record<NotificationType, number>
  byPriority: Record<NotificationPriority, number>
}

/**
 * 通知类型配置接口
 */
export interface NotificationTypeConfig {
  type: NotificationType
  label: string
  description: string
  icon: string
  color: string
  defaultEnabled: boolean
}

/**
 * 通知渠道配置接口
 */
export interface NotificationChannelConfig {
  channel: NotificationChannel
  label: string
  description: string
  icon: string
  available: boolean
}

/**
 * 通知优先级配置接口
 */
export interface NotificationPriorityConfig {
  priority: NotificationPriority
  label: string
  color: string
  weight: number
}

/**
 * 通知过滤器接口
 */
export interface NotificationFilter {
  type?: NotificationType[]
  priority?: NotificationPriority[]
  isRead?: boolean
  dateRange?: {
    start: string
    end: string
  }
}

/**
 * 通知排序选项接口
 */
export interface NotificationSortOption {
  field: 'createdAt' | 'priority' | 'isRead'
  order: 'asc' | 'desc'
}

/**
 * 通知操作结果接口
 */
export interface NotificationActionResult {
  success: boolean
  message: string
  data?: any
}

/**
 * 实时通知事件接口
 */
export interface NotificationEvent {
  type: 'new_notification' | 'notification_read' | 'notification_deleted'
  notification?: Notification
  notificationId?: number
  userId: number
}

/**
 * 通知中心状态接口
 */
export interface NotificationCenterState {
  notifications: Notification[]
  unreadCount: number
  loading: boolean
  error: string | null
  hasMore: boolean
  currentPage: number
  filter: NotificationFilter
  sort: NotificationSortOption
}

/**
 * 通知偏好设置状态接口
 */
export interface NotificationPreferencesState {
  preferences: NotificationPreference[]
  loading: boolean
  error: string | null
}

/**
 * 通知WebSocket消息接口
 */
export interface NotificationWebSocketMessage {
  type: 'notification'
  event: NotificationEvent
  timestamp: string
}

/**
 * 通知模板接口
 */
export interface NotificationTemplate {
  type: NotificationType
  title: string
  content?: string
  variables: string[]
}

/**
 * 通知配置常量
 */
export const NOTIFICATION_CONFIG = {
  // 通知类型配置
  TYPES: {
    interaction: {
      label: '互动通知',
      description: '评论、点赞、回复等互动消息',
      icon: 'ChatDotRound',
      color: '#409EFF',
      defaultEnabled: true
    },
    content: {
      label: '内容通知',
      description: '新文章、内容更新等消息',
      icon: 'Document',
      color: '#67C23A',
      defaultEnabled: true
    },
    system: {
      label: '系统通知',
      description: '系统维护、功能更新等消息',
      icon: 'Setting',
      color: '#E6A23C',
      defaultEnabled: true
    },
    marketing: {
      label: '营销通知',
      description: '活动推广、功能推荐等消息',
      icon: 'Present',
      color: '#F56C6C',
      defaultEnabled: false
    }
  } as Record<NotificationType, NotificationTypeConfig>,

  // 通知渠道配置
  CHANNELS: {
    in_app: {
      label: '站内通知',
      description: '在网站内显示通知',
      icon: 'Bell',
      available: true
    },
    email: {
      label: '邮件通知',
      description: '发送到注册邮箱',
      icon: 'Message',
      available: true
    },
    push: {
      label: '推送通知',
      description: '浏览器桌面推送',
      icon: 'Position',
      available: false // 暂未实现
    }
  } as Record<NotificationChannel, NotificationChannelConfig>,

  // 通知优先级配置
  PRIORITIES: {
    high: {
      label: '高优先级',
      color: '#F56C6C',
      weight: 3
    },
    medium: {
      label: '中优先级',
      color: '#E6A23C',
      weight: 2
    },
    low: {
      label: '低优先级',
      color: '#909399',
      weight: 1
    }
  } as Record<NotificationPriority, NotificationPriorityConfig>,

  // 默认设置
  DEFAULTS: {
    PAGE_SIZE: 20,
    MAX_PAGE_SIZE: 100,
    REFRESH_INTERVAL: 30000, // 30秒
    CLEANUP_DAYS: 30
  }
} as const
