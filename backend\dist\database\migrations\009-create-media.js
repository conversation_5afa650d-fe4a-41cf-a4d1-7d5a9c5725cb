"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.createTable('media', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false,
            comment: '媒体ID'
        },
        filename: {
            type: sequelize_1.DataTypes.STRING(255),
            allowNull: false,
            comment: '文件名'
        },
        original_name: {
            type: sequelize_1.DataTypes.STRING(255),
            allowNull: false,
            comment: '原始文件名'
        },
        mime_type: {
            type: sequelize_1.DataTypes.STRING(100),
            allowNull: false,
            comment: 'MIME类型'
        },
        size: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            comment: '文件大小（字节）'
        },
        url: {
            type: sequelize_1.DataTypes.STRING(500),
            allowNull: false,
            comment: '文件URL'
        },
        thumbnail_url: {
            type: sequelize_1.DataTypes.STRING(500),
            allowNull: true,
            comment: '缩略图URL'
        },
        width: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true,
            comment: '图片/视频宽度'
        },
        height: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true,
            comment: '图片/视频高度'
        },
        uploader_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            comment: '上传者ID',
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        category: {
            type: sequelize_1.DataTypes.ENUM('image', 'video', 'audio', 'document'),
            allowNull: false,
            comment: '媒体类型'
        },
        tags: {
            type: sequelize_1.DataTypes.JSON,
            allowNull: true,
            comment: '标签数组（JSON格式）'
        },
        description: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true,
            comment: '媒体描述'
        },
        is_public: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: true,
            comment: '是否公开'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '创建时间'
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '更新时间'
        }
    }, {
        charset: 'utf8mb4',
        collate: 'utf8mb4_unicode_ci',
        comment: '媒体文件表'
    });
    await queryInterface.addIndex('media', ['uploader_id'], {
        name: 'idx_media_uploader_id'
    });
    await queryInterface.addIndex('media', ['category'], {
        name: 'idx_media_category'
    });
    await queryInterface.addIndex('media', ['is_public'], {
        name: 'idx_media_is_public'
    });
    await queryInterface.addIndex('media', ['mime_type'], {
        name: 'idx_media_mime_type'
    });
    await queryInterface.addIndex('media', ['created_at'], {
        name: 'idx_media_created_at'
    });
    await queryInterface.addIndex('media', ['uploader_id', 'category'], {
        name: 'idx_media_uploader_category'
    });
    console.log('✅ Created media table with indexes');
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.dropTable('media');
    console.log('✅ Dropped media table');
};
exports.down = down;
//# sourceMappingURL=009-create-media.js.map