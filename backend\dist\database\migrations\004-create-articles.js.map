{"version": 3, "file": "004-create-articles.js", "sourceRoot": "", "sources": ["../../../src/database/migrations/004-create-articles.ts"], "names": [], "mappings": ";;;AAAA,yCAAqD;AAO9C,MAAM,EAAE,GAAG,KAAK,EAAE,cAA8B,EAAiB,EAAE;IACxE,MAAM,cAAc,CAAC,WAAW,CAAC,UAAU,EAAE;QAC3C,EAAE,EAAE;YACF,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,UAAU,EAAE,IAAI;YAChB,aAAa,EAAE,IAAI;YACnB,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,MAAM;SAChB;QACD,KAAK,EAAE;YACL,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,GAAG,CAAC;YAC3B,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,MAAM;SAChB;QACD,IAAI,EAAE;YACJ,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,GAAG,CAAC;YAC3B,SAAS,EAAE,KAAK;YAChB,MAAM,EAAE,IAAI;YACZ,OAAO,EAAE,aAAa;SACvB;QACD,OAAO,EAAE;YACP,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,kBAAkB;SAC5B;QACD,OAAO,EAAE;YACP,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,MAAM;SAChB;QACD,MAAM,EAAE;YACN,IAAI,EAAE,qBAAS,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC;YAC1C,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,OAAO;YACrB,OAAO,EAAE,MAAM;SAChB;QACD,SAAS,EAAE;YACT,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,MAAM;YACf,UAAU,EAAE;gBACV,KAAK,EAAE,OAAO;gBACd,GAAG,EAAE,IAAI;aACV;YACD,QAAQ,EAAE,SAAS;YACnB,QAAQ,EAAE,SAAS;SACpB;QACD,WAAW,EAAE;YACX,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,MAAM;YACf,UAAU,EAAE;gBACV,KAAK,EAAE,YAAY;gBACnB,GAAG,EAAE,IAAI;aACV;YACD,QAAQ,EAAE,SAAS;YACnB,QAAQ,EAAE,UAAU;SACrB;QACD,YAAY,EAAE;YACZ,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,MAAM;SAChB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;YAC3B,OAAO,EAAE,MAAM;SAChB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;YAC3B,OAAO,EAAE,MAAM;SAChB;KACF,EAAE;QACD,OAAO,EAAE,SAAS;QAClB,OAAO,EAAE,oBAAoB;QAC7B,OAAO,EAAE,KAAK;KACf,CAAC,CAAA;IAGF,MAAM,cAAc,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,MAAM,CAAC,EAAE;QAClD,IAAI,EAAE,mBAAmB;QACzB,MAAM,EAAE,IAAI;KACb,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,WAAW,CAAC,EAAE;QACvD,IAAI,EAAE,wBAAwB;KAC/B,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,aAAa,CAAC,EAAE;QACzD,IAAI,EAAE,0BAA0B;KACjC,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,EAAE;QACpD,IAAI,EAAE,qBAAqB;KAC5B,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,cAAc,CAAC,EAAE;QAC1D,IAAI,EAAE,2BAA2B;KAClC,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,QAAQ,EAAE,cAAc,CAAC,EAAE;QACpE,IAAI,EAAE,+BAA+B;KACtC,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,WAAW,EAAE,QAAQ,CAAC,EAAE;QACjE,IAAI,EAAE,4BAA4B;KACnC,CAAC,CAAA;IAEF,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAA;AACtD,CAAC,CAAA;AAhHY,QAAA,EAAE,MAgHd;AAEM,MAAM,IAAI,GAAG,KAAK,EAAE,cAA8B,EAAiB,EAAE;IAC1E,MAAM,cAAc,CAAC,SAAS,CAAC,UAAU,CAAC,CAAA;IAC1C,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAA;AACzC,CAAC,CAAA;AAHY,QAAA,IAAI,QAGhB"}