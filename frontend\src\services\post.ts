import { request } from '@/utils/request'
import type {
  Post,
  PostParams,
  PostsResponse,
  PostResponse,
  PostCreateRequest,
  PostUpdateRequest,
  PostActionResponse,
  PostLikeResponse,
  PostLikesResponse,
  PostStats
} from '@/types/post'

/**
 * 说说服务模块，提供说说相关的API接口封装
 */
export const postService = {
  /**
   * 获取说说列表
   * @param params 查询参数，包括分页、可见性、作者筛选等
   * @returns 返回包含说说列表和分页信息的Promise对象
   */
  async getPosts(params: PostParams = {}): Promise<PostsResponse> {
    try {
      const response = await request.get<PostsResponse>('/posts', {
        params,
        loadingMessage: '正在加载说说列表...'
      })
      return {
        success: response.data.success || true,
        posts: response.data.posts || [],
        pagination: response.data.pagination || {
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 1
        }
      }
    } catch (error) {
      console.error('获取说说列表失败:', error)
      // 返回空数据而不是抛出错误
      return {
        success: false,
        posts: [],
        pagination: {
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 1
        }
      }
    }
  },

  /**
   * 获取单个说说详情
   * @param id 说说ID
   * @returns 返回说说详情数据
   */
  async getPost(id: number): Promise<Post> {
    const response = await request.get<PostResponse>(`/posts/${id}`, {
      loadingMessage: '正在加载说说详情...'
    })
    return response.data.post
  },

  /**
   * 创建新说说
   * @param postData 说说数据对象，包含内容、图片、可见性等信息
   * @returns 返回创建成功的说说对象
   */
  async createPost(postData: PostCreateRequest): Promise<Post> {
    // 前端验证
    if (!postData.content || postData.content.trim().length === 0) {
      throw new Error('说说内容不能为空')
    }
    if (postData.content.length > 1000) {
      throw new Error('说说内容不能超过1000个字符')
    }
    if (postData.images && postData.images.length > 9) {
      throw new Error('最多只能上传9张图片')
    }

    const response = await request.post<PostResponse>('/posts', postData, {
      loadingMessage: '正在发布说说...'
    })
    return response.data.post
  },

  /**
   * 更新说说
   * @param id 说说ID
   * @param postData 更新的说说数据
   * @returns 返回更新后的说说对象
   */
  async updatePost(id: number, postData: PostUpdateRequest): Promise<Post> {
    // 前端验证
    if (postData.content !== undefined) {
      if (postData.content.trim().length === 0) {
        throw new Error('说说内容不能为空')
      }
      if (postData.content.length > 1000) {
        throw new Error('说说内容不能超过1000个字符')
      }
    }
    if (postData.images && postData.images.length > 9) {
      throw new Error('最多只能上传9张图片')
    }

    const response = await request.put<PostResponse>(`/posts/${id}`, postData, {
      loadingMessage: '正在更新说说...'
    })
    return response.data.post
  },

  /**
   * 删除说说
   * @param id 说说ID
   * @returns 返回删除操作结果
   */
  async deletePost(id: number): Promise<PostActionResponse> {
    const response = await request.delete<PostActionResponse>(`/posts/${id}`, {
      loadingMessage: '正在删除说说...'
    })
    return response.data
  },

  /**
   * 切换说说点赞状态
   * @param id 说说ID
   * @returns 返回点赞操作结果
   */
  async toggleLike(id: number): Promise<PostLikeResponse> {
    const response = await request.post<PostLikeResponse>(`/posts/${id}/like`, {}, {
      loadingMessage: '正在处理点赞...'
    })
    return response.data
  },

  /**
   * 获取说说的点赞用户列表
   * @param id 说说ID
   * @param params 查询参数
   * @returns 返回点赞用户列表
   */
  async getPostLikes(id: number, params: { page?: number; limit?: number } = {}): Promise<PostLikesResponse> {
    try {
      const response = await request.get<PostLikesResponse>(`/posts/${id}/likes`, {
        params,
        loadingMessage: '正在加载点赞列表...'
      })
      return {
        success: response.data.success || true,
        likes: response.data.likes || [],
        pagination: response.data.pagination || {
          page: 1,
          limit: 20,
          total: 0,
          totalPages: 1
        }
      }
    } catch (error) {
      console.error('获取点赞列表失败:', error)
      return {
        success: false,
        likes: [],
        pagination: {
          page: 1,
          limit: 20,
          total: 0,
          totalPages: 1
        }
      }
    }
  },

  /**
   * 获取说说统计信息
   * @returns 返回说说统计数据
   */
  async getPostStats(): Promise<PostStats> {
    try {
      const response = await request.get<{ data: PostStats }>('/posts/stats', {
        loadingMessage: '正在加载统计信息...'
      })
      return response.data.data
    } catch (error) {
      console.error('获取说说统计失败:', error)
      // 返回默认统计数据
      return {
        total: 0,
        public: 0,
        private: 0,
        todayCount: 0
      }
    }
  },

  /**
   * 获取用户的说说列表
   * @param userId 用户ID
   * @param params 查询参数
   * @returns 返回用户说说列表
   */
  async getUserPosts(userId: number, params: PostParams = {}): Promise<PostsResponse> {
    try {
      const response = await request.get<PostsResponse>(`/posts/user/${userId}`, {
        params,
        loadingMessage: '正在加载用户说说...'
      })
      return {
        success: response.data.success || true,
        posts: response.data.posts || [],
        pagination: response.data.pagination || {
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 1
        }
      }
    } catch (error) {
      console.error('获取用户说说失败:', error)
      return {
        success: false,
        posts: [],
        pagination: {
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 1
        }
      }
    }
  },

  /**
   * 批量删除说说
   * @param ids 说说ID数组
   * @returns 返回批量删除操作结果
   */
  async batchDeletePosts(ids: number[]): Promise<PostActionResponse> {
    if (!ids || ids.length === 0) {
      throw new Error('请选择要删除的说说')
    }

    const response = await request.delete<PostActionResponse>('/posts/batch', {
      data: { ids },
      loadingMessage: '正在批量删除说说...'
    })
    return response.data
  }
}
