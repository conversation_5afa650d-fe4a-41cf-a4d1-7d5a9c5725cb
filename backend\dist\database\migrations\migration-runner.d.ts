export declare class MigrationRunner {
    private queryInterface;
    constructor();
    private ensureMigrationsTable;
    private getExecutedMigrations;
    private recordMigration;
    private removeMigrationRecord;
    private getMigrationFiles;
    private loadMigration;
    up(targetMigration?: string): Promise<void>;
    down(steps?: number): Promise<void>;
    status(): Promise<void>;
    reset(): Promise<void>;
}
//# sourceMappingURL=migration-runner.d.ts.map