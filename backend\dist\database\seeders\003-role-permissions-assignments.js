"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.up = up;
exports.down = down;
async function up(queryInterface) {
    console.log('🌱 创建角色权限分配种子数据...');
    const superAdminPermissions = Array.from({ length: 53 }, (_, i) => ({
        role_id: 1,
        permission_id: i + 1,
        assigned_by: 1,
        assigned_at: new Date(),
        created_at: new Date(),
        updated_at: new Date()
    }));
    const adminPermissionIds = [
        2, 3, 5,
        6, 7, 8, 9, 10, 11,
        12, 13, 14, 15, 16,
        17, 18, 19, 20, 21,
        22, 23, 24, 25, 26, 27,
        28, 29, 30, 31, 32,
        33, 34, 35, 36, 37,
        38, 39, 40, 41,
        43, 46,
        48
    ];
    const adminPermissions = adminPermissionIds.map(permissionId => ({
        role_id: 2,
        permission_id: permissionId,
        assigned_by: 1,
        assigned_at: new Date(),
        created_at: new Date(),
        updated_at: new Date()
    }));
    const editorPermissionIds = [
        2,
        6, 7, 8, 9, 10, 11,
        12, 13, 14, 16,
        17, 18, 19, 20, 21,
        23, 24, 26, 27,
        28, 29, 30, 31, 32,
        33, 34, 35, 37,
        39, 40
    ];
    const editorPermissions = editorPermissionIds.map(permissionId => ({
        role_id: 3,
        permission_id: permissionId,
        assigned_by: 1,
        assigned_at: new Date(),
        created_at: new Date(),
        updated_at: new Date()
    }));
    const userPermissionIds = [
        2, 3,
        6, 7, 8, 10,
        13, 16,
        18, 21,
        22, 23, 24,
        28, 29, 30, 31, 32,
        33, 34, 35,
        39, 40
    ];
    const userPermissions = userPermissionIds.map(permissionId => ({
        role_id: 4,
        permission_id: permissionId,
        assigned_by: 1,
        assigned_at: new Date(),
        created_at: new Date(),
        updated_at: new Date()
    }));
    await queryInterface.bulkInsert('role_permissions', [
        ...superAdminPermissions,
        ...adminPermissions,
        ...editorPermissions,
        ...userPermissions
    ]);
    const userRoles = [
        {
            id: 1,
            user_id: 1,
            role_id: 1,
            assigned_by: 1,
            assigned_at: new Date(),
            created_at: new Date(),
            updated_at: new Date()
        },
        {
            id: 2,
            user_id: 2,
            role_id: 3,
            assigned_by: 1,
            assigned_at: new Date(),
            created_at: new Date(),
            updated_at: new Date()
        },
        {
            id: 3,
            user_id: 3,
            role_id: 4,
            assigned_by: 1,
            assigned_at: new Date(),
            created_at: new Date(),
            updated_at: new Date()
        },
        {
            id: 4,
            user_id: 4,
            role_id: 4,
            assigned_by: 1,
            assigned_at: new Date(),
            created_at: new Date(),
            updated_at: new Date()
        }
    ];
    await queryInterface.bulkInsert('user_roles', userRoles);
    console.log('✅ 角色权限分配种子数据创建完成');
    console.log(`   - 超级管理员: ${superAdminPermissions.length} 个权限`);
    console.log(`   - 管理员: ${adminPermissions.length} 个权限`);
    console.log(`   - 编辑者: ${editorPermissions.length} 个权限`);
    console.log(`   - 普通用户: ${userPermissions.length} 个权限`);
    console.log(`   - 创建了 ${userRoles.length} 个用户角色分配`);
}
async function down(queryInterface) {
    await queryInterface.bulkDelete('user_roles', {}, {});
    await queryInterface.bulkDelete('role_permissions', {}, {});
}
//# sourceMappingURL=003-role-permissions-assignments.js.map