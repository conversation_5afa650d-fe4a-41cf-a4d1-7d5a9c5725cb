import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import ArticleCard from '../ArticleCard.vue'
import type { Article } from '@/types/article'
import type { Tag } from '@/types/tag'

// Mock Element Plus components
vi.mock('element-plus', () => ({
  ElCard: { name: 'ElCard', template: '<div class="el-card"><slot /></div>' },
  ElSkeleton: { name: 'ElSkeleton', template: '<div class="el-skeleton"></div>' },
  ElResult: { name: 'ElResult', template: '<div class="el-result"><slot name="extra" /></div>' },
  ElButton: { name: 'ElButton', template: '<button class="el-button"><slot /></button>' },
  ElButtonGroup: { name: 'ElButtonGroup', template: '<div class="el-button-group"><slot /></div>' },
  ElTag: { name: 'ElTag', template: '<span class="el-tag"><slot /></span>' },
  ElText: { name: 'ElText', template: '<span class="el-text"><slot /></span>' },
  ElIcon: { name: 'ElIcon', template: '<i class="el-icon"><slot /></i>' }
}))

// Mock Element Plus icons
vi.mock('@element-plus/icons-vue', () => ({
  Edit: { name: 'Edit' },
  Delete: { name: 'Delete' },
  Star: { name: 'Star' },
  Location: { name: 'Location' }
}))

// Create a mock router
const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', component: { template: '<div>Home</div>' } },
    { path: '/article/:id', component: { template: '<div>Article</div>' } }
  ]
})

// Mock article data
const mockArticle: Article = {
  id: 1,
  title: '测试文章标题',
  slug: 'test-article',
  content: '这是一篇测试文章的内容，用于测试阅读时间计算功能。'.repeat(50),
  excerpt: '这是文章摘要，用于在卡片中显示文章的简要介绍。',
  status: 'published',
  authorId: 1,
  createdAt: '2024-01-15T10:00:00Z',
  updatedAt: '2024-01-15T10:00:00Z',
  publishedAt: '2024-01-15T10:00:00Z',
  tags: [
    { id: 1, name: 'Vue', slug: 'vue' },
    { id: 2, name: 'TypeScript', slug: 'typescript' },
    { id: 3, name: '前端开发', slug: 'frontend' }
  ]
}

const mockDraftArticle: Article = {
  ...mockArticle,
  id: 2,
  title: '草稿文章',
  status: 'draft'
}

describe('ArticleCard', () => {
  let wrapper: VueWrapper<any>

  beforeEach(() => {
    vi.clearAllMocks()
  })

  const createWrapper = (props = {}) => {
    return mount(ArticleCard, {
      props: {
        article: mockArticle,
        ...props
      },
      global: {
        plugins: [router]
      }
    })
  }

  describe('基础渲染', () => {
    it('应该正确渲染文章标题', () => {
      wrapper = createWrapper()
      expect(wrapper.text()).toContain(mockArticle.title)
    })

    it('应该正确渲染文章摘要', () => {
      wrapper = createWrapper()
      expect(wrapper.text()).toContain(mockArticle.excerpt)
    })

    it('应该正确渲染发布日期', () => {
      wrapper = createWrapper()
      expect(wrapper.text()).toContain('2024')
    })

    it('应该正确渲染标签', () => {
      wrapper = createWrapper()
      expect(wrapper.text()).toContain('Vue')
      expect(wrapper.text()).toContain('TypeScript')
    })
  })

  describe('显示模式', () => {
    it('应该正确应用紧凑模式样式', () => {
      wrapper = createWrapper({ mode: 'compact' })
      expect(wrapper.find('.article-card--compact').exists()).toBe(true)
      expect(wrapper.find('.article-card__compact').exists()).toBe(true)
    })

    it('应该正确应用标准模式样式', () => {
      wrapper = createWrapper({ mode: 'normal' })
      expect(wrapper.find('.article-card--normal').exists()).toBe(true)
      expect(wrapper.find('.article-card__normal').exists()).toBe(true)
    })

    it('应该正确应用详细模式样式', () => {
      wrapper = createWrapper({ mode: 'detailed' })
      expect(wrapper.find('.article-card--detailed').exists()).toBe(true)
      expect(wrapper.find('.article-card__detailed').exists()).toBe(true)
    })

    it('紧凑模式不应该显示摘要', () => {
      wrapper = createWrapper({ mode: 'compact' })
      expect(wrapper.find('.article-card__excerpt').exists()).toBe(false)
    })

    it('标准模式应该限制显示的标签数量', () => {
      wrapper = createWrapper({ mode: 'normal' })
      const tags = wrapper.findAll('.article-card__tag')
      expect(tags.length).toBeLessThanOrEqual(3)
    })
  })

  describe('状态显示', () => {
    it('应该为草稿文章显示草稿标识', () => {
      wrapper = createWrapper({
        article: mockDraftArticle,
        showStatus: true
      })
      expect(wrapper.find('.article-card--draft').exists()).toBe(true)
      expect(wrapper.text()).toContain('草稿')
    })

    it('当showStatus为false时不应该显示状态标识', () => {
      wrapper = createWrapper({
        article: mockDraftArticle,
        showStatus: false
      })
      expect(wrapper.text()).not.toContain('草稿')
    })
  })

  describe('加载状态', () => {
    it('应该在加载时显示骨架屏', () => {
      wrapper = createWrapper({ loading: true })
      expect(wrapper.find('.article-card__loading').exists()).toBe(true)
      expect(wrapper.find('.el-skeleton').exists()).toBe(true)
    })

    it('加载时不应该显示文章内容', () => {
      wrapper = createWrapper({ loading: true })
      expect(wrapper.text()).not.toContain(mockArticle.title)
    })

    it('加载时应该禁用点击', () => {
      wrapper = createWrapper({ loading: true, clickable: true })
      expect(wrapper.attributes('tabindex')).toBe('-1')
    })
  })

  describe('错误状态', () => {
    it('应该在错误时显示错误信息', () => {
      wrapper = createWrapper({ error: '加载失败' })
      expect(wrapper.find('.article-card__error').exists()).toBe(true)
      expect(wrapper.text()).toContain('加载失败')
    })

    it('错误时应该显示重试按钮', () => {
      wrapper = createWrapper({ error: '网络错误' })
      expect(wrapper.text()).toContain('重试')
    })
  })

  describe('交互功能', () => {
    it('应该在点击时触发click事件', async () => {
      wrapper = createWrapper({ clickable: true })
      await wrapper.trigger('click')
      expect(wrapper.emitted('click')).toBeTruthy()
      expect(wrapper.emitted('click')?.[0]).toEqual([mockArticle])
    })

    it('不可点击时不应该触发click事件', async () => {
      wrapper = createWrapper({ clickable: false })
      await wrapper.trigger('click')
      expect(wrapper.emitted('click')).toBeFalsy()
    })

    it('应该在按Enter键时触发点击', async () => {
      wrapper = createWrapper({ clickable: true })
      await wrapper.trigger('keydown', { key: 'Enter' })
      expect(wrapper.emitted('click')).toBeTruthy()
    })

    it('应该在按空格键时触发点击', async () => {
      wrapper = createWrapper({ clickable: true })
      await wrapper.trigger('keydown', { key: ' ' })
      expect(wrapper.emitted('click')).toBeTruthy()
    })
  })

  describe('标签交互', () => {
    it('应该在点击标签时触发tagClick事件', async () => {
      wrapper = createWrapper()
      const tagElement = wrapper.find('.article-card__tag')
      await tagElement.trigger('click')
      expect(wrapper.emitted('tagClick')).toBeTruthy()
    })

    it('点击标签时应该阻止事件冒泡', async () => {
      wrapper = createWrapper({ clickable: true })
      const tagElement = wrapper.find('.article-card__tag')
      await tagElement.trigger('click')
      // 标签点击不应该触发卡片点击
      expect(wrapper.emitted('click')).toBeFalsy()
    })
  })

  describe('操作按钮', () => {
    it('showActions为true时应该显示操作按钮', () => {
      wrapper = createWrapper({ showActions: true })
      expect(wrapper.find('.article-card__actions').exists()).toBe(true)
    })

    it('showActions为false时不应该显示操作按钮', () => {
      wrapper = createWrapper({ showActions: false })
      expect(wrapper.find('.article-card__actions').exists()).toBe(false)
    })

    it('应该在点击编辑按钮时触发edit事件', async () => {
      wrapper = createWrapper({ showActions: true })
      const editButton = wrapper.find('.el-button')
      await editButton.trigger('click')
      expect(wrapper.emitted('edit')).toBeTruthy()
    })
  })

  describe('可访问性', () => {
    it('可点击卡片应该有正确的role属性', () => {
      wrapper = createWrapper({ clickable: true })
      expect(wrapper.attributes('role')).toBe('button')
    })

    it('不可点击卡片应该有article role', () => {
      wrapper = createWrapper({ clickable: false })
      expect(wrapper.attributes('role')).toBe('article')
    })

    it('应该有正确的aria-label', () => {
      wrapper = createWrapper({ clickable: true })
      expect(wrapper.attributes('aria-label')).toContain(mockArticle.title)
    })

    it('标签应该有正确的可访问性属性', () => {
      wrapper = createWrapper()
      const tagElement = wrapper.find('.article-card__tag')
      expect(tagElement.attributes('role')).toContain('button')
      expect(tagElement.attributes('tabindex')).toBe('0')
    })
  })

  describe('条件渲染', () => {
    it('showExcerpt为false时不应该显示摘要', () => {
      wrapper = createWrapper({ showExcerpt: false })
      expect(wrapper.find('.article-card__excerpt').exists()).toBe(false)
    })

    it('showReadingTime为false时不应该显示阅读时间', () => {
      wrapper = createWrapper({ showReadingTime: false })
      expect(wrapper.text()).not.toContain('分钟阅读')
    })

    it('没有标签时不应该显示标签区域', () => {
      const articleWithoutTags = { ...mockArticle, tags: [] }
      wrapper = createWrapper({ article: articleWithoutTags })
      expect(wrapper.find('.article-card__tags').exists()).toBe(false)
    })
  })

  describe('阅读时间计算', () => {
    it('应该正确计算阅读时间', () => {
      wrapper = createWrapper()
      expect(wrapper.text()).toContain('分钟阅读')
    })

    it('没有内容时不应该显示阅读时间', () => {
      const articleWithoutContent = { ...mockArticle, content: '' }
      wrapper = createWrapper({ article: articleWithoutContent })
      expect(wrapper.text()).not.toContain('分钟阅读')
    })
  })
})
