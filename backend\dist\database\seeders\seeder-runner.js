"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SeederRunner = void 0;
const database_1 = require("../../config/database");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
class SeederRunner {
    constructor() {
        this.queryInterface = database_1.sequelize.getQueryInterface();
    }
    async ensureSeedersTable() {
        const tableExists = await this.queryInterface.showAllTables()
            .then(tables => tables.includes('seeders'));
        if (!tableExists) {
            await this.queryInterface.createTable('seeders', {
                id: {
                    type: 'INTEGER',
                    primaryKey: true,
                    autoIncrement: true,
                    allowNull: false
                },
                name: {
                    type: 'VARCHAR(255)',
                    allowNull: false,
                    unique: true
                },
                executed_at: {
                    type: 'DATETIME',
                    allowNull: false,
                    defaultValue: new Date()
                }
            });
            console.log('✅ 创建种子数据记录表 seeders');
        }
    }
    async getExecutedSeeders() {
        await this.ensureSeedersTable();
        const records = await database_1.sequelize.query('SELECT name FROM seeders ORDER BY executed_at ASC', { type: 'SELECT' });
        return records.map(record => record.name);
    }
    async recordSeeder(name) {
        await database_1.sequelize.query('INSERT INTO seeders (name, executed_at) VALUES (?, ?)', {
            replacements: [name, new Date()],
            type: 'INSERT'
        });
    }
    async removeSeederRecord(name) {
        await database_1.sequelize.query('DELETE FROM seeders WHERE name = ?', {
            replacements: [name],
            type: 'DELETE'
        });
    }
    async getSeederFiles() {
        const seedersDir = __dirname;
        const files = fs.readdirSync(seedersDir);
        return files
            .filter(file => file.endsWith('.ts') && file !== 'seeder-runner.ts' && /^\d{3}-/.test(file))
            .sort();
    }
    async loadSeeder(filename) {
        const seederPath = path.join(__dirname, filename);
        const seeder = await Promise.resolve(`${seederPath}`).then(s => __importStar(require(s)));
        return {
            name: filename.replace('.ts', ''),
            up: seeder.up,
            down: seeder.down
        };
    }
    async seed(targetSeeder) {
        console.log('🌱 开始执行种子数据...');
        try {
            await database_1.sequelize.authenticate();
            console.log('✅ 数据库连接成功');
            const executedSeeders = await this.getExecutedSeeders();
            const seederFiles = await this.getSeederFiles();
            console.log(`📋 发现 ${seederFiles.length} 个种子数据文件`);
            console.log(`📋 已执行 ${executedSeeders.length} 个种子数据`);
            for (const filename of seederFiles) {
                const seederName = filename.replace('.ts', '');
                if (targetSeeder && seederName === targetSeeder) {
                    break;
                }
                if (executedSeeders.includes(seederName)) {
                    console.log(`⏭️ 跳过已执行的种子数据: ${seederName}`);
                    continue;
                }
                console.log(`🌱 执行种子数据: ${seederName}`);
                const seeder = await this.loadSeeder(filename);
                await seeder.up(this.queryInterface);
                await this.recordSeeder(seederName);
                console.log(`✅ 种子数据完成: ${seederName}`);
            }
            console.log('🎉 所有种子数据执行完成!');
        }
        catch (error) {
            console.error('❌ 种子数据执行失败:', error);
            throw error;
        }
    }
    async unseed(steps = 1) {
        console.log(`🔄 开始回滚最近 ${steps} 个种子数据...`);
        try {
            await database_1.sequelize.authenticate();
            console.log('✅ 数据库连接成功');
            const executedSeeders = await this.getExecutedSeeders();
            if (executedSeeders.length === 0) {
                console.log('ℹ️ 没有可回滚的种子数据');
                return;
            }
            const seedersToRollback = executedSeeders
                .slice(-steps)
                .reverse();
            for (const seederName of seedersToRollback) {
                console.log(`🔄 回滚种子数据: ${seederName}`);
                const seeder = await this.loadSeeder(`${seederName}.ts`);
                await seeder.down(this.queryInterface);
                await this.removeSeederRecord(seederName);
                console.log(`✅ 回滚完成: ${seederName}`);
            }
            console.log('🎉 种子数据回滚完成!');
        }
        catch (error) {
            console.error('❌ 种子数据回滚失败:', error);
            throw error;
        }
    }
    async status() {
        console.log('📊 种子数据状态:');
        try {
            await database_1.sequelize.authenticate();
            const executedSeeders = await this.getExecutedSeeders();
            const seederFiles = await this.getSeederFiles();
            console.log('\n已执行的种子数据:');
            if (executedSeeders.length === 0) {
                console.log('  (无)');
            }
            else {
                executedSeeders.forEach(name => {
                    console.log(`  ✅ ${name}`);
                });
            }
            console.log('\n待执行的种子数据:');
            const pendingSeeders = seederFiles
                .map(file => file.replace('.ts', ''))
                .filter(name => !executedSeeders.includes(name));
            if (pendingSeeders.length === 0) {
                console.log('  (无)');
            }
            else {
                pendingSeeders.forEach(name => {
                    console.log(`  ⏳ ${name}`);
                });
            }
            console.log(`\n总计: ${seederFiles.length} 个种子数据文件`);
            console.log(`已执行: ${executedSeeders.length} 个`);
            console.log(`待执行: ${pendingSeeders.length} 个`);
        }
        catch (error) {
            console.error('❌ 获取种子数据状态失败:', error);
            throw error;
        }
    }
    async reset() {
        console.log('🔄 重置种子数据...');
        try {
            const executedSeeders = await this.getExecutedSeeders();
            if (executedSeeders.length > 0) {
                await this.unseed(executedSeeders.length);
            }
            await this.seed();
            console.log('🎉 种子数据重置完成!');
        }
        catch (error) {
            console.error('❌ 种子数据重置失败:', error);
            throw error;
        }
    }
    async clear() {
        console.log('🗑️ 清空所有种子数据...');
        try {
            const executedSeeders = await this.getExecutedSeeders();
            if (executedSeeders.length > 0) {
                await this.unseed(executedSeeders.length);
            }
            console.log('✅ 所有种子数据已清空');
        }
        catch (error) {
            console.error('❌ 清空种子数据失败:', error);
            throw error;
        }
    }
}
exports.SeederRunner = SeederRunner;
if (require.main === module) {
    const runner = new SeederRunner();
    const command = process.argv[2];
    const arg = process.argv[3];
    switch (command) {
        case 'seed':
            runner.seed(arg).catch(console.error);
            break;
        case 'unseed':
            runner.unseed(arg ? parseInt(arg) : 1).catch(console.error);
            break;
        case 'status':
            runner.status().catch(console.error);
            break;
        case 'reset':
            runner.reset().catch(console.error);
            break;
        case 'clear':
            runner.clear().catch(console.error);
            break;
        default:
            console.log('用法:');
            console.log('  npm run seed                    - 执行所有种子数据');
            console.log('  npm run seed:status             - 查看种子数据状态');
            console.log('  npm run seed:unseed [steps]     - 回滚种子数据');
            console.log('  npm run seed:reset              - 重置种子数据');
            console.log('  npm run seed:clear              - 清空种子数据');
    }
}
//# sourceMappingURL=seeder-runner.js.map