import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { notificationService } from '@/services/notificationService'
import type {
  Notification,
  NotificationListParams,
  NotificationPreference,
  NotificationFilter,
  NotificationSortOption,
  NotificationType,
  NotificationChannel
} from '@/types/notification'

/**
 * 通知状态管理
 */
export const useNotificationStore = defineStore('notification', () => {
  // 状态定义
  const notifications = ref<Notification[]>([])
  const unreadCount = ref(0)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const hasMore = ref(true)
  const currentPage = ref(1)
  const pageSize = ref(20)

  // 过滤和排序
  const filter = ref<NotificationFilter>({})
  const sort = ref<NotificationSortOption>({
    field: 'createdAt',
    order: 'desc'
  })

  // 通知偏好设置
  const preferences = ref<NotificationPreference[]>([])
  const preferencesLoading = ref(false)
  const preferencesError = ref<string | null>(null)

  // 计算属性
  const unreadNotifications = computed(() =>
    notifications.value.filter(n => !n.isRead)
  )

  const readNotifications = computed(() =>
    notifications.value.filter(n => n.isRead)
  )

  const notificationsByType = computed(() => {
    const grouped: Record<string, Notification[]> = {}
    notifications.value.forEach(notification => {
      if (!grouped[notification.type]) {
        grouped[notification.type] = []
      }
      grouped[notification.type].push(notification)
    })
    return grouped
  })

  const hasUnreadNotifications = computed(() => unreadCount.value > 0)

  // 获取通知列表
  const fetchNotifications = async (params: NotificationListParams = {}, append = false) => {
    try {
      loading.value = true
      error.value = null

      const requestParams = {
        page: append ? currentPage.value : 1,
        limit: pageSize.value,
        ...params
      }

      const response = await notificationService.getNotifications(requestParams)

      if (append) {
        notifications.value.push(...response.notifications)
      } else {
        notifications.value = response.notifications
        currentPage.value = 1
      }

      hasMore.value = response.pagination.page < response.pagination.totalPages

      if (append) {
        currentPage.value++
      }

    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取通知列表失败'
      ElMessage.error(error.value)
    } finally {
      loading.value = false
    }
  }

  // 加载更多通知
  const loadMoreNotifications = async () => {
    if (!hasMore.value || loading.value) return

    const params: NotificationListParams = {}

    // 应用过滤条件
    if (filter.value.type && filter.value.type.length > 0) {
      params.type = filter.value.type[0] // API只支持单个类型过滤
    }
    if (filter.value.priority && filter.value.priority.length > 0) {
      params.priority = filter.value.priority[0] // API只支持单个优先级过滤
    }
    if (filter.value.isRead !== undefined) {
      params.is_read = filter.value.isRead
    }

    await fetchNotifications(params, true)
  }

  // 刷新通知列表
  const refreshNotifications = async () => {
    currentPage.value = 1
    hasMore.value = true
    await fetchNotifications()
    await fetchUnreadCount()
  }

  // 获取未读通知数量
  const fetchUnreadCount = async () => {
    try {
      unreadCount.value = await notificationService.getUnreadCount()
    } catch (err) {
      console.error('获取未读通知数量失败:', err)
    }
  }

  // 标记单个通知为已读
  const markAsRead = async (notificationId: number) => {
    try {
      const updatedNotification = await notificationService.markAsRead(notificationId)

      // 更新本地状态
      const index = notifications.value.findIndex(n => n.id === notificationId)
      if (index !== -1) {
        notifications.value[index] = updatedNotification
        if (!updatedNotification.isRead) {
          unreadCount.value = Math.max(0, unreadCount.value - 1)
        }
      }

      ElMessage.success('通知已标记为已读')
    } catch (err) {
      const message = err instanceof Error ? err.message : '标记通知已读失败'
      ElMessage.error(message)
      throw err
    }
  }

  // 批量标记通知为已读
  const markBatchAsRead = async (notificationIds: number[]) => {
    try {
      const affectedCount = await notificationService.markBatchAsRead(notificationIds)

      // 更新本地状态
      notifications.value.forEach(notification => {
        if (notificationIds.includes(notification.id) && !notification.isRead) {
          notification.isRead = true
          notification.readAt = new Date().toISOString()
        }
      })

      unreadCount.value = Math.max(0, unreadCount.value - affectedCount)
      ElMessage.success(`成功标记 ${affectedCount} 条通知为已读`)
    } catch (err) {
      const message = err instanceof Error ? err.message : '批量标记通知已读失败'
      ElMessage.error(message)
      throw err
    }
  }

  // 标记所有通知为已读
  const markAllAsRead = async () => {
    try {
      const affectedCount = await notificationService.markAllAsRead()

      // 更新本地状态
      notifications.value.forEach(notification => {
        if (!notification.isRead) {
          notification.isRead = true
          notification.readAt = new Date().toISOString()
        }
      })

      unreadCount.value = 0
      ElMessage.success(`成功标记 ${affectedCount} 条通知为已读`)
    } catch (err) {
      const message = err instanceof Error ? err.message : '标记所有通知已读失败'
      ElMessage.error(message)
      throw err
    }
  }

  // 删除单个通知
  const deleteNotification = async (notificationId: number) => {
    try {
      await notificationService.deleteNotification(notificationId)

      // 更新本地状态
      const index = notifications.value.findIndex(n => n.id === notificationId)
      if (index !== -1) {
        const notification = notifications.value[index]
        if (!notification.isRead) {
          unreadCount.value = Math.max(0, unreadCount.value - 1)
        }
        notifications.value.splice(index, 1)
      }

      ElMessage.success('通知删除成功')
    } catch (err) {
      const message = err instanceof Error ? err.message : '删除通知失败'
      ElMessage.error(message)
      throw err
    }
  }

  // 批量删除通知
  const deleteBatchNotifications = async (notificationIds: number[]) => {
    try {
      const deletedCount = await notificationService.deleteBatchNotifications(notificationIds)

      // 更新本地状态
      let unreadDeleted = 0
      notifications.value = notifications.value.filter(notification => {
        if (notificationIds.includes(notification.id)) {
          if (!notification.isRead) {
            unreadDeleted++
          }
          return false
        }
        return true
      })

      unreadCount.value = Math.max(0, unreadCount.value - unreadDeleted)
      ElMessage.success(`成功删除 ${deletedCount} 条通知`)
    } catch (err) {
      const message = err instanceof Error ? err.message : '批量删除通知失败'
      ElMessage.error(message)
      throw err
    }
  }

  // 获取通知偏好设置
  const fetchPreferences = async () => {
    try {
      preferencesLoading.value = true
      preferencesError.value = null
      preferences.value = await notificationService.getPreferences()
    } catch (err) {
      preferencesError.value = err instanceof Error ? err.message : '获取通知偏好设置失败'
      ElMessage.error(preferencesError.value)
    } finally {
      preferencesLoading.value = false
    }
  }

  // 更新通知偏好设置
  const updatePreferences = async (newPreferences: Array<{
    notificationType: NotificationType
    channel: NotificationChannel
    isEnabled: boolean
  }>) => {
    try {
      preferencesLoading.value = true
      preferencesError.value = null

      preferences.value = await notificationService.updatePreferences(newPreferences)
      ElMessage.success('通知偏好设置更新成功')
    } catch (err) {
      preferencesError.value = err instanceof Error ? err.message : '更新通知偏好设置失败'
      ElMessage.error(preferencesError.value)
      throw err
    } finally {
      preferencesLoading.value = false
    }
  }

  // 应用过滤器
  const applyFilter = async (newFilter: NotificationFilter) => {
    filter.value = { ...newFilter }
    await refreshNotifications()
  }

  // 应用排序
  const applySort = async (newSort: NotificationSortOption) => {
    sort.value = { ...newSort }
    await refreshNotifications()
  }

  // 清除过滤器
  const clearFilter = async () => {
    filter.value = {}
    await refreshNotifications()
  }

  // 重置状态
  const resetState = () => {
    notifications.value = []
    unreadCount.value = 0
    loading.value = false
    error.value = null
    hasMore.value = true
    currentPage.value = 1
    filter.value = {}
    sort.value = { field: 'createdAt', order: 'desc' }
  }

  // 初始化
  const initialize = async () => {
    await Promise.all([
      fetchNotifications(),
      fetchUnreadCount(),
      fetchPreferences()
    ])
  }

  return {
    // 状态
    notifications,
    unreadCount,
    loading,
    error,
    hasMore,
    currentPage,
    pageSize,
    filter,
    sort,
    preferences,
    preferencesLoading,
    preferencesError,

    // 计算属性
    unreadNotifications,
    readNotifications,
    notificationsByType,
    hasUnreadNotifications,

    // 方法
    fetchNotifications,
    loadMoreNotifications,
    refreshNotifications,
    fetchUnreadCount,
    markAsRead,
    markBatchAsRead,
    markAllAsRead,
    deleteNotification,
    deleteBatchNotifications,
    fetchPreferences,
    updatePreferences,
    applyFilter,
    applySort,
    clearFilter,
    resetState,
    initialize
  }
})
