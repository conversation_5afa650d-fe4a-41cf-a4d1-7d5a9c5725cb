import { request } from '@/utils/request'
import type {
  MediaQueryParams,
  MediaListApiResponse,
  MediaDetailApiResponse,
  MediaStatsApiResponse,
  MediaUpdateAttributes,
  SingleUploadApiResponse,
  MultipleUploadApiResponse,
  ApiResponse,
  MediaWithUploader
} from '@/types/media'

/**
 * 媒体文件API服务类
 * 提供媒体文件相关的所有API调用方法
 */
export class MediaService {
  private static readonly BASE_URL = '/api/media'
  private static readonly UPLOAD_URL = '/api/upload'

  /**
   * 获取媒体文件列表
   * @param params 查询参数
   * @returns Promise<MediaListApiResponse>
   */
  public static async getMediaList(params: MediaQueryParams = {}): Promise<MediaListApiResponse> {
    const response = await request.get<MediaListApiResponse>(this.BASE_URL, {
      params: {
        page: params.page || 1,
        limit: params.limit || 20,
        category: params.category,
        search: params.search,
        uploaderId: params.uploaderId,
        isPublic: params.isPublic,
        sortBy: params.sortBy || 'createdAt',
        sortOrder: params.sortOrder || 'DESC'
      },
      loadingMessage: '正在加载媒体文件列表...'
    })
    return response.data
  }

  /**
   * 获取单个媒体文件详情
   * @param id 媒体文件ID
   * @returns Promise<MediaDetailApiResponse>
   */
  public static async getMediaById(id: number): Promise<MediaDetailApiResponse> {
    const response = await request.get<MediaDetailApiResponse>(`${this.BASE_URL}/${id}`, {
      loadingMessage: '正在加载媒体详情...'
    })
    return response.data
  }

  /**
   * 更新媒体文件信息
   * @param id 媒体文件ID
   * @param data 更新数据
   * @returns Promise<MediaDetailApiResponse>
   */
  public static async updateMedia(id: number, data: MediaUpdateAttributes): Promise<MediaDetailApiResponse> {
    const response = await request.put<MediaDetailApiResponse>(`${this.BASE_URL}/${id}`, data, {
      loadingMessage: '正在更新媒体信息...'
    })
    return response.data
  }

  /**
   * 删除媒体文件
   * @param id 媒体文件ID
   * @returns Promise<ApiResponse>
   */
  public static async deleteMedia(id: number): Promise<ApiResponse> {
    const response = await request.delete<ApiResponse>(`${this.BASE_URL}/${id}`, {
      loadingMessage: '正在删除媒体文件...'
    })
    return response.data
  }

  /**
   * 获取媒体统计信息
   * @returns Promise<MediaStatsApiResponse>
   */
  public static async getMediaStats(): Promise<MediaStatsApiResponse> {
    try {
      const response: AxiosResponse<MediaStatsApiResponse> = await axios.get(`${this.BASE_URL}/stats`)
      return response.data
    } catch (error) {
      console.error('获取媒体统计失败:', error)
      throw this.handleError(error, '获取媒体统计失败')
    }
  }

  /**
   * 上传单个文件
   * @param file 文件对象
   * @param onProgress 上传进度回调
   * @returns Promise<SingleUploadApiResponse>
   */
  public static async uploadSingle(
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<SingleUploadApiResponse> {
    try {
      const formData = new FormData()
      formData.append('image', file)

      const response: AxiosResponse<SingleUploadApiResponse> = await axios.post(
        `${this.UPLOAD_URL}/single`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: (progressEvent) => {
            if (onProgress && progressEvent.total) {
              const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
              onProgress(progress)
            }
          }
        }
      )

      return response.data
    } catch (error) {
      console.error('单文件上传失败:', error)
      throw this.handleError(error, '文件上传失败')
    }
  }

  /**
   * 上传多个文件
   * @param files 文件数组
   * @param onProgress 上传进度回调
   * @returns Promise<MultipleUploadApiResponse>
   */
  public static async uploadMultiple(
    files: File[],
    onProgress?: (progress: number) => void
  ): Promise<MultipleUploadApiResponse> {
    try {
      const formData = new FormData()
      files.forEach(file => {
        formData.append('images', file)
      })

      const response: AxiosResponse<MultipleUploadApiResponse> = await axios.post(
        `${this.UPLOAD_URL}/multiple`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: (progressEvent) => {
            if (onProgress && progressEvent.total) {
              const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
              onProgress(progress)
            }
          }
        }
      )

      return response.data
    } catch (error) {
      console.error('多文件上传失败:', error)
      throw this.handleError(error, '文件上传失败')
    }
  }

  /**
   * 批量删除媒体文件
   * @param ids 媒体文件ID数组
   * @returns Promise<ApiResponse>
   */
  public static async batchDelete(ids: number[]): Promise<ApiResponse> {
    try {
      const deletePromises = ids.map(id => this.deleteMedia(id))
      await Promise.all(deletePromises)

      return {
        success: true,
        message: `成功删除 ${ids.length} 个媒体文件`
      }
    } catch (error) {
      console.error('批量删除失败:', error)
      throw this.handleError(error, '批量删除失败')
    }
  }

  /**
   * 批量更新媒体文件标签
   * @param ids 媒体文件ID数组
   * @param tags 标签数组
   * @returns Promise<ApiResponse>
   */
  public static async batchUpdateTags(ids: number[], tags: string[]): Promise<ApiResponse> {
    try {
      const updatePromises = ids.map(id => this.updateMedia(id, { tags }))
      await Promise.all(updatePromises)

      return {
        success: true,
        message: `成功更新 ${ids.length} 个媒体文件的标签`
      }
    } catch (error) {
      console.error('批量更新标签失败:', error)
      throw this.handleError(error, '批量更新标签失败')
    }
  }

  /**
   * 批量更新媒体文件可见性
   * @param ids 媒体文件ID数组
   * @param isPublic 是否公开
   * @returns Promise<ApiResponse>
   */
  public static async batchUpdateVisibility(ids: number[], isPublic: boolean): Promise<ApiResponse> {
    try {
      const updatePromises = ids.map(id => this.updateMedia(id, { isPublic }))
      await Promise.all(updatePromises)

      return {
        success: true,
        message: `成功更新 ${ids.length} 个媒体文件的可见性`
      }
    } catch (error) {
      console.error('批量更新可见性失败:', error)
      throw this.handleError(error, '批量更新可见性失败')
    }
  }

  /**
   * 搜索媒体文件
   * @param keyword 搜索关键词
   * @param filters 额外过滤条件
   * @returns Promise<MediaListApiResponse>
   */
  public static async searchMedia(
    keyword: string,
    filters: Partial<MediaQueryParams> = {}
  ): Promise<MediaListApiResponse> {
    return this.getMediaList({
      search: keyword,
      ...filters
    })
  }

  /**
   * 获取用户的媒体文件
   * @param userId 用户ID
   * @param params 查询参数
   * @returns Promise<MediaListApiResponse>
   */
  public static async getUserMedia(
    userId: number,
    params: Partial<MediaQueryParams> = {}
  ): Promise<MediaListApiResponse> {
    return this.getMediaList({
      uploaderId: userId,
      ...params
    })
  }

  /**
   * 处理API错误
   * @param error 错误对象
   * @param defaultMessage 默认错误消息
   * @returns Error
   */
  private static handleError(error: any, defaultMessage: string): Error {
    if (axios.isAxiosError(error)) {
      const message = error.response?.data?.message || error.message || defaultMessage
      return new Error(message)
    }
    return new Error(defaultMessage)
  }
}

/**
 * 媒体工具函数
 */
export class MediaUtils {
  /**
   * 格式化文件大小
   * @param bytes 字节数
   * @returns 格式化后的文件大小字符串
   */
  public static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes'

    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * 获取文件扩展名
   * @param filename 文件名
   * @returns 文件扩展名
   */
  public static getFileExtension(filename: string): string {
    return filename.split('.').pop()?.toLowerCase() || ''
  }

  /**
   * 检查是否为图片文件
   * @param mimeType MIME类型
   * @returns 是否为图片
   */
  public static isImage(mimeType: string): boolean {
    return mimeType.startsWith('image/')
  }

  /**
   * 检查是否为视频文件
   * @param mimeType MIME类型
   * @returns 是否为视频
   */
  public static isVideo(mimeType: string): boolean {
    return mimeType.startsWith('video/')
  }

  /**
   * 检查是否为音频文件
   * @param mimeType MIME类型
   * @returns 是否为音频
   */
  public static isAudio(mimeType: string): boolean {
    return mimeType.startsWith('audio/')
  }

  /**
   * 根据MIME类型获取媒体类别
   * @param mimeType MIME类型
   * @returns 媒体类别
   */
  public static getCategoryFromMimeType(mimeType: string): 'image' | 'video' | 'audio' | 'document' {
    if (this.isImage(mimeType)) return 'image'
    if (this.isVideo(mimeType)) return 'video'
    if (this.isAudio(mimeType)) return 'audio'
    return 'document'
  }

  /**
   * 验证文件类型是否被支持
   * @param mimeType MIME类型
   * @returns 是否支持
   */
  public static validateFileType(mimeType: string): boolean {
    const supportedTypes = [
      // 图片类型
      'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml',
      // 视频类型
      'video/mp4', 'video/webm', 'video/ogg', 'video/avi', 'video/mov',
      // 音频类型
      'audio/mp3', 'audio/wav', 'audio/ogg', 'audio/aac', 'audio/flac',
      // 文档类型
      'application/pdf', 'text/plain', 'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ]
    return supportedTypes.includes(mimeType)
  }
}
