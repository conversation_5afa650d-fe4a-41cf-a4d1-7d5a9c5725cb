"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.MigrationRunner = void 0;
const sequelize_1 = require("sequelize");
const database_1 = require("../../config/database");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
class MigrationRunner {
    constructor() {
        this.queryInterface = database_1.sequelize.getQueryInterface();
    }
    async ensureMigrationsTable() {
        const tableExists = await this.queryInterface.showAllTables()
            .then(tables => tables.includes('migrations'));
        if (!tableExists) {
            await this.queryInterface.createTable('migrations', {
                id: {
                    type: sequelize_1.DataTypes.INTEGER,
                    primaryKey: true,
                    autoIncrement: true,
                    allowNull: false
                },
                name: {
                    type: sequelize_1.DataTypes.STRING(255),
                    allowNull: false,
                    unique: true
                },
                executed_at: {
                    type: sequelize_1.DataTypes.DATE,
                    allowNull: false,
                    defaultValue: sequelize_1.DataTypes.NOW
                }
            });
            console.log('✅ 创建迁移记录表 migrations');
        }
    }
    async getExecutedMigrations() {
        await this.ensureMigrationsTable();
        const records = await database_1.sequelize.query('SELECT name FROM migrations ORDER BY executed_at ASC', { type: 'SELECT' });
        return records.map(record => record.name);
    }
    async recordMigration(name) {
        await database_1.sequelize.query('INSERT INTO migrations (name, executed_at) VALUES (?, ?)', {
            replacements: [name, new Date()],
            type: 'INSERT'
        });
    }
    async removeMigrationRecord(name) {
        await database_1.sequelize.query('DELETE FROM migrations WHERE name = ?', {
            replacements: [name],
            type: 'DELETE'
        });
    }
    async getMigrationFiles() {
        const migrationsDir = __dirname;
        const files = fs.readdirSync(migrationsDir);
        return files
            .filter(file => file.endsWith('.ts') && file !== 'migration-runner.ts')
            .sort();
    }
    async loadMigration(filename) {
        const migrationPath = path.join(__dirname, filename);
        const migration = await Promise.resolve(`${migrationPath}`).then(s => __importStar(require(s)));
        return {
            name: filename.replace('.ts', ''),
            up: migration.up,
            down: migration.down
        };
    }
    async up(targetMigration) {
        console.log('🚀 开始执行数据库迁移...');
        try {
            await database_1.sequelize.authenticate();
            console.log('✅ 数据库连接成功');
            const executedMigrations = await this.getExecutedMigrations();
            const migrationFiles = await this.getMigrationFiles();
            console.log(`📋 发现 ${migrationFiles.length} 个迁移文件`);
            console.log(`📋 已执行 ${executedMigrations.length} 个迁移`);
            for (const filename of migrationFiles) {
                const migrationName = filename.replace('.ts', '');
                if (targetMigration && migrationName === targetMigration) {
                    break;
                }
                if (executedMigrations.includes(migrationName)) {
                    console.log(`⏭️ 跳过已执行的迁移: ${migrationName}`);
                    continue;
                }
                console.log(`🔄 执行迁移: ${migrationName}`);
                const migration = await this.loadMigration(filename);
                await migration.up(this.queryInterface);
                await this.recordMigration(migrationName);
                console.log(`✅ 迁移完成: ${migrationName}`);
            }
            console.log('🎉 所有迁移执行完成!');
        }
        catch (error) {
            console.error('❌ 迁移执行失败:', error);
            throw error;
        }
    }
    async down(steps = 1) {
        console.log(`🔄 开始回滚最近 ${steps} 个迁移...`);
        try {
            await database_1.sequelize.authenticate();
            console.log('✅ 数据库连接成功');
            const executedMigrations = await this.getExecutedMigrations();
            if (executedMigrations.length === 0) {
                console.log('ℹ️ 没有可回滚的迁移');
                return;
            }
            const migrationsToRollback = executedMigrations
                .slice(-steps)
                .reverse();
            for (const migrationName of migrationsToRollback) {
                console.log(`🔄 回滚迁移: ${migrationName}`);
                const migration = await this.loadMigration(`${migrationName}.ts`);
                await migration.down(this.queryInterface);
                await this.removeMigrationRecord(migrationName);
                console.log(`✅ 回滚完成: ${migrationName}`);
            }
            console.log('🎉 迁移回滚完成!');
        }
        catch (error) {
            console.error('❌ 迁移回滚失败:', error);
            throw error;
        }
    }
    async status() {
        console.log('📊 迁移状态:');
        try {
            await database_1.sequelize.authenticate();
            const executedMigrations = await this.getExecutedMigrations();
            const migrationFiles = await this.getMigrationFiles();
            console.log('\n已执行的迁移:');
            if (executedMigrations.length === 0) {
                console.log('  (无)');
            }
            else {
                executedMigrations.forEach(name => {
                    console.log(`  ✅ ${name}`);
                });
            }
            console.log('\n待执行的迁移:');
            const pendingMigrations = migrationFiles
                .map(file => file.replace('.ts', ''))
                .filter(name => !executedMigrations.includes(name));
            if (pendingMigrations.length === 0) {
                console.log('  (无)');
            }
            else {
                pendingMigrations.forEach(name => {
                    console.log(`  ⏳ ${name}`);
                });
            }
            console.log(`\n总计: ${migrationFiles.length} 个迁移文件`);
            console.log(`已执行: ${executedMigrations.length} 个`);
            console.log(`待执行: ${pendingMigrations.length} 个`);
        }
        catch (error) {
            console.error('❌ 获取迁移状态失败:', error);
            throw error;
        }
    }
    async reset() {
        console.log('🔄 重置数据库...');
        try {
            const executedMigrations = await this.getExecutedMigrations();
            if (executedMigrations.length > 0) {
                await this.down(executedMigrations.length);
            }
            await this.up();
            console.log('🎉 数据库重置完成!');
        }
        catch (error) {
            console.error('❌ 数据库重置失败:', error);
            throw error;
        }
    }
}
exports.MigrationRunner = MigrationRunner;
if (require.main === module) {
    const runner = new MigrationRunner();
    const command = process.argv[2];
    const arg = process.argv[3];
    switch (command) {
        case 'up':
            runner.up(arg).catch(console.error);
            break;
        case 'down':
            runner.down(arg ? parseInt(arg) : 1).catch(console.error);
            break;
        case 'status':
            runner.status().catch(console.error);
            break;
        case 'reset':
            runner.reset().catch(console.error);
            break;
        default:
            console.log('用法:');
            console.log('  npm run migrate up [migration_name]  - 执行迁移');
            console.log('  npm run migrate down [steps]        - 回滚迁移');
            console.log('  npm run migrate status              - 查看迁移状态');
            console.log('  npm run migrate reset               - 重置数据库');
    }
}
//# sourceMappingURL=migration-runner.js.map