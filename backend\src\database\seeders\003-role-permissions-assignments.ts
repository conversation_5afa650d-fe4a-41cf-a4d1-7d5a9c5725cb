import { QueryInterface } from 'sequelize'

/**
 * 角色权限分配种子数据
 * 为不同角色分配相应的权限
 */

export async function up(queryInterface: QueryInterface): Promise<void> {
  console.log('🌱 创建角色权限分配种子数据...')

  // 超级管理员 - 拥有所有权限
  const superAdminPermissions = Array.from({ length: 53 }, (_, i) => ({
    role_id: 1, // super_admin
    permission_id: i + 1,
    assigned_by: 1,
    assigned_at: new Date(),
    created_at: new Date(),
    updated_at: new Date()
  }))

  // 管理员 - 除了系统级权限外的所有权限
  const adminPermissionIds = [
    // 用户管理 (除创建用户外)
    2, 3, 5,
    // 文章管理 (全部)
    6, 7, 8, 9, 10, 11,
    // 分类管理 (全部)
    12, 13, 14, 15, 16,
    // 标签管理 (全部)
    17, 18, 19, 20, 21,
    // 评论管理 (全部)
    22, 23, 24, 25, 26, 27,
    // 说说管理 (全部)
    28, 29, 30, 31, 32,
    // 媒体管理 (全部)
    33, 34, 35, 36, 37,
    // 通知管理 (全部)
    38, 39, 40, 41,
    // 角色权限管理 (查看和分配)
    43, 46,
    // 权限管理 (查看)
    48
  ]

  const adminPermissions = adminPermissionIds.map(permissionId => ({
    role_id: 2, // admin
    permission_id: permissionId,
    assigned_by: 1,
    assigned_at: new Date(),
    created_at: new Date(),
    updated_at: new Date()
  }))

  // 编辑者 - 内容管理权限
  const editorPermissionIds = [
    // 用户管理 (查看自己)
    2,
    // 文章管理 (全部)
    6, 7, 8, 9, 10, 11,
    // 分类管理 (查看、创建、更新)
    12, 13, 14, 16,
    // 标签管理 (全部)
    17, 18, 19, 20, 21,
    // 评论管理 (查看、更新、批准、拒绝)
    23, 24, 26, 27,
    // 说说管理 (全部)
    28, 29, 30, 31, 32,
    // 媒体管理 (上传、查看、更新、列表)
    33, 34, 35, 37,
    // 通知管理 (查看、更新)
    39, 40
  ]

  const editorPermissions = editorPermissionIds.map(permissionId => ({
    role_id: 3, // editor
    permission_id: permissionId,
    assigned_by: 1,
    assigned_at: new Date(),
    created_at: new Date(),
    updated_at: new Date()
  }))

  // 普通用户 - 基础权限
  const userPermissionIds = [
    // 用户管理 (查看、更新自己)
    2, 3,
    // 文章管理 (创建、查看、更新自己的)
    6, 7, 8, 10,
    // 分类管理 (查看、列表)
    13, 16,
    // 标签管理 (查看、列表)
    18, 21,
    // 评论管理 (创建、查看、更新自己的)
    22, 23, 24,
    // 说说管理 (全部自己的)
    28, 29, 30, 31, 32,
    // 媒体管理 (上传、查看、更新自己的)
    33, 34, 35,
    // 通知管理 (查看、更新自己的)
    39, 40
  ]

  const userPermissions = userPermissionIds.map(permissionId => ({
    role_id: 4, // user
    permission_id: permissionId,
    assigned_by: 1,
    assigned_at: new Date(),
    created_at: new Date(),
    updated_at: new Date()
  }))

  // 批量插入角色权限关联
  await queryInterface.bulkInsert('role_permissions', [
    ...superAdminPermissions,
    ...adminPermissions,
    ...editorPermissions,
    ...userPermissions
  ])

  // 创建用户角色分配
  const userRoles = [
    {
      id: 1,
      user_id: 1, // admin用户
      role_id: 1, // super_admin角色
      assigned_by: 1,
      assigned_at: new Date(),
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 2,
      user_id: 2, // john_doe用户
      role_id: 3, // editor角色
      assigned_by: 1,
      assigned_at: new Date(),
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 3,
      user_id: 3, // jane_smith用户
      role_id: 4, // user角色
      assigned_by: 1,
      assigned_at: new Date(),
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 4,
      user_id: 4, // mike_wilson用户
      role_id: 4, // user角色
      assigned_by: 1,
      assigned_at: new Date(),
      created_at: new Date(),
      updated_at: new Date()
    }
  ]

  await queryInterface.bulkInsert('user_roles', userRoles)

  console.log('✅ 角色权限分配种子数据创建完成')
  console.log(`   - 超级管理员: ${superAdminPermissions.length} 个权限`)
  console.log(`   - 管理员: ${adminPermissions.length} 个权限`)
  console.log(`   - 编辑者: ${editorPermissions.length} 个权限`)
  console.log(`   - 普通用户: ${userPermissions.length} 个权限`)
  console.log(`   - 创建了 ${userRoles.length} 个用户角色分配`)
}

export async function down(queryInterface: QueryInterface): Promise<void> {
  await queryInterface.bulkDelete('user_roles', {}, {})
  await queryInterface.bulkDelete('role_permissions', {}, {})
}
