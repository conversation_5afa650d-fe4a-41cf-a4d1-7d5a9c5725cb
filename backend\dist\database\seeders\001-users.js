"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.up = up;
exports.down = down;
const bcryptjs_1 = __importDefault(require("bcryptjs"));
async function up(queryInterface) {
    console.log('🌱 创建用户种子数据...');
    const users = [
        {
            id: 1,
            username: 'admin',
            email: '<EMAIL>',
            password_hash: await bcryptjs_1.default.hash('admin123', 12),
            is_active: true,
            email_verified: true,
            email_verified_at: new Date(),
            last_login_at: new Date(),
            created_at: new Date(),
            updated_at: new Date()
        },
        {
            id: 2,
            username: 'john_doe',
            email: '<EMAIL>',
            password_hash: await bcryptjs_1.default.hash('password123', 12),
            is_active: true,
            email_verified: true,
            email_verified_at: new Date(),
            last_login_at: new Date(Date.now() - 24 * 60 * 60 * 1000),
            created_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            updated_at: new Date()
        },
        {
            id: 3,
            username: 'jane_smith',
            email: '<EMAIL>',
            password_hash: await bcryptjs_1.default.hash('password123', 12),
            is_active: true,
            email_verified: true,
            email_verified_at: new Date(),
            last_login_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
            created_at: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000),
            updated_at: new Date()
        },
        {
            id: 4,
            username: 'mike_wilson',
            email: '<EMAIL>',
            password_hash: await bcryptjs_1.default.hash('password123', 12),
            is_active: true,
            email_verified: false,
            email_verification_token: 'verification_token_123',
            email_verification_expires: new Date(Date.now() + 24 * 60 * 60 * 1000),
            created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
            updated_at: new Date()
        }
    ];
    await queryInterface.bulkInsert('users', users);
    const settings = [
        {
            id: 1,
            user_id: 1,
            display_name: '系统管理员',
            bio: '博客系统管理员，负责内容审核和系统维护。',
            website: 'https://admin.example.com',
            location: '北京, 中国',
            theme: 'dark',
            language: 'zh-CN',
            timezone: 'Asia/Shanghai',
            items_per_page: 20,
            email_notifications: true,
            comment_notifications: true,
            system_notifications: true,
            profile_visibility: 'public',
            default_post_visibility: 'public',
            show_email: false,
            two_factor_enabled: true,
            created_at: new Date(),
            updated_at: new Date()
        },
        {
            id: 2,
            user_id: 2,
            display_name: 'John Doe',
            bio: '全栈开发工程师，热爱技术分享和开源项目。专注于 React、Node.js 和云原生技术。',
            website: 'https://johndoe.dev',
            location: '上海, 中国',
            theme: 'light',
            language: 'zh-CN',
            timezone: 'Asia/Shanghai',
            items_per_page: 15,
            email_notifications: true,
            comment_notifications: true,
            system_notifications: false,
            profile_visibility: 'public',
            default_post_visibility: 'public',
            show_email: true,
            two_factor_enabled: false,
            created_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            updated_at: new Date()
        },
        {
            id: 3,
            user_id: 3,
            display_name: 'Jane Smith',
            bio: '前端开发专家，UI/UX设计师。喜欢创造美观且实用的用户界面。',
            website: 'https://janesmith.design',
            location: '深圳, 中国',
            theme: 'auto',
            language: 'zh-CN',
            timezone: 'Asia/Shanghai',
            items_per_page: 10,
            email_notifications: false,
            comment_notifications: true,
            system_notifications: true,
            profile_visibility: 'public',
            default_post_visibility: 'private',
            show_email: false,
            two_factor_enabled: false,
            created_at: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000),
            updated_at: new Date()
        },
        {
            id: 4,
            user_id: 4,
            display_name: 'Mike Wilson',
            bio: '后端开发工程师，数据库专家。',
            theme: 'light',
            language: 'zh-CN',
            timezone: 'Asia/Shanghai',
            items_per_page: 20,
            email_notifications: true,
            comment_notifications: true,
            system_notifications: true,
            profile_visibility: 'private',
            default_post_visibility: 'public',
            show_email: false,
            two_factor_enabled: false,
            created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
            updated_at: new Date()
        }
    ];
    await queryInterface.bulkInsert('settings', settings);
    console.log('✅ 用户种子数据创建完成');
    console.log(`   - 创建了 ${users.length} 个用户`);
    console.log(`   - 创建了 ${settings.length} 个用户设置`);
}
async function down(queryInterface) {
    await queryInterface.bulkDelete('settings', {}, {});
    await queryInterface.bulkDelete('users', {}, {});
}
//# sourceMappingURL=001-users.js.map