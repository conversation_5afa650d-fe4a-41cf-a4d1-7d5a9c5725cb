"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.up = up;
exports.down = down;
async function up(queryInterface) {
    console.log('🌱 创建文章种子数据...');
    const articles = [
        {
            id: 1,
            title: 'React Hooks 完全指南：从入门到精通',
            slug: 'react-hooks-complete-guide',
            content: `# React Hooks 完全指南

React Hooks 是 React 16.8 引入的新特性，它让你在不编写 class 的情况下使用 state 以及其他的 React 特性。

## 什么是 Hooks？

Hooks 是一些可以让你在函数组件里"钩入" React state 及生命周期等特性的函数。

## 基础 Hooks

### useState

\`\`\`javascript
import React, { useState } from 'react';

function Counter() {
  const [count, setCount] = useState(0);

  return (
    <div>
      <p>You clicked {count} times</p>
      <button onClick={() => setCount(count + 1)}>
        Click me
      </button>
    </div>
  );
}
\`\`\`

### useEffect

\`\`\`javascript
import React, { useState, useEffect } from 'react';

function Example() {
  const [count, setCount] = useState(0);

  useEffect(() => {
    document.title = \`You clicked \${count} times\`;
  });

  return (
    <div>
      <p>You clicked {count} times</p>
      <button onClick={() => setCount(count + 1)}>
        Click me
      </button>
    </div>
  );
}
\`\`\`

## 高级 Hooks

### useContext
### useReducer
### useCallback
### useMemo

## 自定义 Hooks

自定义 Hook 是一个函数，其名称以 "use" 开头，函数内部可以调用其他的 Hook。

## 最佳实践

1. 只在最顶层使用 Hook
2. 只在 React 函数中调用 Hook
3. 使用 ESLint 插件来强制执行这些规则

## 总结

React Hooks 为函数组件提供了强大的能力，让代码更加简洁和可复用。`,
            excerpt: 'React Hooks 是 React 16.8 引入的新特性，本文将从基础到高级全面介绍 Hooks 的使用方法和最佳实践。',
            status: 'published',
            author_id: 2,
            category_id: 9,
            published_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
            created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
            updated_at: new Date()
        },
        {
            id: 2,
            title: 'Node.js 性能优化实战指南',
            slug: 'nodejs-performance-optimization-guide',
            content: `# Node.js 性能优化实战指南

Node.js 作为服务端 JavaScript 运行时，性能优化是一个重要话题。本文将分享一些实用的优化技巧。

## 1. 事件循环优化

### 避免阻塞事件循环

\`\`\`javascript
// 错误示例：同步操作阻塞事件循环
const fs = require('fs');
const data = fs.readFileSync('large-file.txt'); // 阻塞

// 正确示例：使用异步操作
const fs = require('fs').promises;
const data = await fs.readFile('large-file.txt'); // 非阻塞
\`\`\`

## 2. 内存管理

### 避免内存泄漏

1. 及时清理事件监听器
2. 避免全局变量
3. 正确使用闭包

### 使用流处理大文件

\`\`\`javascript
const fs = require('fs');
const readline = require('readline');

const fileStream = fs.createReadStream('large-file.txt');
const rl = readline.createInterface({
  input: fileStream,
  crlfDelay: Infinity
});

rl.on('line', (line) => {
  // 处理每一行
  console.log(line);
});
\`\`\`

## 3. 数据库优化

### 连接池管理

\`\`\`javascript
const mysql = require('mysql2');

const pool = mysql.createPool({
  host: 'localhost',
  user: 'root',
  password: 'password',
  database: 'test',
  connectionLimit: 10,
  queueLimit: 0
});
\`\`\`

### 查询优化

1. 使用索引
2. 避免 N+1 查询
3. 使用预编译语句

## 4. 缓存策略

### Redis 缓存

\`\`\`javascript
const redis = require('redis');
const client = redis.createClient();

// 缓存查询结果
async function getUserById(id) {
  const cacheKey = \`user:\${id}\`;
  let user = await client.get(cacheKey);
  
  if (!user) {
    user = await db.query('SELECT * FROM users WHERE id = ?', [id]);
    await client.setex(cacheKey, 3600, JSON.stringify(user));
  } else {
    user = JSON.parse(user);
  }
  
  return user;
}
\`\`\`

## 5. 监控和分析

### 使用 APM 工具

1. New Relic
2. DataDog
3. AppDynamics

### 性能分析

\`\`\`javascript
// 使用 console.time 测量执行时间
console.time('operation');
// 执行操作
console.timeEnd('operation');

// 使用 process.hrtime 进行高精度测量
const start = process.hrtime.bigint();
// 执行操作
const end = process.hrtime.bigint();
console.log(\`执行时间: \${Number(end - start) / 1000000}ms\`);
\`\`\`

## 总结

Node.js 性能优化需要从多个维度考虑，包括代码层面、架构层面和运维层面。持续监控和分析是优化的关键。`,
            excerpt: '本文分享 Node.js 性能优化的实战经验，包括事件循环优化、内存管理、数据库优化、缓存策略等方面的技巧。',
            status: 'published',
            author_id: 2,
            category_id: 12,
            published_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
            created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
            updated_at: new Date()
        },
        {
            id: 3,
            title: 'TypeScript 高级类型系统详解',
            slug: 'typescript-advanced-type-system',
            content: `# TypeScript 高级类型系统详解

TypeScript 的类型系统非常强大，本文将深入探讨一些高级特性。

## 1. 泛型 (Generics)

### 基础泛型

\`\`\`typescript
function identity<T>(arg: T): T {
  return arg;
}

let output = identity<string>("myString");
let output2 = identity("myString"); // 类型推断
\`\`\`

### 泛型约束

\`\`\`typescript
interface Lengthwise {
  length: number;
}

function loggingIdentity<T extends Lengthwise>(arg: T): T {
  console.log(arg.length);
  return arg;
}
\`\`\`

## 2. 条件类型 (Conditional Types)

\`\`\`typescript
type NonNullable<T> = T extends null | undefined ? never : T;

type Example = NonNullable<string | null>; // string
\`\`\`

## 3. 映射类型 (Mapped Types)

\`\`\`typescript
type Readonly<T> = {
  readonly [P in keyof T]: T[P];
}

type Partial<T> = {
  [P in keyof T]?: T[P];
}

interface User {
  name: string;
  age: number;
}

type ReadonlyUser = Readonly<User>;
type PartialUser = Partial<User>;
\`\`\`

## 4. 工具类型 (Utility Types)

### Pick 和 Omit

\`\`\`typescript
interface Todo {
  title: string;
  description: string;
  completed: boolean;
}

type TodoPreview = Pick<Todo, "title" | "completed">;
type TodoInfo = Omit<Todo, "completed">;
\`\`\`

### Record

\`\`\`typescript
type CatName = "miffy" | "boris" | "mordred";

interface CatInfo {
  age: number;
  breed: string;
}

const cats: Record<CatName, CatInfo> = {
  miffy: { age: 10, breed: "Persian" },
  boris: { age: 5, breed: "Maine Coon" },
  mordred: { age: 16, breed: "British Shorthair" },
};
\`\`\`

## 5. 模板字面量类型

\`\`\`typescript
type World = "world";
type Greeting = \`hello \${World}\`; // "hello world"

type EmailLocaleIDs = "welcome_email" | "email_heading";
type FooterLocaleIDs = "footer_title" | "footer_sendoff";

type AllLocaleIDs = \`\${EmailLocaleIDs | FooterLocaleIDs}_id\`;
// "welcome_email_id" | "email_heading_id" | "footer_title_id" | "footer_sendoff_id"
\`\`\`

## 6. 装饰器 (Decorators)

\`\`\`typescript
function sealed(constructor: Function) {
  Object.seal(constructor);
  Object.seal(constructor.prototype);
}

@sealed
class Greeter {
  greeting: string;
  constructor(message: string) {
    this.greeting = message;
  }
  greet() {
    return "Hello, " + this.greeting;
  }
}
\`\`\`

## 总结

TypeScript 的高级类型系统为我们提供了强大的类型安全保障和代码提示功能，掌握这些特性能够显著提升开发效率。`,
            excerpt: '深入探讨 TypeScript 的高级类型系统，包括泛型、条件类型、映射类型、工具类型等特性的使用方法。',
            status: 'published',
            author_id: 2,
            category_id: 11,
            published_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
            created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
            updated_at: new Date()
        },
        {
            id: 4,
            title: '我的编程学习之路：从零基础到全栈开发',
            slug: 'my-programming-journey-from-zero-to-fullstack',
            content: `# 我的编程学习之路：从零基础到全栈开发

回顾自己的编程学习历程，想分享一些经验和感悟，希望能对初学者有所帮助。

## 起点：完全的零基础

三年前，我还是一个对编程一无所知的小白。那时候连什么是编程语言都不清楚，更别说写代码了。

### 第一次接触编程

记得第一次接触编程是在大学的计算机基础课上，老师讲的是 C 语言。当时看着那些奇怪的符号和语法，完全摸不着头脑。

\`\`\`c
#include <stdio.h>

int main() {
    printf("Hello, World!");
    return 0;
}
\`\`\`

这简单的几行代码，当时的我看了半天才理解。

## 学习阶段：HTML/CSS 入门

### 选择前端作为起点

经过一番思考，我决定从前端开发开始学习，因为：
1. 学习成果可以直观看到
2. 入门相对容易
3. 资源丰富

### HTML 基础

从最基础的 HTML 标签开始：

\`\`\`html
<!DOCTYPE html>
<html>
<head>
    <title>我的第一个网页</title>
</head>
<body>
    <h1>Hello World</h1>
    <p>这是我的第一个网页！</p>
</body>
</html>
\`\`\`

### CSS 样式

然后学习 CSS，让网页变得好看：

\`\`\`css
body {
    font-family: Arial, sans-serif;
    background-color: #f0f0f0;
}

h1 {
    color: #333;
    text-align: center;
}
\`\`\`

## 进阶阶段：JavaScript 和框架

### JavaScript 基础

JavaScript 是我遇到的第一个真正的编程语言挑战：

\`\`\`javascript
// 变量和函数
let name = "张三";
function greet(name) {
    return "Hello, " + name + "!";
}

console.log(greet(name));
\`\`\`

### 学习 React

掌握了 JavaScript 基础后，开始学习 React：

\`\`\`jsx
import React, { useState } from 'react';

function Counter() {
    const [count, setCount] = useState(0);
    
    return (
        <div>
            <p>计数: {count}</p>
            <button onClick={() => setCount(count + 1)}>
                增加
            </button>
        </div>
    );
}
\`\`\`

## 全栈阶段：后端和数据库

### Node.js 后端

为了成为全栈开发者，开始学习 Node.js：

\`\`\`javascript
const express = require('express');
const app = express();

app.get('/api/users', (req, res) => {
    res.json({ message: 'Hello from backend!' });
});

app.listen(3000, () => {
    console.log('服务器运行在端口 3000');
});
\`\`\`

### 数据库学习

学习了 MySQL 和 MongoDB，理解了数据存储和查询。

## 学习心得

### 1. 坚持很重要
编程学习是一个长期过程，需要持续的练习和学习。

### 2. 实践出真知
光看教程是不够的，一定要动手写代码。

### 3. 不要害怕犯错
错误是学习的一部分，每个 bug 都是成长的机会。

### 4. 建立学习社群
加入技术社群，与其他开发者交流学习。

## 给初学者的建议

1. **选择一个方向深入学习**：不要什么都想学，先专精一个领域
2. **多做项目**：理论结合实践，做一些小项目巩固知识
3. **阅读优秀代码**：GitHub 上有很多优秀的开源项目
4. **写技术博客**：记录学习过程，分享给其他人
5. **保持好奇心**：技术在不断发展，要保持学习的热情

## 未来规划

目前我正在学习：
- 微服务架构
- 云原生技术
- 机器学习基础

编程学习永无止境，希望能在这条路上走得更远！

## 总结

从零基础到全栈开发，这条路虽然不容易，但每一步都很有意义。希望我的经历能给正在学习编程的朋友们一些启发和鼓励。

记住：每个大神都曾经是菜鸟，关键是要开始行动！`,
            excerpt: '分享我从零基础学习编程到成为全栈开发者的完整历程，包括学习方法、遇到的困难和给初学者的建议。',
            status: 'published',
            author_id: 3,
            category_id: 3,
            published_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
            created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
            updated_at: new Date()
        },
        {
            id: 5,
            title: 'Docker 容器化部署实战：从开发到生产',
            slug: 'docker-containerization-deployment-guide',
            content: `# Docker 容器化部署实战：从开发到生产

本文将详细介绍如何使用 Docker 进行应用的容器化部署，从开发环境到生产环境的完整流程。

## 什么是 Docker？

Docker 是一个开源的容器化平台，它可以让开发者将应用程序及其依赖打包到一个轻量级、可移植的容器中。

## 基础概念

### 镜像 (Image)
镜像是一个只读的模板，用来创建容器。

### 容器 (Container)
容器是镜像的运行实例。

### Dockerfile
用于构建镜像的文本文件。

## 编写 Dockerfile

### Node.js 应用示例

\`\`\`dockerfile
# 使用官方 Node.js 镜像作为基础镜像
FROM node:16-alpine

# 设置工作目录
WORKDIR /app

# 复制 package.json 和 package-lock.json
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制应用代码
COPY . .

# 暴露端口
EXPOSE 3000

# 创建非 root 用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# 切换到非 root 用户
USER nextjs

# 启动应用
CMD ["npm", "start"]
\`\`\`

### 多阶段构建

\`\`\`dockerfile
# 构建阶段
FROM node:16-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

# 生产阶段
FROM node:16-alpine AS production

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

COPY --from=builder /app/dist ./dist

EXPOSE 3000
CMD ["npm", "start"]
\`\`\`

## Docker Compose

### 开发环境配置

\`\`\`yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
    depends_on:
      - db
      - redis

  db:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: myapp
      MYSQL_USER: user
      MYSQL_PASSWORD: password
    ports:
      - "3306:3306"
    volumes:
      - db_data:/var/lib/mysql

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"

volumes:
  db_data:
\`\`\`

### 生产环境配置

\`\`\`yaml
version: '3.8'

services:
  app:
    image: myapp:latest
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=db
      - REDIS_HOST=redis
    depends_on:
      - db
      - redis
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

  db:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD_FILE: /run/secrets/db_root_password
      MYSQL_DATABASE: myapp
      MYSQL_USER: user
      MYSQL_PASSWORD_FILE: /run/secrets/db_password
    volumes:
      - db_data:/var/lib/mysql
    secrets:
      - db_root_password
      - db_password
    restart: unless-stopped

  redis:
    image: redis:6-alpine
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  db_data:
  redis_data:

secrets:
  db_root_password:
    file: ./secrets/db_root_password.txt
  db_password:
    file: ./secrets/db_password.txt
\`\`\`

## 最佳实践

### 1. 镜像优化

\`\`\`dockerfile
# 使用 Alpine 镜像减小体积
FROM node:16-alpine

# 合并 RUN 指令减少层数
RUN apk add --no-cache git && \\
    npm install -g pm2 && \\
    apk del git

# 使用 .dockerignore 排除不必要文件
\`\`\`

### 2. 安全性

\`\`\`dockerfile
# 不要使用 root 用户运行应用
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001
USER nextjs

# 使用 COPY 而不是 ADD
COPY package*.json ./

# 设置合适的文件权限
RUN chmod +x ./start.sh
\`\`\`

### 3. 健康检查

\`\`\`dockerfile
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \\
  CMD curl -f http://localhost:3000/health || exit 1
\`\`\`

## CI/CD 集成

### GitHub Actions 示例

\`\`\`yaml
name: Build and Deploy

on:
  push:
    branches: [main]

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Build Docker image
      run: docker build -t myapp:\${{ github.sha }} .

    - name: Push to registry
      run: |
        echo \${{ secrets.DOCKER_PASSWORD }} | docker login -u \${{ secrets.DOCKER_USERNAME }} --password-stdin
        docker push myapp:\${{ github.sha }}

    - name: Deploy to production
      run: |
        ssh user@server "docker pull myapp:\${{ github.sha }} && docker-compose up -d"
\`\`\`

## 监控和日志

### 日志管理

\`\`\`yaml
services:
  app:
    image: myapp:latest
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
\`\`\`

### 监控配置

\`\`\`yaml
services:
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
\`\`\`

## 总结

Docker 容器化部署为应用提供了一致的运行环境，简化了部署流程。通过合理的配置和最佳实践，可以构建出高效、安全、可维护的容器化应用。

关键要点：
1. 编写高效的 Dockerfile
2. 使用 Docker Compose 管理多服务应用
3. 遵循安全最佳实践
4. 集成 CI/CD 流程
5. 做好监控和日志管理`,
            excerpt: '详细介绍 Docker 容器化部署的完整流程，包括 Dockerfile 编写、Docker Compose 配置、CI/CD 集成等实战内容。',
            status: 'draft',
            author_id: 2,
            category_id: 8,
            created_at: new Date(),
            updated_at: new Date()
        }
    ];
    await queryInterface.bulkInsert('articles', articles);
    const articleTags = [
        { article_id: 1, tag_id: 3 },
        { article_id: 1, tag_id: 1 },
        { article_id: 1, tag_id: 31 },
        { article_id: 1, tag_id: 32 },
        { article_id: 1, tag_id: 21 },
        { article_id: 2, tag_id: 5 },
        { article_id: 2, tag_id: 22 },
        { article_id: 2, tag_id: 21 },
        { article_id: 2, tag_id: 35 },
        { article_id: 2, tag_id: 30 },
        { article_id: 3, tag_id: 2 },
        { article_id: 3, tag_id: 33 },
        { article_id: 3, tag_id: 31 },
        { article_id: 3, tag_id: 38 },
        { article_id: 4, tag_id: 35 },
        { article_id: 4, tag_id: 36 },
        { article_id: 4, tag_id: 32 },
        { article_id: 4, tag_id: 39 },
        { article_id: 5, tag_id: 12 },
        { article_id: 5, tag_id: 28 },
        { article_id: 5, tag_id: 29 },
        { article_id: 5, tag_id: 31 },
        { article_id: 5, tag_id: 21 }
    ];
    await queryInterface.bulkInsert('article_tags', articleTags);
    console.log('✅ 文章种子数据创建完成');
    console.log(`   - 创建了 ${articles.length} 篇文章`);
    console.log(`   - 创建了 ${articleTags.length} 个文章标签关联`);
}
async function down(queryInterface) {
    await queryInterface.bulkDelete('article_tags', {}, {});
    await queryInterface.bulkDelete('articles', {}, {});
}
//# sourceMappingURL=005-articles.js.map