#!/usr/bin/env ts-node
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const database_1 = require("../config/database");
const migration_runner_1 = require("../database/migrations/migration-runner");
class MigrationTester {
    constructor() {
        this.runner = new migration_runner_1.MigrationRunner();
    }
    async getAllTables() {
        const queryInterface = database_1.sequelize.getQueryInterface();
        return await queryInterface.showAllTables();
    }
    async getTableColumns(tableName) {
        const queryInterface = database_1.sequelize.getQueryInterface();
        return await queryInterface.describeTable(tableName);
    }
    async getTableIndexes(tableName) {
        const queryInterface = database_1.sequelize.getQueryInterface();
        try {
            return await queryInterface.showIndex(tableName);
        }
        catch (error) {
            console.warn(`⚠️ 无法获取表 ${tableName} 的索引信息:`, error);
            return [];
        }
    }
    async validateTableStructure() {
        console.log('🔍 验证表结构...');
        const expectedTables = [
            'users', 'roles', 'permissions', 'categories', 'tags',
            'settings', 'articles', 'posts', 'comments', 'media',
            'notifications', 'audit_logs', 'user_roles', 'role_permissions',
            'article_tags', 'post_likes', 'notification_preferences', 'migrations'
        ];
        const actualTables = await this.getAllTables();
        console.log(`📋 期望表数量: ${expectedTables.length}`);
        console.log(`📋 实际表数量: ${actualTables.length}`);
        const missingTables = expectedTables.filter(table => !actualTables.includes(table));
        if (missingTables.length > 0) {
            console.error('❌ 缺失的表:', missingTables);
            throw new Error(`缺失 ${missingTables.length} 个表`);
        }
        const extraTables = actualTables.filter(table => !expectedTables.includes(table));
        if (extraTables.length > 0) {
            console.warn('⚠️ 多余的表:', extraTables);
        }
        console.log('✅ 表结构验证通过');
    }
    async validateForeignKeys() {
        console.log('🔍 验证外键约束...');
        const foreignKeyTests = [
            { table: 'settings', column: 'user_id', references: 'users' },
            { table: 'articles', column: 'author_id', references: 'users' },
            { table: 'articles', column: 'category_id', references: 'categories' },
            { table: 'posts', column: 'author_id', references: 'users' },
            { table: 'comments', column: 'author_id', references: 'users' },
            { table: 'comments', column: 'article_id', references: 'articles' },
            { table: 'comments', column: 'post_id', references: 'posts' },
            { table: 'media', column: 'uploader_id', references: 'users' },
            { table: 'notifications', column: 'recipient_id', references: 'users' },
            { table: 'user_roles', column: 'user_id', references: 'users' },
            { table: 'user_roles', column: 'role_id', references: 'roles' },
            { table: 'role_permissions', column: 'role_id', references: 'roles' },
            { table: 'role_permissions', column: 'permission_id', references: 'permissions' }
        ];
        for (const test of foreignKeyTests) {
            try {
                const columns = await this.getTableColumns(test.table);
                const column = columns[test.column];
                if (!column) {
                    throw new Error(`列 ${test.column} 不存在于表 ${test.table}`);
                }
                console.log(`✅ 外键验证通过: ${test.table}.${test.column} -> ${test.references}`);
            }
            catch (error) {
                console.error(`❌ 外键验证失败: ${test.table}.${test.column}`, error);
                throw error;
            }
        }
        console.log('✅ 外键约束验证通过');
    }
    async validateIndexes() {
        console.log('🔍 验证索引...');
        const importantIndexes = [
            { table: 'users', columns: ['email'] },
            { table: 'users', columns: ['username'] },
            { table: 'articles', columns: ['slug'] },
            { table: 'articles', columns: ['author_id'] },
            { table: 'categories', columns: ['slug'] },
            { table: 'tags', columns: ['slug'] },
            { table: 'user_roles', columns: ['user_id', 'role_id'] },
            { table: 'role_permissions', columns: ['role_id', 'permission_id'] }
        ];
        for (const indexTest of importantIndexes) {
            try {
                const indexes = await this.getTableIndexes(indexTest.table);
                const hasIndex = indexes.some(index => {
                    const indexColumns = Array.isArray(index.fields)
                        ? index.fields.map((f) => f.attribute || f)
                        : [index.fields];
                    return indexTest.columns.every(col => indexColumns.includes(col));
                });
                if (hasIndex) {
                    console.log(`✅ 索引验证通过: ${indexTest.table}(${indexTest.columns.join(', ')})`);
                }
                else {
                    console.warn(`⚠️ 索引可能缺失: ${indexTest.table}(${indexTest.columns.join(', ')})`);
                }
            }
            catch (error) {
                console.warn(`⚠️ 无法验证索引: ${indexTest.table}`, error);
            }
        }
        console.log('✅ 索引验证完成');
    }
    async testDataInsertion() {
        console.log('🔍 测试数据插入...');
        try {
            await database_1.sequelize.query(`
        INSERT INTO users (username, email, password_hash, created_at, updated_at) 
        VALUES ('test_user', '<EMAIL>', 'hashed_password', NOW(), NOW())
      `);
            await database_1.sequelize.query(`
        INSERT INTO roles (name, description, created_at, updated_at) 
        VALUES ('test_role', 'Test role', NOW(), NOW())
      `);
            await database_1.sequelize.query(`
        INSERT INTO categories (name, slug, created_at, updated_at) 
        VALUES ('Test Category', 'test-category', NOW(), NOW())
      `);
            console.log('✅ 数据插入测试通过');
            await database_1.sequelize.query(`DELETE FROM categories WHERE slug = 'test-category'`);
            await database_1.sequelize.query(`DELETE FROM roles WHERE name = 'test_role'`);
            await database_1.sequelize.query(`DELETE FROM users WHERE username = 'test_user'`);
            console.log('✅ 测试数据清理完成');
        }
        catch (error) {
            console.error('❌ 数据插入测试失败:', error);
            throw error;
        }
    }
    async runTests() {
        console.log('🧪 开始迁移测试...');
        console.log('='.repeat(50));
        try {
            await database_1.sequelize.authenticate();
            console.log('✅ 数据库连接成功');
            console.log('\n📦 执行迁移...');
            await this.runner.up();
            console.log('\n🔍 开始验证...');
            await this.validateTableStructure();
            await this.validateForeignKeys();
            await this.validateIndexes();
            await this.testDataInsertion();
            console.log('\n' + '='.repeat(50));
            console.log('🎉 所有测试通过!');
            await this.runner.status();
        }
        catch (error) {
            console.error('\n❌ 测试失败:', error);
            throw error;
        }
    }
    async testRollback() {
        console.log('🔄 测试迁移回滚...');
        try {
            await this.runner.down();
            console.log('✅ 迁移回滚成功');
            await this.runner.up();
            console.log('✅ 迁移重新执行成功');
        }
        catch (error) {
            console.error('❌ 回滚测试失败:', error);
            throw error;
        }
    }
}
async function main() {
    const tester = new MigrationTester();
    try {
        await tester.runTests();
        if (process.argv.includes('--test-rollback')) {
            await tester.testRollback();
        }
        console.log('\n🎉 迁移测试完成!');
    }
    catch (error) {
        console.error('\n❌ 迁移测试失败:', error);
        process.exit(1);
    }
    finally {
        await database_1.sequelize.close();
    }
}
if (require.main === module) {
    main();
}
//# sourceMappingURL=test-migrations.js.map