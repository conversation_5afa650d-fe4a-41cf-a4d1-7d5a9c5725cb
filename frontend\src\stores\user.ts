import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { UserService } from '@/services/userService'
import type {
  User,
  Role,
  Permission,
  UserListParams,
  UserListResponse,
  UserStats,
  CreateUserRequest,
  UpdateUserRequest
} from '@/types/user'

export const useUserStore = defineStore('user', () => {
  // 状态
  const users = ref<User[]>([])
  const currentUser = ref<User | null>(null)
  const roles = ref<Role[]>([])
  const permissions = ref<Permission[]>([])
  const userStats = ref<UserStats | null>(null)
  const loading = ref(false)
  const pagination = ref({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  })

  // 计算属性
  const totalUsers = computed(() => userStats.value?.totalUsers || 0)
  const activeUsers = computed(() => userStats.value?.activeUsers || 0)
  const newUsersToday = computed(() => userStats.value?.newUsersToday || 0)
  const onlineUsers = computed(() => userStats.value?.onlineUsers || 0)

  const usersByRole = computed(() => userStats.value?.usersByRole || {})
  const usersByStatus = computed(() => userStats.value?.usersByStatus || {})

  // 根据ID获取用户
  const getUserById = computed(() => {
    return (id: number) => users.value.find(user => user.id === id)
  })

  // 根据用户名获取用户
  const getUserByUsername = computed(() => {
    return (username: string) => users.value.find(user => user.username === username)
  })

  // 获取活跃用户
  const getActiveUsers = computed(() => {
    return users.value.filter(user => user.status === 'active')
  })

  // 获取被禁用户
  const getBannedUsers = computed(() => {
    return users.value.filter(user => user.status === 'banned')
  })

  // Actions

  // 获取用户列表
  const fetchUsers = async (params: UserListParams = {}) => {
    try {
      loading.value = true
      const response: UserListResponse = await UserService.getUsers(params)

      users.value = response.users
      pagination.value = {
        page: response.currentPage,
        limit: params.limit || 20,
        total: response.total,
        totalPages: response.totalPages
      }

      return response
    } catch (error) {
      console.error('Failed to fetch users:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取用户详情
  const fetchUserById = async (id: number) => {
    try {
      const user = await UserService.getUserById(id)
      currentUser.value = user

      // 更新列表中的用户信息
      const index = users.value.findIndex(u => u.id === id)
      if (index !== -1) {
        users.value[index] = user
      }

      return user
    } catch (error) {
      console.error('Failed to fetch user:', error)
      throw error
    }
  }

  // 创建用户
  const createUser = async (userData: CreateUserRequest) => {
    try {
      const newUser = await UserService.createUser(userData)
      users.value.unshift(newUser)
      pagination.value.total += 1
      return newUser
    } catch (error) {
      console.error('Failed to create user:', error)
      throw error
    }
  }

  // 更新用户
  const updateUser = async (id: number, userData: UpdateUserRequest) => {
    try {
      const updatedUser = await UserService.updateUser(id, userData)

      // 更新列表中的用户
      const index = users.value.findIndex(u => u.id === id)
      if (index !== -1) {
        users.value[index] = updatedUser
      }

      // 更新当前用户
      if (currentUser.value?.id === id) {
        currentUser.value = updatedUser
      }

      return updatedUser
    } catch (error) {
      console.error('Failed to update user:', error)
      throw error
    }
  }

  // 删除用户
  const deleteUser = async (id: number) => {
    try {
      await UserService.deleteUser(id)

      // 从列表中移除用户
      const index = users.value.findIndex(u => u.id === id)
      if (index !== -1) {
        users.value.splice(index, 1)
        pagination.value.total -= 1
      }

      // 清除当前用户
      if (currentUser.value?.id === id) {
        currentUser.value = null
      }
    } catch (error) {
      console.error('Failed to delete user:', error)
      throw error
    }
  }

  // 批量删除用户
  const batchDeleteUsers = async (ids: number[]) => {
    try {
      await UserService.batchDeleteUsers(ids)

      // 从列表中移除用户
      users.value = users.value.filter(user => !ids.includes(user.id))
      pagination.value.total -= ids.length

      // 清除当前用户（如果被删除）
      if (currentUser.value && ids.includes(currentUser.value.id)) {
        currentUser.value = null
      }
    } catch (error) {
      console.error('Failed to batch delete users:', error)
      throw error
    }
  }

  // 重置密码
  const resetPassword = async (id: number, newPassword: string) => {
    try {
      await UserService.resetPassword(id, newPassword)
    } catch (error) {
      console.error('Failed to reset password:', error)
      throw error
    }
  }

  // 更新用户状态
  const updateUserStatus = async (id: number, status: 'active' | 'inactive' | 'banned') => {
    try {
      await UserService.updateUserStatus(id, status)

      // 更新列表中的用户状态
      const index = users.value.findIndex(u => u.id === id)
      if (index !== -1) {
        users.value[index].status = status
      }

      // 更新当前用户状态
      if (currentUser.value?.id === id) {
        currentUser.value.status = status
      }
    } catch (error) {
      console.error('Failed to update user status:', error)
      throw error
    }
  }

  // 获取用户统计
  const fetchUserStats = async () => {
    try {
      const stats = await UserService.getUserStats()
      userStats.value = stats
      return stats
    } catch (error) {
      console.error('Failed to fetch user stats:', error)
      throw error
    }
  }

  // 获取角色列表
  const fetchRoles = async () => {
    try {
      const roleList = await UserService.getRoles()
      roles.value = roleList
      return roleList
    } catch (error) {
      console.error('Failed to fetch roles:', error)
      throw error
    }
  }

  // 获取权限列表
  const fetchPermissions = async () => {
    try {
      const permissionList = await UserService.getPermissions()
      permissions.value = permissionList
      return permissionList
    } catch (error) {
      console.error('Failed to fetch permissions:', error)
      throw error
    }
  }

  // 分配角色
  const assignRoles = async (userId: number, roleIds: number[]) => {
    try {
      await UserService.assignRoles(userId, roleIds)

      // 重新获取用户详情以更新角色信息
      await fetchUserById(userId)
    } catch (error) {
      console.error('Failed to assign roles:', error)
      throw error
    }
  }

  // 移除角色
  const removeRoles = async (userId: number, roleIds: number[]) => {
    try {
      await UserService.removeRoles(userId, roleIds)

      // 重新获取用户详情以更新角色信息
      await fetchUserById(userId)
    } catch (error) {
      console.error('Failed to remove roles:', error)
      throw error
    }
  }

  // 搜索用户
  const searchUsers = async (query: string, limit: number = 10) => {
    try {
      return await UserService.searchUsers(query, limit)
    } catch (error) {
      console.error('Failed to search users:', error)
      throw error
    }
  }

  // 导出用户数据
  const exportUsers = async (params: UserListParams = {}) => {
    try {
      return await UserService.exportUsers(params)
    } catch (error) {
      console.error('Failed to export users:', error)
      throw error
    }
  }

  // 导入用户数据
  const importUsers = async (file: File) => {
    try {
      const result = await UserService.importUsers(file)

      // 重新获取用户列表
      await fetchUsers()

      return result
    } catch (error) {
      console.error('Failed to import users:', error)
      throw error
    }
  }

  // 强制用户下线
  const forceLogout = async (id: number) => {
    try {
      await UserService.forceLogout(id)
    } catch (error) {
      console.error('Failed to force logout:', error)
      throw error
    }
  }

  // 锁定用户
  const lockUser = async (id: number, reason?: string) => {
    try {
      await UserService.lockUser(id, reason)
      await updateUserStatus(id, 'banned')
    } catch (error) {
      console.error('Failed to lock user:', error)
      throw error
    }
  }

  // 解锁用户
  const unlockUser = async (id: number) => {
    try {
      await UserService.unlockUser(id)
      await updateUserStatus(id, 'active')
    } catch (error) {
      console.error('Failed to unlock user:', error)
      throw error
    }
  }

  // 检查用户名可用性
  const checkUsernameAvailability = async (username: string, excludeId?: number) => {
    try {
      return await UserService.checkUsernameAvailability(username, excludeId)
    } catch (error) {
      console.error('Failed to check username availability:', error)
      throw error
    }
  }

  // 检查邮箱可用性
  const checkEmailAvailability = async (email: string, excludeId?: number) => {
    try {
      return await UserService.checkEmailAvailability(email, excludeId)
    } catch (error) {
      console.error('Failed to check email availability:', error)
      throw error
    }
  }

  // 清除状态
  const clearState = () => {
    users.value = []
    currentUser.value = null
    roles.value = []
    permissions.value = []
    userStats.value = null
    pagination.value = {
      page: 1,
      limit: 20,
      total: 0,
      totalPages: 0
    }
  }

  // 设置当前用户
  const setCurrentUser = (user: User | null) => {
    currentUser.value = user
  }

  return {
    // 状态
    users,
    currentUser,
    roles,
    permissions,
    userStats,
    loading,
    pagination,

    // 计算属性
    totalUsers,
    activeUsers,
    newUsersToday,
    onlineUsers,
    usersByRole,
    usersByStatus,
    getUserById,
    getUserByUsername,
    getActiveUsers,
    getBannedUsers,

    // Actions
    fetchUsers,
    fetchUserById,
    createUser,
    updateUser,
    deleteUser,
    batchDeleteUsers,
    resetPassword,
    updateUserStatus,
    fetchUserStats,
    fetchRoles,
    fetchPermissions,
    assignRoles,
    removeRoles,
    searchUsers,
    exportUsers,
    importUsers,
    forceLogout,
    lockUser,
    unlockUser,
    checkUsernameAvailability,
    checkEmailAvailability,
    clearState,
    setCurrentUser
  }
})

export default useUserStore
