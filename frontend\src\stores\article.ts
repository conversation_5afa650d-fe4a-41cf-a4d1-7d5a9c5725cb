import { defineStore } from 'pinia'
import { ref } from 'vue'
import { articleService } from '@/services/article'
import type { Article } from '@/types/article'
import type { Category } from '@/types/category'

/**
 * 文章状态管理仓库
 * 使用Pinia定义的文章相关状态管理，包含文章列表、当前文章、分页信息、过滤条件等
 */
export const useArticleStore = defineStore('article', () => {
  const articles = ref<Article[]>([])
  const currentArticle = ref<Article | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 分页信息
  const pagination = ref({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 10,
    hasNextPage: false,
    hasPrevPage: false
  })

  // 过滤条件
  const filters = ref({
    search: '',
    status: '' as '' | 'draft' | 'published',
    tag: '',
    category: '' as string | number | ''
  })

  /**
   * 获取文章列表
   * @param page - 页码，默认为1
   * @param limit - 每页条数，默认为10
   * @param params - 查询参数，包含标签、分类、搜索关键词和状态
   * @returns 返回包含文章列表和分页信息的响应数据
   */
  const fetchArticles = async (page = 1, limit = 10, params?: { tag?: string; category?: string | number; search?: string; status?: 'draft' | 'published' }) => {
    error.value = null

    try {
      const response = await articleService.getArticles({
        page,
        limit,
        tag: params?.tag || filters.value.tag || undefined,
        category: params?.category || filters.value.category || undefined,
        search: params?.search || filters.value.search || undefined,
        status: params?.status || filters.value.status || undefined
      })
      articles.value = response.articles

      // 将后端分页格式转换为前端格式
      pagination.value = {
        currentPage: response.pagination.page,
        totalPages: response.pagination.totalPages,
        totalItems: response.pagination.total,
        itemsPerPage: response.pagination.limit,
        hasNextPage: response.pagination.page < response.pagination.totalPages,
        hasPrevPage: response.pagination.page > 1
      }

      return response
    } catch (err: any) {
      error.value = err.message
      throw err
    }
  }

  /**
   * 获取单篇文章详情
   * @param id - 文章ID
   * @returns 返回文章详情数据
   */
  const fetchArticle = async (id: number) => {
    error.value = null

    try {
      const article = await articleService.getArticle(id)
      currentArticle.value = article
      return article
    } catch (err: any) {
      error.value = err.message
      throw err
    }
  }

  /**
   * 创建新文章
   * @param articleData - 文章数据
   * @returns 返回创建成功的文章数据
   */
  const createArticle = async (articleData: Partial<Article>) => {
    try {
      const article = await articleService.createArticle(articleData)
      articles.value.unshift(article)
      return article
    } catch (err: any) {
      error.value = err.message
      throw err
    }
  }

  /**
   * 更新文章信息
   * @param id - 要更新的文章ID
   * @param articleData - 更新的文章数据
   * @returns 返回更新后的文章数据
   */
  const updateArticle = async (id: number, articleData: Partial<Article>) => {
    try {
      const article = await articleService.updateArticle(id, articleData)
      const index = articles.value.findIndex(a => a.id === id)
      if (index !== -1) {
        articles.value[index] = article
      }
      if (currentArticle.value?.id === id) {
        currentArticle.value = article
      }
      return article
    } catch (err: any) {
      error.value = err.message
      throw err
    }
  }

  /**
   * 删除单篇文章
   * @param id - 要删除的文章ID
   */
  const deleteArticle = async (id: number) => {
    try {
      await articleService.deleteArticle(id)
      articles.value = articles.value.filter(a => a.id !== id)
      if (currentArticle.value?.id === id) {
        currentArticle.value = null
      }
    } catch (err: any) {
      error.value = err.message
      throw err
    }
  }

  /**
   * 批量删除文章
   * @param ids - 要删除的文章ID数组
   */
  const deleteArticles = async (ids: number[]) => {
    try {
      await articleService.deleteArticles(ids)
      articles.value = articles.value.filter(a => !ids.includes(a.id))
      if (currentArticle.value && ids.includes(currentArticle.value.id)) {
        currentArticle.value = null
      }
    } catch (err: any) {
      error.value = err.message
      throw err
    }
  }

  /**
   * 设置过滤条件
   * @param newFilters - 新的过滤条件
   */
  const setFilters = (newFilters: Partial<typeof filters.value>) => {
    filters.value = { ...filters.value, ...newFilters }
  }

  /**
   * 清空所有过滤条件
   */
  const clearFilters = () => {
    filters.value = {
      search: '',
      status: '',
      tag: '',
      category: ''
    }
  }

  /**
   * 根据分类获取文章列表
   * @param categoryId 分类ID或slug
   * @param page 页码，默认为1
   * @param limit 每页条数，默认为10
   * @param params 其他查询参数
   */
  const fetchArticlesByCategory = async (categoryId: string | number, page = 1, limit = 10, params?: { search?: string; status?: 'draft' | 'published' }) => {
    error.value = null

    try {
      const response = await articleService.getArticlesByCategory(categoryId, {
        page,
        limit,
        search: params?.search,
        status: params?.status
      })
      articles.value = response.articles

      // 将后端分页格式转换为前端格式
      pagination.value = {
        currentPage: response.pagination.page,
        totalPages: response.pagination.totalPages,
        totalItems: response.pagination.total,
        itemsPerPage: response.pagination.limit,
        hasNextPage: response.pagination.page < response.pagination.totalPages,
        hasPrevPage: response.pagination.page > 1
      }

      return response
    } catch (err: any) {
      error.value = err.message
      throw err
    }
  }

  /**
   * 发布文章
   * @param id 文章ID
   */
  const publishArticle = async (id: number) => {
    try {
      const updatedArticle = await articleService.publishArticle(id)

      // 更新文章列表中的文章状态
      const index = articles.value.findIndex(article => article.id === id)
      if (index !== -1) {
        articles.value[index] = updatedArticle
      }

      // 更新当前文章状态
      if (currentArticle.value?.id === id) {
        currentArticle.value = updatedArticle
      }

      return updatedArticle
    } catch (err: any) {
      error.value = err.message
      throw err
    }
  }

  /**
   * 取消发布文章
   * @param id 文章ID
   */
  const unpublishArticle = async (id: number) => {
    try {
      const updatedArticle = await articleService.unpublishArticle(id)

      // 更新文章列表中的文章状态
      const index = articles.value.findIndex(article => article.id === id)
      if (index !== -1) {
        articles.value[index] = updatedArticle
      }

      // 更新当前文章状态
      if (currentArticle.value?.id === id) {
        currentArticle.value = updatedArticle
      }

      return updatedArticle
    } catch (err: any) {
      error.value = err.message
      throw err
    }
  }

  return {
    articles,
    currentArticle,
    loading,
    error,
    pagination,
    filters,
    fetchArticles,
    fetchArticle,
    fetchArticlesByCategory,
    createArticle,
    updateArticle,
    deleteArticle,
    deleteArticles,
    publishArticle,
    unpublishArticle,
    setFilters,
    clearFilters
  }
})