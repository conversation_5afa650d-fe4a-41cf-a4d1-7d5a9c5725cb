"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.createTable('tags', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false,
            comment: '标签ID'
        },
        name: {
            type: sequelize_1.DataTypes.STRING(50),
            allowNull: false,
            unique: true,
            comment: '标签名称'
        },
        slug: {
            type: sequelize_1.DataTypes.STRING(50),
            allowNull: false,
            unique: true,
            comment: '标签别名（URL友好）'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '创建时间'
        }
    }, {
        charset: 'utf8mb4',
        collate: 'utf8mb4_unicode_ci',
        comment: '标签表'
    });
    await queryInterface.addIndex('tags', ['name'], {
        name: 'idx_tags_name',
        unique: true
    });
    await queryInterface.addIndex('tags', ['slug'], {
        name: 'idx_tags_slug',
        unique: true
    });
    console.log('✅ Created tags table with indexes');
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.dropTable('tags');
    console.log('✅ Dropped tags table');
};
exports.down = down;
//# sourceMappingURL=002-create-tags.js.map