/**
 * 评论状态枚举
 */
export type CommentStatus = 'pending' | 'approved' | 'rejected'

/**
 * 用户信息接口（评论中的作者信息）
 */
export interface CommentAuthor {
  id: number
  username: string
}

/**
 * 文章信息接口（评论中的文章信息）
 */
export interface CommentArticle {
  id: number
  title: string
  slug: string
}

/**
 * 评论接口定义
 */
export interface Comment {
  /**
   * 评论ID
   */
  id: number
  
  /**
   * 评论内容
   */
  content: string
  
  /**
   * 评论状态
   */
  status: CommentStatus
  
  /**
   * 关联的文章ID
   */
  articleId: number
  
  /**
   * 评论作者ID
   */
  authorId: number
  
  /**
   * 父评论ID（用于回复功能）
   */
  parentId?: number
  
  /**
   * 创建时间
   */
  createdAt: string
  
  /**
   * 更新时间
   */
  updatedAt: string
  
  /**
   * 评论作者信息
   */
  author?: CommentAuthor
  
  /**
   * 关联的文章信息
   */
  article?: CommentArticle
  
  /**
   * 父评论信息
   */
  parent?: Comment
  
  /**
   * 回复列表
   */
  replies?: Comment[]
}

/**
 * 创建评论的请求参数
 */
export interface CommentCreateRequest {
  /**
   * 评论内容（1-2000字符）
   */
  content: string
  
  /**
   * 文章ID
   */
  articleId: number
  
  /**
   * 父评论ID（可选，用于回复功能）
   */
  parentId?: number
}

/**
 * 更新评论的请求参数
 */
export interface CommentUpdateRequest {
  /**
   * 更新的评论内容（1-2000字符）
   */
  content: string
}

/**
 * 更新评论状态的请求参数
 */
export interface CommentStatusUpdateRequest {
  /**
   * 新的评论状态
   */
  status: CommentStatus
}

/**
 * 评论查询参数接口
 */
export interface CommentParams {
  /**
   * 页码
   */
  page?: number
  
  /**
   * 每页条数
   */
  limit?: number
  
  /**
   * 文章ID筛选
   */
  articleId?: number
  
  /**
   * 评论状态筛选
   */
  status?: CommentStatus | 'all'
  
  /**
   * 作者ID筛选
   */
  authorId?: number
  
  /**
   * 排序字段
   */
  sort?: string
  
  /**
   * 排序方向
   */
  order?: 'asc' | 'desc'
}

/**
 * 分页信息接口
 */
export interface CommentPagination {
  /**
   * 当前页码
   */
  page: number
  
  /**
   * 每页条数
   */
  limit: number
  
  /**
   * 总条数
   */
  total: number
  
  /**
   * 总页数
   */
  totalPages: number
}

/**
 * 评论列表响应数据接口
 */
export interface CommentsResponse {
  /**
   * 请求是否成功
   */
  success: boolean
  
  /**
   * 评论列表数据
   */
  comments: Comment[]
  
  /**
   * 分页信息
   */
  pagination: CommentPagination
}

/**
 * 单个评论响应数据接口
 */
export interface CommentResponse {
  /**
   * 请求是否成功
   */
  success: boolean
  
  /**
   * 评论数据
   */
  comment: Comment
}

/**
 * 评论操作的通用响应接口
 */
export interface CommentActionResponse {
  /**
   * 请求是否成功
   */
  success: boolean
  
  /**
   * 响应消息
   */
  message?: string
  
  /**
   * 操作后的评论数据（可选）
   */
  comment?: Comment
}

/**
 * 评论统计信息接口
 */
export interface CommentStats {
  /**
   * 总评论数
   */
  total: number
  
  /**
   * 待审核评论数
   */
  pending: number
  
  /**
   * 已批准评论数
   */
  approved: number
  
  /**
   * 已拒绝评论数
   */
  rejected: number
}

/**
 * 评论验证错误接口
 */
export interface CommentValidationError {
  /**
   * 错误字段
   */
  field: string
  
  /**
   * 错误消息
   */
  message: string
}

/**
 * 评论API错误响应接口
 */
export interface CommentErrorResponse {
  /**
   * 请求是否成功
   */
  success: false
  
  /**
   * 错误信息
   */
  error: {
    /**
     * 错误代码
     */
    code: string
    
    /**
     * 错误消息
     */
    message: string
    
    /**
     * 验证错误详情（可选）
     */
    details?: CommentValidationError[]
  }
}
