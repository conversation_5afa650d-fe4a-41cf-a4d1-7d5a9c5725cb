/**
 * 分类信息接口
 */
export interface Category {
  /**
   * 分类唯一标识
   */
  id: number
  /**
   * 分类名称
   */
  name: string
  /**
   * 分类别名（URL友好）
   */
  slug: string
  /**
   * 分类描述
   */
  description?: string
  /**
   * 父分类ID
   */
  parentId?: number
  /**
   * 排序值
   */
  sort: number
  /**
   * 创建时间
   */
  createdAt: string
  /**
   * 更新时间
   */
  updatedAt: string
  /**
   * 父分类信息
   */
  parent?: Category
  /**
   * 子分类列表
   */
  children?: Category[]
  /**
   * 文章数量（统计信息）
   */
  articleCount?: number
  /**
   * 层级深度（扁平列表时使用）
   */
  level?: number
  /**
   * 完整路径（扁平列表时使用）
   */
  path?: string
}

/**
 * 分类列表响应数据接口
 */
export interface CategoriesResponse {
  success: boolean
  data: {
    categories: Category[]
  }
}

/**
 * 分类详情响应数据接口
 */
export interface CategoryResponse {
  success: boolean
  data: {
    category: Category
  }
}

/**
 * 分类文章列表响应数据接口
 */
export interface CategoryArticlesResponse {
  success: boolean
  data: {
    category: Category
    articles: any[]
    pagination: {
      currentPage: number
      totalPages: number
      totalItems: number
      itemsPerPage: number
      hasNextPage: boolean
      hasPrevPage: boolean
    }
  }
}

/**
 * 分类统计响应数据接口
 */
export interface CategoryStatsResponse {
  success: boolean
  data: {
    stats: Array<{
      category: Category
      articleCount: number
    }>
  }
}

/**
 * 分类查询参数接口
 */
export interface CategoryParams {
  /**
   * 是否返回树结构
   */
  tree?: boolean
  /**
   * 是否返回扁平列表（包含层级信息）
   */
  flat?: boolean
  /**
   * 是否包含统计信息
   */
  stats?: boolean
}

/**
 * 分类文章查询参数接口
 */
export interface CategoryArticleParams {
  /**
   * 页码
   */
  page?: number
  /**
   * 每页数量
   */
  limit?: number
  /**
   * 文章状态
   */
  status?: 'draft' | 'published'
  /**
   * 是否包含子分类的文章
   */
  includeChildren?: boolean
}

/**
 * 创建分类数据接口
 */
export interface CreateCategoryData {
  /**
   * 分类名称
   */
  name: string
  /**
   * 分类别名（可选）
   */
  slug?: string
  /**
   * 分类描述
   */
  description?: string
  /**
   * 父分类ID
   */
  parentId?: number
  /**
   * 排序值
   */
  sort?: number
}

/**
 * 更新分类数据接口
 */
export interface UpdateCategoryData {
  /**
   * 分类名称
   */
  name?: string
  /**
   * 分类别名
   */
  slug?: string
  /**
   * 分类描述
   */
  description?: string
  /**
   * 父分类ID
   */
  parentId?: number
  /**
   * 排序值
   */
  sort?: number
}

/**
 * 批量删除响应数据接口
 */
export interface BatchDeleteResponse {
  success: boolean
  message: string
  data: {
    deletedCount: number
    deletedIds: number[]
  }
}
