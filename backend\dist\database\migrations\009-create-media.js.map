{"version": 3, "file": "009-create-media.js", "sourceRoot": "", "sources": ["../../../src/database/migrations/009-create-media.ts"], "names": [], "mappings": ";;;AAAA,yCAAqD;AAO9C,MAAM,EAAE,GAAG,KAAK,EAAE,cAA8B,EAAiB,EAAE;IACxE,MAAM,cAAc,CAAC,WAAW,CAAC,OAAO,EAAE;QACxC,EAAE,EAAE;YACF,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,UAAU,EAAE,IAAI;YAChB,aAAa,EAAE,IAAI;YACnB,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,MAAM;SAChB;QACD,QAAQ,EAAE;YACR,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,GAAG,CAAC;YAC3B,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,KAAK;SACf;QACD,aAAa,EAAE;YACb,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,GAAG,CAAC;YAC3B,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,OAAO;SACjB;QACD,SAAS,EAAE;YACT,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,GAAG,CAAC;YAC3B,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,QAAQ;SAClB;QACD,IAAI,EAAE;YACJ,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,UAAU;SACpB;QACD,GAAG,EAAE;YACH,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,GAAG,CAAC;YAC3B,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,OAAO;SACjB;QACD,aAAa,EAAE;YACb,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,GAAG,CAAC;YAC3B,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,QAAQ;SAClB;QACD,KAAK,EAAE;YACL,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,SAAS;SACnB;QACD,MAAM,EAAE;YACN,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,SAAS;SACnB;QACD,WAAW,EAAE;YACX,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,OAAO;YAChB,UAAU,EAAE;gBACV,KAAK,EAAE,OAAO;gBACd,GAAG,EAAE,IAAI;aACV;YACD,QAAQ,EAAE,SAAS;YACnB,QAAQ,EAAE,SAAS;SACpB;QACD,QAAQ,EAAE;YACR,IAAI,EAAE,qBAAS,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,CAAC;YAC3D,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,MAAM;SAChB;QACD,IAAI,EAAE;YACJ,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,cAAc;SACxB;QACD,WAAW,EAAE;YACX,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,MAAM;SAChB;QACD,SAAS,EAAE;YACT,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,IAAI;YAClB,OAAO,EAAE,MAAM;SAChB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;YAC3B,OAAO,EAAE,MAAM;SAChB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;YAC3B,OAAO,EAAE,MAAM;SAChB;KACF,EAAE;QACD,OAAO,EAAE,SAAS;QAClB,OAAO,EAAE,oBAAoB;QAC7B,OAAO,EAAE,OAAO;KACjB,CAAC,CAAA;IAGF,MAAM,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,aAAa,CAAC,EAAE;QACtD,IAAI,EAAE,uBAAuB;KAC9B,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,UAAU,CAAC,EAAE;QACnD,IAAI,EAAE,oBAAoB;KAC3B,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,WAAW,CAAC,EAAE;QACpD,IAAI,EAAE,qBAAqB;KAC5B,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,WAAW,CAAC,EAAE;QACpD,IAAI,EAAE,qBAAqB;KAC5B,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,YAAY,CAAC,EAAE;QACrD,IAAI,EAAE,sBAAsB;KAC7B,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,aAAa,EAAE,UAAU,CAAC,EAAE;QAClE,IAAI,EAAE,6BAA6B;KACpC,CAAC,CAAA;IAEF,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAA;AACnD,CAAC,CAAA;AA7HY,QAAA,EAAE,MA6Hd;AAEM,MAAM,IAAI,GAAG,KAAK,EAAE,cAA8B,EAAiB,EAAE;IAC1E,MAAM,cAAc,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;IACvC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAA;AACtC,CAAC,CAAA;AAHY,QAAA,IAAI,QAGhB"}