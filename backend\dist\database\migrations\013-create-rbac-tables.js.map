{"version": 3, "file": "013-create-rbac-tables.js", "sourceRoot": "", "sources": ["../../../src/database/migrations/013-create-rbac-tables.ts"], "names": [], "mappings": ";;;AAAA,yCAAqD;AAO9C,MAAM,EAAE,GAAG,KAAK,EAAE,cAA8B,EAAiB,EAAE;IAExE,MAAM,cAAc,CAAC,WAAW,CAAC,OAAO,EAAE;QACxC,EAAE,EAAE;YACF,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,UAAU,EAAE,IAAI;YAChB,aAAa,EAAE,IAAI;YACnB,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,MAAM;SAChB;QACD,IAAI,EAAE;YACJ,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1B,SAAS,EAAE,KAAK;YAChB,MAAM,EAAE,IAAI;YACZ,OAAO,EAAE,MAAM;SAChB;QACD,WAAW,EAAE;YACX,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,MAAM;SAChB;QACD,SAAS,EAAE;YACT,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,IAAI;YAClB,OAAO,EAAE,MAAM;SAChB;QACD,SAAS,EAAE;YACT,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,KAAK;YACnB,OAAO,EAAE,cAAc;SACxB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;YAC3B,OAAO,EAAE,MAAM;SAChB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;YAC3B,OAAO,EAAE,MAAM;SAChB;KACF,EAAE;QACD,OAAO,EAAE,SAAS;QAClB,OAAO,EAAE,oBAAoB;QAC7B,OAAO,EAAE,KAAK;KACf,CAAC,CAAA;IAGF,MAAM,cAAc,CAAC,WAAW,CAAC,aAAa,EAAE;QAC9C,EAAE,EAAE;YACF,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,UAAU,EAAE,IAAI;YAChB,aAAa,EAAE,IAAI;YACnB,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,MAAM;SAChB;QACD,IAAI,EAAE;YACJ,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,GAAG,CAAC;YAC3B,SAAS,EAAE,KAAK;YAChB,MAAM,EAAE,IAAI;YACZ,OAAO,EAAE,MAAM;SAChB;QACD,QAAQ,EAAE;YACR,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1B,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,MAAM;SAChB;QACD,MAAM,EAAE;YACN,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1B,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,MAAM;SAChB;QACD,WAAW,EAAE;YACX,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,MAAM;SAChB;QACD,SAAS,EAAE;YACT,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,KAAK;YACnB,OAAO,EAAE,cAAc;SACxB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;YAC3B,OAAO,EAAE,MAAM;SAChB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;YAC3B,OAAO,EAAE,MAAM;SAChB;KACF,EAAE;QACD,OAAO,EAAE,SAAS;QAClB,OAAO,EAAE,oBAAoB;QAC7B,OAAO,EAAE,KAAK;KACf,CAAC,CAAA;IAGF,MAAM,cAAc,CAAC,WAAW,CAAC,YAAY,EAAE;QAC7C,EAAE,EAAE;YACF,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,UAAU,EAAE,IAAI;YAChB,aAAa,EAAE,IAAI;YACnB,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,MAAM;SAChB;QACD,OAAO,EAAE;YACP,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,MAAM;YACf,UAAU,EAAE;gBACV,KAAK,EAAE,OAAO;gBACd,GAAG,EAAE,IAAI;aACV;YACD,QAAQ,EAAE,SAAS;YACnB,QAAQ,EAAE,SAAS;SACpB;QACD,OAAO,EAAE;YACP,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,MAAM;YACf,UAAU,EAAE;gBACV,KAAK,EAAE,OAAO;gBACd,GAAG,EAAE,IAAI;aACV;YACD,QAAQ,EAAE,SAAS;YACnB,QAAQ,EAAE,SAAS;SACpB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;YAC3B,OAAO,EAAE,MAAM;SAChB;KACF,EAAE;QACD,OAAO,EAAE,SAAS;QAClB,OAAO,EAAE,oBAAoB;QAC7B,OAAO,EAAE,SAAS;KACnB,CAAC,CAAA;IAGF,MAAM,cAAc,CAAC,WAAW,CAAC,kBAAkB,EAAE;QACnD,EAAE,EAAE;YACF,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,UAAU,EAAE,IAAI;YAChB,aAAa,EAAE,IAAI;YACnB,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,MAAM;SAChB;QACD,OAAO,EAAE;YACP,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,MAAM;YACf,UAAU,EAAE;gBACV,KAAK,EAAE,OAAO;gBACd,GAAG,EAAE,IAAI;aACV;YACD,QAAQ,EAAE,SAAS;YACnB,QAAQ,EAAE,SAAS;SACpB;QACD,aAAa,EAAE;YACb,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,MAAM;YACf,UAAU,EAAE;gBACV,KAAK,EAAE,aAAa;gBACpB,GAAG,EAAE,IAAI;aACV;YACD,QAAQ,EAAE,SAAS;YACnB,QAAQ,EAAE,SAAS;SACpB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;YAC3B,OAAO,EAAE,MAAM;SAChB;KACF,EAAE;QACD,OAAO,EAAE,SAAS;QAClB,OAAO,EAAE,oBAAoB;QAC7B,OAAO,EAAE,SAAS;KACnB,CAAC,CAAA;IAGF,MAAM,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,EAAE;QAC/C,IAAI,EAAE,gBAAgB;QACtB,MAAM,EAAE,IAAI;KACb,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,WAAW,CAAC,EAAE;QACpD,IAAI,EAAE,qBAAqB;KAC5B,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC,MAAM,CAAC,EAAE;QACrD,IAAI,EAAE,sBAAsB;QAC5B,MAAM,EAAE,IAAI;KACb,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC,UAAU,CAAC,EAAE;QACzD,IAAI,EAAE,0BAA0B;KACjC,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE;QACnE,IAAI,EAAE,iCAAiC;KACxC,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,SAAS,CAAC,EAAE;QACvD,IAAI,EAAE,wBAAwB;KAC/B,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,SAAS,CAAC,EAAE;QACvD,IAAI,EAAE,wBAAwB;KAC/B,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE;QAClE,IAAI,EAAE,uBAAuB;QAC7B,MAAM,EAAE,IAAI;KACb,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC,SAAS,CAAC,EAAE;QAC7D,IAAI,EAAE,8BAA8B;KACrC,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC,eAAe,CAAC,EAAE;QACnE,IAAI,EAAE,oCAAoC;KAC3C,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC,SAAS,EAAE,eAAe,CAAC,EAAE;QAC9E,IAAI,EAAE,6BAA6B;QACnC,MAAM,EAAE,IAAI;KACb,CAAC,CAAA;IAEF,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAA;AACnD,CAAC,CAAA;AAjPY,QAAA,EAAE,MAiPd;AAEM,MAAM,IAAI,GAAG,KAAK,EAAE,cAA8B,EAAiB,EAAE;IAC1E,MAAM,cAAc,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAA;IAClD,MAAM,cAAc,CAAC,SAAS,CAAC,YAAY,CAAC,CAAA;IAC5C,MAAM,cAAc,CAAC,SAAS,CAAC,aAAa,CAAC,CAAA;IAC7C,MAAM,cAAc,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;IACvC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAA;AACtC,CAAC,CAAA;AANY,QAAA,IAAI,QAMhB"}