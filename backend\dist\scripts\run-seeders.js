#!/usr/bin/env ts-node
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const seeder_runner_1 = require("../database/seeders/seeder-runner");
async function main() {
    const runner = new seeder_runner_1.SeederRunner();
    try {
        console.log('🌱 开始执行数据库种子数据...');
        console.log('='.repeat(50));
        await runner.status();
        console.log('='.repeat(50));
        await runner.seed();
        console.log('='.repeat(50));
        console.log('🎉 数据库种子数据完成!');
        await runner.status();
    }
    catch (error) {
        console.error('❌ 种子数据执行失败:', error);
        process.exit(1);
    }
}
if (require.main === module) {
    main();
}
//# sourceMappingURL=run-seeders.js.map