"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.createTable('post_likes', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false,
            comment: '点赞ID'
        },
        post_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            comment: '说说ID',
            references: {
                model: 'posts',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        user_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            comment: '用户ID',
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '点赞时间'
        }
    }, {
        charset: 'utf8mb4',
        collate: 'utf8mb4_unicode_ci',
        comment: '说说点赞表'
    });
    await queryInterface.addIndex('post_likes', ['post_id'], {
        name: 'idx_post_likes_post_id'
    });
    await queryInterface.addIndex('post_likes', ['user_id'], {
        name: 'idx_post_likes_user_id'
    });
    await queryInterface.addIndex('post_likes', ['post_id', 'user_id'], {
        name: 'idx_post_likes_unique',
        unique: true
    });
    console.log('✅ Created post_likes table with indexes');
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.dropTable('post_likes');
    console.log('✅ Dropped post_likes table');
};
exports.down = down;
//# sourceMappingURL=008-create-post-likes.js.map