"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.createTable('roles', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false,
            comment: '角色ID'
        },
        name: {
            type: sequelize_1.DataTypes.STRING(50),
            allowNull: false,
            unique: true,
            comment: '角色名称'
        },
        description: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true,
            comment: '角色描述'
        },
        is_active: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: true,
            comment: '是否激活'
        },
        is_system: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false,
            comment: '是否系统角色（不可删除）'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '创建时间'
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '更新时间'
        }
    }, {
        charset: 'utf8mb4',
        collate: 'utf8mb4_unicode_ci',
        comment: '角色表'
    });
    await queryInterface.createTable('permissions', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false,
            comment: '权限ID'
        },
        name: {
            type: sequelize_1.DataTypes.STRING(100),
            allowNull: false,
            unique: true,
            comment: '权限名称'
        },
        resource: {
            type: sequelize_1.DataTypes.STRING(50),
            allowNull: false,
            comment: '资源名称'
        },
        action: {
            type: sequelize_1.DataTypes.STRING(50),
            allowNull: false,
            comment: '操作名称'
        },
        description: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true,
            comment: '权限描述'
        },
        is_system: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false,
            comment: '是否系统权限（不可删除）'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '创建时间'
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '更新时间'
        }
    }, {
        charset: 'utf8mb4',
        collate: 'utf8mb4_unicode_ci',
        comment: '权限表'
    });
    await queryInterface.createTable('user_roles', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false,
            comment: '关联ID'
        },
        user_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            comment: '用户ID',
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        role_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            comment: '角色ID',
            references: {
                model: 'roles',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '创建时间'
        }
    }, {
        charset: 'utf8mb4',
        collate: 'utf8mb4_unicode_ci',
        comment: '用户角色关联表'
    });
    await queryInterface.createTable('role_permissions', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false,
            comment: '关联ID'
        },
        role_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            comment: '角色ID',
            references: {
                model: 'roles',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        permission_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            comment: '权限ID',
            references: {
                model: 'permissions',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '创建时间'
        }
    }, {
        charset: 'utf8mb4',
        collate: 'utf8mb4_unicode_ci',
        comment: '角色权限关联表'
    });
    await queryInterface.addIndex('roles', ['name'], {
        name: 'idx_roles_name',
        unique: true
    });
    await queryInterface.addIndex('roles', ['is_active'], {
        name: 'idx_roles_is_active'
    });
    await queryInterface.addIndex('permissions', ['name'], {
        name: 'idx_permissions_name',
        unique: true
    });
    await queryInterface.addIndex('permissions', ['resource'], {
        name: 'idx_permissions_resource'
    });
    await queryInterface.addIndex('permissions', ['resource', 'action'], {
        name: 'idx_permissions_resource_action'
    });
    await queryInterface.addIndex('user_roles', ['user_id'], {
        name: 'idx_user_roles_user_id'
    });
    await queryInterface.addIndex('user_roles', ['role_id'], {
        name: 'idx_user_roles_role_id'
    });
    await queryInterface.addIndex('user_roles', ['user_id', 'role_id'], {
        name: 'idx_user_roles_unique',
        unique: true
    });
    await queryInterface.addIndex('role_permissions', ['role_id'], {
        name: 'idx_role_permissions_role_id'
    });
    await queryInterface.addIndex('role_permissions', ['permission_id'], {
        name: 'idx_role_permissions_permission_id'
    });
    await queryInterface.addIndex('role_permissions', ['role_id', 'permission_id'], {
        name: 'idx_role_permissions_unique',
        unique: true
    });
    console.log('✅ Created RBAC tables with indexes');
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.dropTable('role_permissions');
    await queryInterface.dropTable('user_roles');
    await queryInterface.dropTable('permissions');
    await queryInterface.dropTable('roles');
    console.log('✅ Dropped RBAC tables');
};
exports.down = down;
//# sourceMappingURL=013-create-rbac-tables.js.map