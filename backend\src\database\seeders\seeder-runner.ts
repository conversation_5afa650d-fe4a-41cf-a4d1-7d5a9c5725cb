import { QueryInterface } from 'sequelize'
import { sequelize } from '../../config/database'
import * as fs from 'fs'
import * as path from 'path'

/**
 * 种子数据运行器
 * 用于管理和执行数据库种子数据脚本
 */

interface SeederRecord {
  id: number
  name: string
  executed_at: Date
}

interface Seeder {
  name: string
  up: (queryInterface: QueryInterface) => Promise<void>
  down: (queryInterface: QueryInterface) => Promise<void>
}

export class SeederRunner {
  private queryInterface: QueryInterface

  constructor() {
    this.queryInterface = sequelize.getQueryInterface()
  }

  /**
   * 确保种子数据记录表存在
   */
  private async ensureSeedersTable(): Promise<void> {
    const tableExists = await this.queryInterface.showAllTables()
      .then(tables => tables.includes('seeders'))

    if (!tableExists) {
      await this.queryInterface.createTable('seeders', {
        id: {
          type: 'INTEGER',
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        name: {
          type: 'VARCHAR(255)',
          allowNull: false,
          unique: true
        },
        executed_at: {
          type: 'DATETIME',
          allowNull: false,
          defaultValue: new Date()
        }
      })
      console.log('✅ 创建种子数据记录表 seeders')
    }
  }

  /**
   * 获取已执行的种子数据记录
   */
  private async getExecutedSeeders(): Promise<string[]> {
    await this.ensureSeedersTable()
    
    const records = await sequelize.query(
      'SELECT name FROM seeders ORDER BY executed_at ASC',
      { type: 'SELECT' }
    ) as SeederRecord[]

    return records.map(record => record.name)
  }

  /**
   * 记录种子数据执行
   */
  private async recordSeeder(name: string): Promise<void> {
    await sequelize.query(
      'INSERT INTO seeders (name, executed_at) VALUES (?, ?)',
      {
        replacements: [name, new Date()],
        type: 'INSERT'
      }
    )
  }

  /**
   * 删除种子数据记录
   */
  private async removeSeederRecord(name: string): Promise<void> {
    await sequelize.query(
      'DELETE FROM seeders WHERE name = ?',
      {
        replacements: [name],
        type: 'DELETE'
      }
    )
  }

  /**
   * 获取所有种子数据文件
   */
  private async getSeederFiles(): Promise<string[]> {
    const seedersDir = __dirname
    const files = fs.readdirSync(seedersDir)
    
    return files
      .filter(file => file.endsWith('.ts') && file !== 'seeder-runner.ts' && /^\d{3}-/.test(file))
      .sort()
  }

  /**
   * 加载种子数据模块
   */
  private async loadSeeder(filename: string): Promise<Seeder> {
    const seederPath = path.join(__dirname, filename)
    const seeder = await import(seederPath)
    
    return {
      name: filename.replace('.ts', ''),
      up: seeder.up,
      down: seeder.down
    }
  }

  /**
   * 执行种子数据
   */
  async seed(targetSeeder?: string): Promise<void> {
    console.log('🌱 开始执行种子数据...')
    
    try {
      await sequelize.authenticate()
      console.log('✅ 数据库连接成功')

      const executedSeeders = await this.getExecutedSeeders()
      const seederFiles = await this.getSeederFiles()

      console.log(`📋 发现 ${seederFiles.length} 个种子数据文件`)
      console.log(`📋 已执行 ${executedSeeders.length} 个种子数据`)

      for (const filename of seederFiles) {
        const seederName = filename.replace('.ts', '')
        
        // 如果指定了目标种子数据，只执行到该种子数据
        if (targetSeeder && seederName === targetSeeder) {
          break
        }

        // 跳过已执行的种子数据
        if (executedSeeders.includes(seederName)) {
          console.log(`⏭️ 跳过已执行的种子数据: ${seederName}`)
          continue
        }

        console.log(`🌱 执行种子数据: ${seederName}`)
        
        const seeder = await this.loadSeeder(filename)
        await seeder.up(this.queryInterface)
        await this.recordSeeder(seederName)
        
        console.log(`✅ 种子数据完成: ${seederName}`)
      }

      console.log('🎉 所有种子数据执行完成!')
    } catch (error) {
      console.error('❌ 种子数据执行失败:', error)
      throw error
    }
  }

  /**
   * 回滚种子数据
   */
  async unseed(steps: number = 1): Promise<void> {
    console.log(`🔄 开始回滚最近 ${steps} 个种子数据...`)
    
    try {
      await sequelize.authenticate()
      console.log('✅ 数据库连接成功')

      const executedSeeders = await this.getExecutedSeeders()
      
      if (executedSeeders.length === 0) {
        console.log('ℹ️ 没有可回滚的种子数据')
        return
      }

      // 获取要回滚的种子数据（按执行顺序倒序）
      const seedersToRollback = executedSeeders
        .slice(-steps)
        .reverse()

      for (const seederName of seedersToRollback) {
        console.log(`🔄 回滚种子数据: ${seederName}`)
        
        const seeder = await this.loadSeeder(`${seederName}.ts`)
        await seeder.down(this.queryInterface)
        await this.removeSeederRecord(seederName)
        
        console.log(`✅ 回滚完成: ${seederName}`)
      }

      console.log('🎉 种子数据回滚完成!')
    } catch (error) {
      console.error('❌ 种子数据回滚失败:', error)
      throw error
    }
  }

  /**
   * 显示种子数据状态
   */
  async status(): Promise<void> {
    console.log('📊 种子数据状态:')
    
    try {
      await sequelize.authenticate()
      
      const executedSeeders = await this.getExecutedSeeders()
      const seederFiles = await this.getSeederFiles()

      console.log('\n已执行的种子数据:')
      if (executedSeeders.length === 0) {
        console.log('  (无)')
      } else {
        executedSeeders.forEach(name => {
          console.log(`  ✅ ${name}`)
        })
      }

      console.log('\n待执行的种子数据:')
      const pendingSeeders = seederFiles
        .map(file => file.replace('.ts', ''))
        .filter(name => !executedSeeders.includes(name))
      
      if (pendingSeeders.length === 0) {
        console.log('  (无)')
      } else {
        pendingSeeders.forEach(name => {
          console.log(`  ⏳ ${name}`)
        })
      }

      console.log(`\n总计: ${seederFiles.length} 个种子数据文件`)
      console.log(`已执行: ${executedSeeders.length} 个`)
      console.log(`待执行: ${pendingSeeders.length} 个`)
    } catch (error) {
      console.error('❌ 获取种子数据状态失败:', error)
      throw error
    }
  }

  /**
   * 重置种子数据（删除所有数据并重新执行）
   */
  async reset(): Promise<void> {
    console.log('🔄 重置种子数据...')
    
    try {
      // 获取所有已执行的种子数据
      const executedSeeders = await this.getExecutedSeeders()
      
      // 按倒序回滚所有种子数据
      if (executedSeeders.length > 0) {
        await this.unseed(executedSeeders.length)
      }
      
      // 重新执行所有种子数据
      await this.seed()
      
      console.log('🎉 种子数据重置完成!')
    } catch (error) {
      console.error('❌ 种子数据重置失败:', error)
      throw error
    }
  }

  /**
   * 清空所有种子数据
   */
  async clear(): Promise<void> {
    console.log('🗑️ 清空所有种子数据...')
    
    try {
      const executedSeeders = await this.getExecutedSeeders()
      
      if (executedSeeders.length > 0) {
        await this.unseed(executedSeeders.length)
      }
      
      console.log('✅ 所有种子数据已清空')
    } catch (error) {
      console.error('❌ 清空种子数据失败:', error)
      throw error
    }
  }
}

// 如果直接运行此文件，执行命令行操作
if (require.main === module) {
  const runner = new SeederRunner()
  const command = process.argv[2]
  const arg = process.argv[3]

  switch (command) {
    case 'seed':
      runner.seed(arg).catch(console.error)
      break
    case 'unseed':
      runner.unseed(arg ? parseInt(arg) : 1).catch(console.error)
      break
    case 'status':
      runner.status().catch(console.error)
      break
    case 'reset':
      runner.reset().catch(console.error)
      break
    case 'clear':
      runner.clear().catch(console.error)
      break
    default:
      console.log('用法:')
      console.log('  npm run seed                    - 执行所有种子数据')
      console.log('  npm run seed:status             - 查看种子数据状态')
      console.log('  npm run seed:unseed [steps]     - 回滚种子数据')
      console.log('  npm run seed:reset              - 重置种子数据')
      console.log('  npm run seed:clear              - 清空种子数据')
  }
}
