"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.createTable('categories', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false,
            comment: '分类ID'
        },
        name: {
            type: sequelize_1.DataTypes.STRING(100),
            allowNull: false,
            comment: '分类名称'
        },
        slug: {
            type: sequelize_1.DataTypes.STRING(100),
            allowNull: false,
            unique: true,
            comment: '分类别名（URL友好）'
        },
        description: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true,
            comment: '分类描述'
        },
        parent_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true,
            comment: '父分类ID',
            references: {
                model: 'categories',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'SET NULL'
        },
        sort: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            defaultValue: 0,
            comment: '排序权重'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '创建时间'
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '更新时间'
        }
    }, {
        charset: 'utf8mb4',
        collate: 'utf8mb4_unicode_ci',
        comment: '分类表'
    });
    await queryInterface.addIndex('categories', ['slug'], {
        name: 'idx_categories_slug',
        unique: true
    });
    await queryInterface.addIndex('categories', ['parent_id'], {
        name: 'idx_categories_parent_id'
    });
    await queryInterface.addIndex('categories', ['sort'], {
        name: 'idx_categories_sort'
    });
    await queryInterface.addIndex('categories', ['parent_id', 'sort'], {
        name: 'idx_categories_parent_sort'
    });
    console.log('✅ Created categories table with indexes');
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.dropTable('categories');
    console.log('✅ Dropped categories table');
};
exports.down = down;
//# sourceMappingURL=003-create-categories.js.map