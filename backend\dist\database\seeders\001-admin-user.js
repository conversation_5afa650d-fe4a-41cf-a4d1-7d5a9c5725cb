"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const up = async (queryInterface) => {
    console.log('Creating sample users...');
    const saltRounds = 12;
    const adminPasswordHash = await bcryptjs_1.default.hash('admin123', saltRounds);
    const userPasswordHash = await bcryptjs_1.default.hash('user123', saltRounds);
    const users = [
        {
            username: 'admin',
            email: '<EMAIL>',
            password_hash: adminPasswordHash,
            is_active: true,
            email_verified: true,
            email_verified_at: new Date('2024-01-01 10:00:00'),
            profile_picture: '/uploads/avatars/admin-avatar.jpg',
            bio: '系统管理员，负责博客系统的维护和管理。',
            website: 'https://admin.example.com',
            location: '北京',
            timezone: 'Asia/Shanghai',
            language: 'zh-CN',
            created_at: new Date('2024-01-01 10:00:00'),
            updated_at: new Date('2024-01-01 10:00:00')
        },
        {
            username: 'john_doe',
            email: '<EMAIL>',
            password_hash: userPasswordHash,
            is_active: true,
            email_verified: true,
            email_verified_at: new Date('2024-01-02 09:30:00'),
            profile_picture: '/uploads/avatars/john-avatar.jpg',
            bio: '全栈开发工程师，热爱技术分享和开源项目。',
            website: 'https://johndoe.dev',
            location: '上海',
            gender: 'male',
            timezone: 'Asia/Shanghai',
            language: 'zh-CN',
            created_at: new Date('2024-01-02 09:30:00'),
            updated_at: new Date('2024-01-02 09:30:00')
        },
        {
            username: 'jane_smith',
            email: '<EMAIL>',
            password_hash: userPasswordHash,
            is_active: true,
            email_verified: true,
            email_verified_at: new Date('2024-01-03 14:15:00'),
            profile_picture: '/uploads/avatars/jane-avatar.jpg',
            bio: '前端开发专家，专注于用户体验和界面设计。',
            website: 'https://janesmith.design',
            location: '深圳',
            gender: 'female',
            timezone: 'Asia/Shanghai',
            language: 'zh-CN',
            created_at: new Date('2024-01-03 14:15:00'),
            updated_at: new Date('2024-01-03 14:15:00')
        },
        {
            username: 'tech_writer',
            email: '<EMAIL>',
            password_hash: userPasswordHash,
            is_active: true,
            email_verified: true,
            email_verified_at: new Date('2024-01-04 11:20:00'),
            profile_picture: '/uploads/avatars/writer-avatar.jpg',
            bio: '技术作家，专注于技术文档和教程编写。',
            website: 'https://techwriter.blog',
            location: '杭州',
            gender: 'prefer_not_to_say',
            timezone: 'Asia/Shanghai',
            language: 'zh-CN',
            created_at: new Date('2024-01-04 11:20:00'),
            updated_at: new Date('2024-01-04 11:20:00')
        }
    ];
    await queryInterface.bulkInsert('users', users);
    console.log(`Created ${users.length} sample users:`);
    users.forEach(user => {
        console.log(`  • ${user.username} (${user.email})`);
    });
    console.log('');
    console.log('Default passwords:');
    console.log('  • admin: admin123');
    console.log('  • others: user123');
    console.log('⚠️  Please change default passwords in production!');
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.bulkDelete('users', {}, {});
    console.log('✅ Removed all sample users');
};
exports.down = down;
//# sourceMappingURL=001-admin-user.js.map