"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.up = up;
exports.down = down;
async function up(queryInterface) {
    console.log('🌱 创建角色权限种子数据...');
    const roles = [
        {
            id: 1,
            name: 'super_admin',
            description: '超级管理员，拥有系统所有权限',
            is_active: true,
            is_system: true,
            created_at: new Date(),
            updated_at: new Date()
        },
        {
            id: 2,
            name: 'admin',
            description: '管理员，拥有大部分管理权限',
            is_active: true,
            is_system: true,
            created_at: new Date(),
            updated_at: new Date()
        },
        {
            id: 3,
            name: 'editor',
            description: '编辑者，可以管理内容',
            is_active: true,
            is_system: true,
            created_at: new Date(),
            updated_at: new Date()
        },
        {
            id: 4,
            name: 'user',
            description: '普通用户，基础权限',
            is_active: true,
            is_system: true,
            created_at: new Date(),
            updated_at: new Date()
        }
    ];
    await queryInterface.bulkInsert('roles', roles);
    const permissions = [
        { id: 1, name: 'user.create', description: '创建用户', resource: 'user', action: 'create', is_active: true },
        { id: 2, name: 'user.read', description: '查看用户', resource: 'user', action: 'read', is_active: true },
        { id: 3, name: 'user.update', description: '更新用户', resource: 'user', action: 'update', is_active: true },
        { id: 4, name: 'user.delete', description: '删除用户', resource: 'user', action: 'delete', is_active: true },
        { id: 5, name: 'user.list', description: '用户列表', resource: 'user', action: 'list', is_active: true },
        { id: 6, name: 'article.create', description: '创建文章', resource: 'article', action: 'create', is_active: true },
        { id: 7, name: 'article.read', description: '查看文章', resource: 'article', action: 'read', is_active: true },
        { id: 8, name: 'article.update', description: '更新文章', resource: 'article', action: 'update', is_active: true },
        { id: 9, name: 'article.delete', description: '删除文章', resource: 'article', action: 'delete', is_active: true },
        { id: 10, name: 'article.list', description: '文章列表', resource: 'article', action: 'list', is_active: true },
        { id: 11, name: 'article.publish', description: '发布文章', resource: 'article', action: 'publish', is_active: true },
        { id: 12, name: 'category.create', description: '创建分类', resource: 'category', action: 'create', is_active: true },
        { id: 13, name: 'category.read', description: '查看分类', resource: 'category', action: 'read', is_active: true },
        { id: 14, name: 'category.update', description: '更新分类', resource: 'category', action: 'update', is_active: true },
        { id: 15, name: 'category.delete', description: '删除分类', resource: 'category', action: 'delete', is_active: true },
        { id: 16, name: 'category.list', description: '分类列表', resource: 'category', action: 'list', is_active: true },
        { id: 17, name: 'tag.create', description: '创建标签', resource: 'tag', action: 'create', is_active: true },
        { id: 18, name: 'tag.read', description: '查看标签', resource: 'tag', action: 'read', is_active: true },
        { id: 19, name: 'tag.update', description: '更新标签', resource: 'tag', action: 'update', is_active: true },
        { id: 20, name: 'tag.delete', description: '删除标签', resource: 'tag', action: 'delete', is_active: true },
        { id: 21, name: 'tag.list', description: '标签列表', resource: 'tag', action: 'list', is_active: true },
        { id: 22, name: 'comment.create', description: '创建评论', resource: 'comment', action: 'create', is_active: true },
        { id: 23, name: 'comment.read', description: '查看评论', resource: 'comment', action: 'read', is_active: true },
        { id: 24, name: 'comment.update', description: '更新评论', resource: 'comment', action: 'update', is_active: true },
        { id: 25, name: 'comment.delete', description: '删除评论', resource: 'comment', action: 'delete', is_active: true },
        { id: 26, name: 'comment.approve', description: '批准评论', resource: 'comment', action: 'approve', is_active: true },
        { id: 27, name: 'comment.reject', description: '拒绝评论', resource: 'comment', action: 'reject', is_active: true },
        { id: 28, name: 'post.create', description: '创建说说', resource: 'post', action: 'create', is_active: true },
        { id: 29, name: 'post.read', description: '查看说说', resource: 'post', action: 'read', is_active: true },
        { id: 30, name: 'post.update', description: '更新说说', resource: 'post', action: 'update', is_active: true },
        { id: 31, name: 'post.delete', description: '删除说说', resource: 'post', action: 'delete', is_active: true },
        { id: 32, name: 'post.like', description: '点赞说说', resource: 'post', action: 'like', is_active: true },
        { id: 33, name: 'media.upload', description: '上传媒体', resource: 'media', action: 'upload', is_active: true },
        { id: 34, name: 'media.read', description: '查看媒体', resource: 'media', action: 'read', is_active: true },
        { id: 35, name: 'media.update', description: '更新媒体', resource: 'media', action: 'update', is_active: true },
        { id: 36, name: 'media.delete', description: '删除媒体', resource: 'media', action: 'delete', is_active: true },
        { id: 37, name: 'media.list', description: '媒体列表', resource: 'media', action: 'list', is_active: true },
        { id: 38, name: 'notification.create', description: '创建通知', resource: 'notification', action: 'create', is_active: true },
        { id: 39, name: 'notification.read', description: '查看通知', resource: 'notification', action: 'read', is_active: true },
        { id: 40, name: 'notification.update', description: '更新通知', resource: 'notification', action: 'update', is_active: true },
        { id: 41, name: 'notification.delete', description: '删除通知', resource: 'notification', action: 'delete', is_active: true },
        { id: 42, name: 'role.create', description: '创建角色', resource: 'role', action: 'create', is_active: true },
        { id: 43, name: 'role.read', description: '查看角色', resource: 'role', action: 'read', is_active: true },
        { id: 44, name: 'role.update', description: '更新角色', resource: 'role', action: 'update', is_active: true },
        { id: 45, name: 'role.delete', description: '删除角色', resource: 'role', action: 'delete', is_active: true },
        { id: 46, name: 'role.assign', description: '分配角色', resource: 'role', action: 'assign', is_active: true },
        { id: 47, name: 'permission.create', description: '创建权限', resource: 'permission', action: 'create', is_active: true },
        { id: 48, name: 'permission.read', description: '查看权限', resource: 'permission', action: 'read', is_active: true },
        { id: 49, name: 'permission.update', description: '更新权限', resource: 'permission', action: 'update', is_active: true },
        { id: 50, name: 'permission.delete', description: '删除权限', resource: 'permission', action: 'delete', is_active: true },
        { id: 51, name: 'system.settings', description: '系统设置', resource: 'system', action: 'settings', is_active: true },
        { id: 52, name: 'system.audit', description: '审计日志', resource: 'system', action: 'audit', is_active: true },
        { id: 53, name: 'system.backup', description: '系统备份', resource: 'system', action: 'backup', is_active: true }
    ].map(p => ({
        ...p,
        created_at: new Date(),
        updated_at: new Date()
    }));
    await queryInterface.bulkInsert('permissions', permissions);
    console.log('✅ 角色权限种子数据创建完成');
    console.log(`   - 创建了 ${roles.length} 个角色`);
    console.log(`   - 创建了 ${permissions.length} 个权限`);
}
async function down(queryInterface) {
    await queryInterface.bulkDelete('permissions', {}, {});
    await queryInterface.bulkDelete('roles', {}, {});
}
//# sourceMappingURL=002-roles-permissions.js.map