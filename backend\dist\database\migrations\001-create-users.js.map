{"version": 3, "file": "001-create-users.js", "sourceRoot": "", "sources": ["../../../src/database/migrations/001-create-users.ts"], "names": [], "mappings": ";;;AAAA,yCAAqD;AAO9C,MAAM,EAAE,GAAG,KAAK,EAAE,cAA8B,EAAiB,EAAE;IACxE,MAAM,cAAc,CAAC,WAAW,CAAC,OAAO,EAAE;QACxC,EAAE,EAAE;YACF,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,UAAU,EAAE,IAAI;YAChB,aAAa,EAAE,IAAI;YACnB,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,MAAM;SAChB;QACD,QAAQ,EAAE;YACR,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1B,SAAS,EAAE,KAAK;YAChB,MAAM,EAAE,IAAI;YACZ,OAAO,EAAE,KAAK;SACf;QACD,KAAK,EAAE;YACL,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,GAAG,CAAC;YAC3B,SAAS,EAAE,KAAK;YAChB,MAAM,EAAE,IAAI;YACZ,OAAO,EAAE,MAAM;SAChB;QACD,aAAa,EAAE;YACb,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,GAAG,CAAC;YAC3B,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,MAAM;SAChB;QACD,SAAS,EAAE;YACT,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,IAAI;YAClB,OAAO,EAAE,MAAM;SAChB;QACD,cAAc,EAAE;YACd,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,KAAK;YACnB,OAAO,EAAE,SAAS;SACnB;QACD,iBAAiB,EAAE;YACjB,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,QAAQ;SAClB;QACD,aAAa,EAAE;YACb,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,QAAQ;SAClB;QACD,oBAAoB,EAAE;YACpB,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,GAAG,CAAC;YAC3B,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,QAAQ;SAClB;QACD,sBAAsB,EAAE;YACtB,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,YAAY;SACtB;QACD,wBAAwB,EAAE;YACxB,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,GAAG,CAAC;YAC3B,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,QAAQ;SAClB;QACD,0BAA0B,EAAE;YAC1B,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,YAAY;SACtB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;YAC3B,OAAO,EAAE,MAAM;SAChB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;YAC3B,OAAO,EAAE,MAAM;SAChB;KACF,EAAE;QACD,OAAO,EAAE,SAAS;QAClB,OAAO,EAAE,oBAAoB;QAC7B,OAAO,EAAE,KAAK;KACf,CAAC,CAAA;IAGF,MAAM,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,EAAE;QAChD,IAAI,EAAE,iBAAiB;QACvB,MAAM,EAAE,IAAI;KACb,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,UAAU,CAAC,EAAE;QACnD,IAAI,EAAE,oBAAoB;QAC1B,MAAM,EAAE,IAAI;KACb,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,WAAW,CAAC,EAAE;QACpD,IAAI,EAAE,qBAAqB;KAC5B,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,gBAAgB,CAAC,EAAE;QACzD,IAAI,EAAE,0BAA0B;KACjC,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,sBAAsB,CAAC,EAAE;QAC/D,IAAI,EAAE,gCAAgC;KACvC,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,0BAA0B,CAAC,EAAE;QACnE,IAAI,EAAE,oCAAoC;KAC3C,CAAC,CAAA;IAEF,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAA;AACnD,CAAC,CAAA;AAlHY,QAAA,EAAE,MAkHd;AAEM,MAAM,IAAI,GAAG,KAAK,EAAE,cAA8B,EAAiB,EAAE;IAC1E,MAAM,cAAc,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;IACvC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAA;AACtC,CAAC,CAAA;AAHY,QAAA,IAAI,QAGhB"}