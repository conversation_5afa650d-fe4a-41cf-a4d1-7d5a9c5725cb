#!/usr/bin/env ts-node

import { sequelize } from '../config/database'
import { SeederRunner } from '../database/seeders/seeder-runner'

/**
 * 种子数据测试脚本
 * 用于测试种子数据的正确性和完整性
 */

interface TableCount {
  tableName: string
  count: number
}

class SeederTester {
  private runner: SeederRunner

  constructor() {
    this.runner = new SeederRunner()
  }

  /**
   * 获取表的记录数量
   */
  private async getTableCount(tableName: string): Promise<number> {
    try {
      const result = await sequelize.query(
        `SELECT COUNT(*) as count FROM ${tableName}`,
        { type: 'SELECT' }
      ) as any[]
      return parseInt(result[0].count)
    } catch (error) {
      console.warn(`⚠️ 无法获取表 ${tableName} 的记录数:`, error)
      return 0
    }
  }

  /**
   * 验证数据完整性
   */
  private async validateDataIntegrity(): Promise<void> {
    console.log('🔍 验证数据完整性...')

    // 验证用户数据
    const userCount = await this.getTableCount('users')
    const settingsCount = await this.getTableCount('settings')
    
    if (userCount !== settingsCount) {
      throw new Error(`用户数量 (${userCount}) 与设置数量 (${settingsCount}) 不匹配`)
    }

    // 验证角色权限数据
    const roleCount = await this.getTableCount('roles')
    const permissionCount = await this.getTableCount('permissions')
    const rolePermissionCount = await this.getTableCount('role_permissions')
    
    if (roleCount === 0 || permissionCount === 0 || rolePermissionCount === 0) {
      throw new Error('角色权限数据不完整')
    }

    // 验证文章标签关联
    const articleCount = await this.getTableCount('articles')
    const tagCount = await this.getTableCount('tags')
    const articleTagCount = await this.getTableCount('article_tags')
    
    if (articleCount > 0 && articleTagCount === 0) {
      throw new Error('文章存在但没有标签关联')
    }

    // 验证评论数据
    const commentCount = await this.getTableCount('comments')
    if (commentCount === 0) {
      console.warn('⚠️ 没有评论数据')
    }

    console.log('✅ 数据完整性验证通过')
  }

  /**
   * 验证数据关联关系
   */
  private async validateRelationships(): Promise<void> {
    console.log('🔍 验证数据关联关系...')

    // 验证文章作者关联
    const articlesWithoutAuthor = await sequelize.query(
      `SELECT COUNT(*) as count FROM articles 
       WHERE author_id NOT IN (SELECT id FROM users)`,
      { type: 'SELECT' }
    ) as any[]

    if (parseInt(articlesWithoutAuthor[0].count) > 0) {
      throw new Error('存在没有有效作者的文章')
    }

    // 验证评论关联
    const commentsWithoutAuthor = await sequelize.query(
      `SELECT COUNT(*) as count FROM comments 
       WHERE author_id NOT IN (SELECT id FROM users)`,
      { type: 'SELECT' }
    ) as any[]

    if (parseInt(commentsWithoutAuthor[0].count) > 0) {
      throw new Error('存在没有有效作者的评论')
    }

    // 验证用户角色关联
    const userRolesWithoutUser = await sequelize.query(
      `SELECT COUNT(*) as count FROM user_roles 
       WHERE user_id NOT IN (SELECT id FROM users)`,
      { type: 'SELECT' }
    ) as any[]

    if (parseInt(userRolesWithoutUser[0].count) > 0) {
      throw new Error('存在没有有效用户的用户角色关联')
    }

    console.log('✅ 数据关联关系验证通过')
  }

  /**
   * 验证数据质量
   */
  private async validateDataQuality(): Promise<void> {
    console.log('🔍 验证数据质量...')

    // 验证用户邮箱唯一性
    const duplicateEmails = await sequelize.query(
      `SELECT email, COUNT(*) as count FROM users 
       GROUP BY email HAVING COUNT(*) > 1`,
      { type: 'SELECT' }
    ) as any[]

    if (duplicateEmails.length > 0) {
      throw new Error(`存在重复的用户邮箱: ${duplicateEmails.map(e => e.email).join(', ')}`)
    }

    // 验证文章slug唯一性
    const duplicateSlugs = await sequelize.query(
      `SELECT slug, COUNT(*) as count FROM articles 
       GROUP BY slug HAVING COUNT(*) > 1`,
      { type: 'SELECT' }
    ) as any[]

    if (duplicateSlugs.length > 0) {
      throw new Error(`存在重复的文章slug: ${duplicateSlugs.map(s => s.slug).join(', ')}`)
    }

    // 验证标签名称唯一性
    const duplicateTagNames = await sequelize.query(
      `SELECT name, COUNT(*) as count FROM tags 
       GROUP BY name HAVING COUNT(*) > 1`,
      { type: 'SELECT' }
    ) as any[]

    if (duplicateTagNames.length > 0) {
      throw new Error(`存在重复的标签名称: ${duplicateTagNames.map(t => t.name).join(', ')}`)
    }

    console.log('✅ 数据质量验证通过')
  }

  /**
   * 显示数据统计
   */
  private async showDataStatistics(): Promise<void> {
    console.log('📊 数据统计:')

    const tables = [
      'users', 'settings', 'roles', 'permissions', 'user_roles', 'role_permissions',
      'categories', 'tags', 'articles', 'article_tags', 'posts', 'post_likes',
      'comments', 'media', 'notifications', 'notification_preferences', 'audit_logs'
    ]

    const statistics: TableCount[] = []

    for (const table of tables) {
      const count = await this.getTableCount(table)
      statistics.push({ tableName: table, count })
    }

    // 按表名排序并显示
    statistics.sort((a, b) => a.tableName.localeCompare(b.tableName))
    
    console.log('\n表名                     | 记录数')
    console.log('------------------------|-------')
    statistics.forEach(stat => {
      const paddedName = stat.tableName.padEnd(23)
      const paddedCount = stat.count.toString().padStart(6)
      console.log(`${paddedName} | ${paddedCount}`)
    })

    const totalRecords = statistics.reduce((sum, stat) => sum + stat.count, 0)
    console.log('------------------------|-------')
    console.log(`${'总计'.padEnd(23)} | ${totalRecords.toString().padStart(6)}`)
  }

  /**
   * 运行完整测试
   */
  async runTests(): Promise<void> {
    console.log('🧪 开始种子数据测试...')
    console.log('=' .repeat(50))

    try {
      // 连接数据库
      await sequelize.authenticate()
      console.log('✅ 数据库连接成功')

      // 执行种子数据
      console.log('\n🌱 执行种子数据...')
      await this.runner.seed()

      // 验证数据
      console.log('\n🔍 开始验证...')
      await this.validateDataIntegrity()
      await this.validateRelationships()
      await this.validateDataQuality()

      // 显示统计信息
      console.log('\n📊 显示统计信息...')
      await this.showDataStatistics()

      console.log('\n' + '=' .repeat(50))
      console.log('🎉 所有测试通过!')

      // 显示最终状态
      await this.runner.status()

    } catch (error) {
      console.error('\n❌ 测试失败:', error)
      throw error
    }
  }

  /**
   * 测试种子数据回滚
   */
  async testRollback(): Promise<void> {
    console.log('🔄 测试种子数据回滚...')

    try {
      // 执行回滚
      await this.runner.unseed(2) // 回滚最近2个种子数据
      console.log('✅ 种子数据回滚成功')

      // 重新执行种子数据
      await this.runner.seed()
      console.log('✅ 种子数据重新执行成功')

    } catch (error) {
      console.error('❌ 回滚测试失败:', error)
      throw error
    }
  }
}

// 主函数
async function main() {
  const tester = new SeederTester()
  
  try {
    await tester.runTests()
    
    // 如果指定了回滚测试参数
    if (process.argv.includes('--test-rollback')) {
      await tester.testRollback()
    }
    
    console.log('\n🎉 种子数据测试完成!')
  } catch (error) {
    console.error('\n❌ 种子数据测试失败:', error)
    process.exit(1)
  } finally {
    await sequelize.close()
  }
}

// 执行测试
if (require.main === module) {
  main()
}
