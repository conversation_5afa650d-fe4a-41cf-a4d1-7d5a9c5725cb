import { QueryInterface, DataTypes } from 'sequelize'
import { sequelize } from '../../config/database'
import * as fs from 'fs'
import * as path from 'path'

/**
 * 迁移运行器
 * 用于管理和执行数据库迁移脚本
 */

interface MigrationRecord {
  id: number
  name: string
  executed_at: Date
}

interface Migration {
  name: string
  up: (queryInterface: QueryInterface) => Promise<void>
  down: (queryInterface: QueryInterface) => Promise<void>
}

export class MigrationRunner {
  private queryInterface: QueryInterface

  constructor() {
    this.queryInterface = sequelize.getQueryInterface()
  }

  /**
   * 确保迁移记录表存在
   */
  private async ensureMigrationsTable(): Promise<void> {
    const tableExists = await this.queryInterface.showAllTables()
      .then(tables => tables.includes('migrations'))

    if (!tableExists) {
      await this.queryInterface.createTable('migrations', {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          allowNull: false
        },
        name: {
          type: DataTypes.STRING(255),
          allowNull: false,
          unique: true
        },
        executed_at: {
          type: DataTypes.DATE,
          allowNull: false,
          defaultValue: DataTypes.NOW
        }
      })
      console.log('✅ 创建迁移记录表 migrations')
    }
  }

  /**
   * 获取已执行的迁移记录
   */
  private async getExecutedMigrations(): Promise<string[]> {
    await this.ensureMigrationsTable()
    
    const records = await sequelize.query(
      'SELECT name FROM migrations ORDER BY executed_at ASC',
      { type: 'SELECT' }
    ) as MigrationRecord[]

    return records.map(record => record.name)
  }

  /**
   * 记录迁移执行
   */
  private async recordMigration(name: string): Promise<void> {
    await sequelize.query(
      'INSERT INTO migrations (name, executed_at) VALUES (?, ?)',
      {
        replacements: [name, new Date()],
        type: 'INSERT'
      }
    )
  }

  /**
   * 删除迁移记录
   */
  private async removeMigrationRecord(name: string): Promise<void> {
    await sequelize.query(
      'DELETE FROM migrations WHERE name = ?',
      {
        replacements: [name],
        type: 'DELETE'
      }
    )
  }

  /**
   * 获取所有迁移文件
   */
  private async getMigrationFiles(): Promise<string[]> {
    const migrationsDir = __dirname
    const files = fs.readdirSync(migrationsDir)
    
    return files
      .filter(file => file.endsWith('.ts') && file !== 'migration-runner.ts')
      .sort()
  }

  /**
   * 加载迁移模块
   */
  private async loadMigration(filename: string): Promise<Migration> {
    const migrationPath = path.join(__dirname, filename)
    const migration = await import(migrationPath)
    
    return {
      name: filename.replace('.ts', ''),
      up: migration.up,
      down: migration.down
    }
  }

  /**
   * 执行向上迁移
   */
  async up(targetMigration?: string): Promise<void> {
    console.log('🚀 开始执行数据库迁移...')
    
    try {
      await sequelize.authenticate()
      console.log('✅ 数据库连接成功')

      const executedMigrations = await this.getExecutedMigrations()
      const migrationFiles = await this.getMigrationFiles()

      console.log(`📋 发现 ${migrationFiles.length} 个迁移文件`)
      console.log(`📋 已执行 ${executedMigrations.length} 个迁移`)

      for (const filename of migrationFiles) {
        const migrationName = filename.replace('.ts', '')
        
        // 如果指定了目标迁移，只执行到该迁移
        if (targetMigration && migrationName === targetMigration) {
          break
        }

        // 跳过已执行的迁移
        if (executedMigrations.includes(migrationName)) {
          console.log(`⏭️ 跳过已执行的迁移: ${migrationName}`)
          continue
        }

        console.log(`🔄 执行迁移: ${migrationName}`)
        
        const migration = await this.loadMigration(filename)
        await migration.up(this.queryInterface)
        await this.recordMigration(migrationName)
        
        console.log(`✅ 迁移完成: ${migrationName}`)
      }

      console.log('🎉 所有迁移执行完成!')
    } catch (error) {
      console.error('❌ 迁移执行失败:', error)
      throw error
    }
  }

  /**
   * 执行向下迁移（回滚）
   */
  async down(steps: number = 1): Promise<void> {
    console.log(`🔄 开始回滚最近 ${steps} 个迁移...`)
    
    try {
      await sequelize.authenticate()
      console.log('✅ 数据库连接成功')

      const executedMigrations = await this.getExecutedMigrations()
      
      if (executedMigrations.length === 0) {
        console.log('ℹ️ 没有可回滚的迁移')
        return
      }

      // 获取要回滚的迁移（按执行顺序倒序）
      const migrationsToRollback = executedMigrations
        .slice(-steps)
        .reverse()

      for (const migrationName of migrationsToRollback) {
        console.log(`🔄 回滚迁移: ${migrationName}`)
        
        const migration = await this.loadMigration(`${migrationName}.ts`)
        await migration.down(this.queryInterface)
        await this.removeMigrationRecord(migrationName)
        
        console.log(`✅ 回滚完成: ${migrationName}`)
      }

      console.log('🎉 迁移回滚完成!')
    } catch (error) {
      console.error('❌ 迁移回滚失败:', error)
      throw error
    }
  }

  /**
   * 显示迁移状态
   */
  async status(): Promise<void> {
    console.log('📊 迁移状态:')
    
    try {
      await sequelize.authenticate()
      
      const executedMigrations = await this.getExecutedMigrations()
      const migrationFiles = await this.getMigrationFiles()

      console.log('\n已执行的迁移:')
      if (executedMigrations.length === 0) {
        console.log('  (无)')
      } else {
        executedMigrations.forEach(name => {
          console.log(`  ✅ ${name}`)
        })
      }

      console.log('\n待执行的迁移:')
      const pendingMigrations = migrationFiles
        .map(file => file.replace('.ts', ''))
        .filter(name => !executedMigrations.includes(name))
      
      if (pendingMigrations.length === 0) {
        console.log('  (无)')
      } else {
        pendingMigrations.forEach(name => {
          console.log(`  ⏳ ${name}`)
        })
      }

      console.log(`\n总计: ${migrationFiles.length} 个迁移文件`)
      console.log(`已执行: ${executedMigrations.length} 个`)
      console.log(`待执行: ${pendingMigrations.length} 个`)
    } catch (error) {
      console.error('❌ 获取迁移状态失败:', error)
      throw error
    }
  }

  /**
   * 重置数据库（删除所有表并重新执行迁移）
   */
  async reset(): Promise<void> {
    console.log('🔄 重置数据库...')
    
    try {
      // 获取所有已执行的迁移
      const executedMigrations = await this.getExecutedMigrations()
      
      // 按倒序回滚所有迁移
      if (executedMigrations.length > 0) {
        await this.down(executedMigrations.length)
      }
      
      // 重新执行所有迁移
      await this.up()
      
      console.log('🎉 数据库重置完成!')
    } catch (error) {
      console.error('❌ 数据库重置失败:', error)
      throw error
    }
  }
}

// 如果直接运行此文件，执行命令行操作
if (require.main === module) {
  const runner = new MigrationRunner()
  const command = process.argv[2]
  const arg = process.argv[3]

  switch (command) {
    case 'up':
      runner.up(arg).catch(console.error)
      break
    case 'down':
      runner.down(arg ? parseInt(arg) : 1).catch(console.error)
      break
    case 'status':
      runner.status().catch(console.error)
      break
    case 'reset':
      runner.reset().catch(console.error)
      break
    default:
      console.log('用法:')
      console.log('  npm run migrate up [migration_name]  - 执行迁移')
      console.log('  npm run migrate down [steps]        - 回滚迁移')
      console.log('  npm run migrate status              - 查看迁移状态')
      console.log('  npm run migrate reset               - 重置数据库')
  }
}
