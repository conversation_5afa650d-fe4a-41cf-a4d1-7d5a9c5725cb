"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.createTable('settings', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false,
            comment: '设置ID'
        },
        key: {
            type: sequelize_1.DataTypes.STRING(100),
            allowNull: false,
            unique: true,
            comment: '设置键名'
        },
        value: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true,
            comment: '设置值'
        },
        type: {
            type: sequelize_1.DataTypes.ENUM('string', 'number', 'boolean', 'json'),
            allowNull: false,
            defaultValue: 'string',
            comment: '值类型'
        },
        category: {
            type: sequelize_1.DataTypes.STRING(50),
            allowNull: false,
            defaultValue: 'general',
            comment: '设置分类'
        },
        description: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true,
            comment: '设置描述'
        },
        is_public: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false,
            comment: '是否公开（前端可访问）'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '创建时间'
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '更新时间'
        }
    }, {
        charset: 'utf8mb4',
        collate: 'utf8mb4_unicode_ci',
        comment: '系统设置表'
    });
    await queryInterface.addIndex('settings', ['key'], {
        name: 'idx_settings_key',
        unique: true
    });
    await queryInterface.addIndex('settings', ['category'], {
        name: 'idx_settings_category'
    });
    await queryInterface.addIndex('settings', ['is_public'], {
        name: 'idx_settings_is_public'
    });
    await queryInterface.addIndex('settings', ['category', 'is_public'], {
        name: 'idx_settings_category_public'
    });
    console.log('✅ Created settings table with indexes');
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.dropTable('settings');
    console.log('✅ Dropped settings table');
};
exports.down = down;
//# sourceMappingURL=012-create-settings.js.map