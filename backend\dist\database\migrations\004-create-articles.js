"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.createTable('articles', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false,
            comment: '文章ID'
        },
        title: {
            type: sequelize_1.DataTypes.STRING(200),
            allowNull: false,
            comment: '文章标题'
        },
        slug: {
            type: sequelize_1.DataTypes.STRING(200),
            allowNull: false,
            unique: true,
            comment: '文章别名（URL友好）'
        },
        content: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: false,
            comment: '文章内容（Markdown格式）'
        },
        excerpt: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true,
            comment: '文章摘要'
        },
        status: {
            type: sequelize_1.DataTypes.ENUM('draft', 'published'),
            allowNull: false,
            defaultValue: 'draft',
            comment: '文章状态'
        },
        author_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            comment: '作者ID',
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        category_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true,
            comment: '分类ID',
            references: {
                model: 'categories',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'SET NULL'
        },
        published_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: true,
            comment: '发布时间'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '创建时间'
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '更新时间'
        }
    }, {
        charset: 'utf8mb4',
        collate: 'utf8mb4_unicode_ci',
        comment: '文章表'
    });
    await queryInterface.addIndex('articles', ['slug'], {
        name: 'idx_articles_slug',
        unique: true
    });
    await queryInterface.addIndex('articles', ['author_id'], {
        name: 'idx_articles_author_id'
    });
    await queryInterface.addIndex('articles', ['category_id'], {
        name: 'idx_articles_category_id'
    });
    await queryInterface.addIndex('articles', ['status'], {
        name: 'idx_articles_status'
    });
    await queryInterface.addIndex('articles', ['published_at'], {
        name: 'idx_articles_published_at'
    });
    await queryInterface.addIndex('articles', ['status', 'published_at'], {
        name: 'idx_articles_status_published'
    });
    await queryInterface.addIndex('articles', ['author_id', 'status'], {
        name: 'idx_articles_author_status'
    });
    console.log('✅ Created articles table with indexes');
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.dropTable('articles');
    console.log('✅ Dropped articles table');
};
exports.down = down;
//# sourceMappingURL=004-create-articles.js.map