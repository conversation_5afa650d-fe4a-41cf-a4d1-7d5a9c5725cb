{"version": 3, "file": "010-create-notifications.js", "sourceRoot": "", "sources": ["../../../src/database/migrations/010-create-notifications.ts"], "names": [], "mappings": ";;;AAAA,yCAAqD;AAO9C,MAAM,EAAE,GAAG,KAAK,EAAE,cAA8B,EAAiB,EAAE;IACxE,MAAM,cAAc,CAAC,WAAW,CAAC,eAAe,EAAE;QAChD,EAAE,EAAE;YACF,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,UAAU,EAAE,IAAI;YAChB,aAAa,EAAE,IAAI;YACnB,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,MAAM;SAChB;QACD,IAAI,EAAE;YACJ,IAAI,EAAE,qBAAS,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC;YACtE,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,MAAM;SAChB;QACD,KAAK,EAAE;YACL,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,GAAG,CAAC;YAC3B,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,MAAM;SAChB;QACD,OAAO,EAAE;YACP,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,MAAM;SAChB;QACD,YAAY,EAAE;YACZ,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,OAAO;YAChB,UAAU,EAAE;gBACV,KAAK,EAAE,OAAO;gBACd,GAAG,EAAE,IAAI;aACV;YACD,QAAQ,EAAE,SAAS;YACnB,QAAQ,EAAE,SAAS;SACpB;QACD,SAAS,EAAE;YACT,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,gBAAgB;YACzB,UAAU,EAAE;gBACV,KAAK,EAAE,OAAO;gBACd,GAAG,EAAE,IAAI;aACV;YACD,QAAQ,EAAE,SAAS;YACnB,QAAQ,EAAE,UAAU;SACrB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,qBAAqB;SAC/B;QACD,YAAY,EAAE;YACZ,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1B,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,2BAA2B;SACrC;QACD,OAAO,EAAE;YACP,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,KAAK;YACnB,OAAO,EAAE,MAAM;SAChB;QACD,OAAO,EAAE;YACP,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,MAAM;SAChB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;YAC3B,OAAO,EAAE,MAAM;SAChB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;YAC3B,OAAO,EAAE,MAAM;SAChB;KACF,EAAE;QACD,OAAO,EAAE,SAAS;QAClB,OAAO,EAAE,oBAAoB;QAC7B,OAAO,EAAE,KAAK;KACf,CAAC,CAAA;IAGF,MAAM,cAAc,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC,cAAc,CAAC,EAAE;QAC/D,IAAI,EAAE,gCAAgC;KACvC,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC,WAAW,CAAC,EAAE;QAC5D,IAAI,EAAE,6BAA6B;KACpC,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC,MAAM,CAAC,EAAE;QACvD,IAAI,EAAE,wBAAwB;KAC/B,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC,SAAS,CAAC,EAAE;QAC1D,IAAI,EAAE,2BAA2B;KAClC,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC,cAAc,EAAE,SAAS,CAAC,EAAE;QAC1E,IAAI,EAAE,kCAAkC;KACzC,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC,cAAc,EAAE,YAAY,CAAC,EAAE;QAC7E,IAAI,EAAE,2BAA2B;KAClC,CAAC,CAAA;IAEF,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAA;AAC3D,CAAC,CAAA;AA/GY,QAAA,EAAE,MA+Gd;AAEM,MAAM,IAAI,GAAG,KAAK,EAAE,cAA8B,EAAiB,EAAE;IAC1E,MAAM,cAAc,CAAC,SAAS,CAAC,eAAe,CAAC,CAAA;IAC/C,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAA;AAC9C,CAAC,CAAA;AAHY,QAAA,IAAI,QAGhB"}