import { request } from '@/utils/request'

/**
 * 用户设置接口定义
 */
export interface UserSettings {
  id: number
  userId: number

  // 个人信息设置
  displayName?: string
  avatar?: string
  bio?: string
  website?: string
  location?: string

  // 偏好设置
  theme: 'light' | 'dark' | 'auto'
  language: string
  timezone: string
  itemsPerPage: number

  // 通知设置
  emailNotifications: boolean
  commentNotifications: boolean
  systemNotifications: boolean

  // 隐私设置
  profileVisibility: 'public' | 'private'
  defaultPostVisibility: 'public' | 'private'
  showEmail: boolean

  // 安全设置
  twoFactorEnabled: boolean

  createdAt: string
  updatedAt: string
}

/**
 * 设置更新参数接口
 */
export interface SettingsUpdateParams {
  // 个人信息设置
  displayName?: string
  avatar?: string
  bio?: string
  website?: string
  location?: string

  // 偏好设置
  theme?: 'light' | 'dark' | 'auto'
  language?: string
  timezone?: string
  itemsPerPage?: number

  // 通知设置
  emailNotifications?: boolean
  commentNotifications?: boolean
  systemNotifications?: boolean

  // 隐私设置
  profileVisibility?: 'public' | 'private'
  defaultPostVisibility?: 'public' | 'private'
  showEmail?: boolean

  // 安全设置
  twoFactorEnabled?: boolean
}

/**
 * 用户完整信息接口（包含设置）
 */
export interface UserProfile {
  id: number
  username: string
  email: string
  createdAt: string
  updatedAt: string
  settings: UserSettings
}

/**
 * API响应接口
 */
export interface SettingsResponse {
  success: boolean
  message: string
  data: UserSettings
}

export interface UserProfileResponse {
  success: boolean
  message: string
  data: UserProfile
}

export interface DefaultSettingsResponse {
  success: boolean
  message: string
  data: Partial<UserSettings>
}

export interface ValidationResponse {
  success: boolean
  message: string
  errors?: string[]
}

/**
 * 设置服务模块，提供设置相关的API接口封装
 */
export const settingsService = {
  /**
   * 获取当前用户的设置
   * @returns 返回用户设置信息的Promise对象
   */
  async getSettings(): Promise<UserSettings> {
    const response = await request.get<SettingsResponse>('/settings', {
      loadingMessage: '正在加载设置...'
    })
    return response.data.data
  },

  /**
   * 更新用户设置
   * @param settings 要更新的设置数据
   * @returns 返回更新后的设置信息的Promise对象
   */
  async updateSettings(settings: SettingsUpdateParams): Promise<UserSettings> {
    const response = await request.put<SettingsResponse>('/settings', settings, {
      loadingMessage: '正在保存设置...'
    })
    return response.data.data
  },

  /**
   * 重置设置为默认值
   * @returns 返回重置后的设置信息的Promise对象
   */
  async resetSettings(): Promise<UserSettings> {
    const response = await request.post<SettingsResponse>('/settings/reset', {}, {
      loadingMessage: '正在重置设置...'
    })
    return response.data.data
  },

  /**
   * 获取用户完整信息（包含设置）
   * @returns 返回用户完整信息的Promise对象
   */
  async getUserProfile(): Promise<UserProfile> {
    const response = await request.get<UserProfileResponse>('/settings/profile', {
      loadingMessage: '正在加载用户信息...'
    })
    return response.data.data
  },

  /**
   * 获取默认设置值
   * @returns 返回默认设置的Promise对象
   */
  async getDefaultSettings(): Promise<Partial<UserSettings>> {
    const response = await request.get<{ data: Partial<UserSettings> }>('/settings/defaults', {
      loadingMessage: '正在加载默认设置...'
    })
    return response.data.data
  }
},

  /**
   * 验证设置数据
   * @param settings 要验证的设置数据
   * @returns 返回验证结果的Promise对象
   */
  async validateSettings(settings: SettingsUpdateParams): Promise<ValidationResponse> {
    const response = await request.post<ValidationResponse>('/settings/validate', settings, {
      loadingMessage: '正在验证设置...'
    })
    return response.data
  },

    /**
     * 批量更新多个设置项
     * @param settingsArray 设置项数组
     * @returns 返回更新后的设置信息的Promise对象
     */
    async batchUpdateSettings(settingsArray: SettingsUpdateParams[]): Promise < UserSettings > {
      // 将多个设置项合并为一个对象
      const mergedSettings = settingsArray.reduce((acc, settings) => {
        return { ...acc, ...settings }
      }, {})

    return this.updateSettings(mergedSettings)
    },

      /**
       * 获取设置项的可选值
       * @returns 返回设置项可选值的对象
       */
      getSettingsOptions() {
  return {
    themes: [
      { value: 'light', label: '浅色主题' },
      { value: 'dark', label: '深色主题' },
      { value: 'auto', label: '跟随系统' }
    ],
    languages: [
      { value: 'zh-CN', label: '简体中文' },
      { value: 'zh-TW', label: '繁体中文' },
      { value: 'en-US', label: 'English' },
      { value: 'ja-JP', label: '日本語' },
      { value: 'ko-KR', label: '한국어' }
    ],
    timezones: [
      { value: 'Asia/Shanghai', label: '北京时间 (UTC+8)' },
      { value: 'Asia/Tokyo', label: '东京时间 (UTC+9)' },
      { value: 'America/New_York', label: '纽约时间 (UTC-5)' },
      { value: 'Europe/London', label: '伦敦时间 (UTC+0)' },
      { value: 'UTC', label: '协调世界时 (UTC)' }
    ],
    itemsPerPageOptions: [
      { value: 5, label: '5条/页' },
      { value: 10, label: '10条/页' },
      { value: 20, label: '20条/页' },
      { value: 50, label: '50条/页' },
      { value: 100, label: '100条/页' }
    ],
    visibilityOptions: [
      { value: 'public', label: '公开' },
      { value: 'private', label: '私有' }
    ]
  }
}
}
