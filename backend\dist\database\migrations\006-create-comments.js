"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.createTable('comments', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false,
            comment: '评论ID'
        },
        content: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: false,
            comment: '评论内容'
        },
        status: {
            type: sequelize_1.DataTypes.ENUM('pending', 'approved', 'rejected'),
            allowNull: false,
            defaultValue: 'pending',
            comment: '评论状态'
        },
        article_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true,
            comment: '文章ID（文章评论时使用）',
            references: {
                model: 'articles',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        post_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true,
            comment: '说说ID（说说评论时使用）',
            references: {
                model: 'posts',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        author_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            comment: '评论作者ID',
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        parent_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true,
            comment: '父评论ID（回复评论时使用）',
            references: {
                model: 'comments',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '创建时间'
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '更新时间'
        }
    }, {
        charset: 'utf8mb4',
        collate: 'utf8mb4_unicode_ci',
        comment: '评论表'
    });
    await queryInterface.addIndex('comments', ['article_id'], {
        name: 'idx_comments_article_id'
    });
    await queryInterface.addIndex('comments', ['post_id'], {
        name: 'idx_comments_post_id'
    });
    await queryInterface.addIndex('comments', ['author_id'], {
        name: 'idx_comments_author_id'
    });
    await queryInterface.addIndex('comments', ['parent_id'], {
        name: 'idx_comments_parent_id'
    });
    await queryInterface.addIndex('comments', ['status'], {
        name: 'idx_comments_status'
    });
    await queryInterface.addIndex('comments', ['article_id', 'status'], {
        name: 'idx_comments_article_status'
    });
    await queryInterface.addIndex('comments', ['post_id', 'status'], {
        name: 'idx_comments_post_status'
    });
    console.log('✅ Created comments table with indexes');
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.dropTable('comments');
    console.log('✅ Dropped comments table');
};
exports.down = down;
//# sourceMappingURL=006-create-comments.js.map