import { request } from '@/utils/request'
import type { Article, ArticlesResponse, ArticleParams } from '@/types/article'

/**
 * 文章服务模块，提供文章相关的API接口封装
 */
export const articleService = {
  /**
   * 获取文章列表
   * @param params 查询参数，包括分页、标签、状态、搜索关键词等
   * @returns 返回包含文章列表和分页信息的Promise对象
   */
  async getArticles(params: ArticleParams = {}): Promise<ArticlesResponse> {
    try {
      const response = await request.get<ArticlesResponse>('/articles', {
        params,
        loadingMessage: '正在加载文章列表...'
      })
      return {
        success: response.data.success || true,
        articles: response.data.articles || [],
        pagination: response.data.pagination || {
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 1
        }
      }
    } catch (error) {
      console.error('获取文章列表失败:', error)
      // 返回空数据而不是抛出错误
      return {
        success: false,
        articles: [],
        pagination: {
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 1
        }
      }
    }
  },

  /**
   * 获取单个文章详情
   * @param id 文章ID
   * @returns 返回指定文章的详细信息
   */
  async getArticle(id: number): Promise<Article> {
    const response = await request.get<{ data: { article: Article } }>(`/articles/${id}`, {
      loadingMessage: '正在加载文章内容...'
    })
    return response.data.data.article
  },

  /**
   * 创建新文章
   * @param articleData 文章数据对象，包含文章的基本信息
   * @returns 返回创建成功的文章对象
   */
  async createArticle(articleData: Partial<Article>): Promise<Article> {
    const response = await request.post<{ data: { article: Article } }>('/articles', articleData, {
      loadingMessage: '正在创建文章...'
    })
    return response.data.data.article
  },

  /**
   * 更新文章信息
   * @param id 要更新的文章ID
   * @param articleData 更新的文章数据
   * @returns 返回更新后的文章对象
   */
  async updateArticle(id: number, articleData: Partial<Article>): Promise<Article> {
    const response = await request.put<{ data: { article: Article } }>(`/articles/${id}`, articleData, {
      loadingMessage: '正在更新文章...'
    })
    return response.data.data.article
  },

  /**
   * 删除指定文章
   * @param id 要删除的文章ID
   */
  async deleteArticle(id: number): Promise<void> {
    await request.delete(`/articles/${id}`, {
      loadingMessage: '正在删除文章...'
    })
  },

  /**
   * 批量删除文章
   * @param ids 要删除的文章ID数组
   */
  async deleteArticles(ids: number[]): Promise<void> {
    await request.post('/articles/batch-delete', { ids }, {
      loadingMessage: '正在批量删除文章...'
    })
  },

  /**
   * 获取文章的上一篇和下一篇导航信息
   * @param id 当前文章ID
   * @returns 包含上一篇和下一篇信息的对象
   */
  async getArticleNavigation(id: number): Promise<{
    prev: { id: number; title: string; slug: string } | null
    next: { id: number; title: string; slug: string } | null
  }> {
    const response = await request.get<{ data: { navigation: any } }>(`/articles/${id}/navigation`)
    return response.data.data.navigation
  },

  /**
   * 根据分类获取文章列表
   * @param categoryId 分类ID或slug
   * @param params 查询参数
   * @returns 返回包含文章列表和分页信息的Promise对象
   */
  async getArticlesByCategory(categoryId: string | number, params: Omit<ArticleParams, 'category'> = {}): Promise<ArticlesResponse> {
    const response = await request.get<ArticlesResponse>(`/categories/${categoryId}/articles`, {
      params,
      loadingMessage: '正在加载分类文章...'
    })
    return {
      success: response.data.success || true,
      articles: response.data.articles || [],
      pagination: response.data.pagination || {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 1
      }
    }
  },

  /**
   * 根据标签获取文章列表
   * @param tagName 标签名称
   * @param params 查询参数
   * @returns 返回包含文章列表和分页信息的Promise对象
   */
  async getArticlesByTag(tagName: string, params: Omit<ArticleParams, 'tag'> = {}): Promise<ArticlesResponse> {
    const response = await request.get<ArticlesResponse>(`/tags/${tagName}/articles`, {
      params,
      loadingMessage: '正在加载标签文章...'
    })
    return {
      success: response.data.success || true,
      articles: response.data.articles || [],
      pagination: response.data.pagination || {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 1
      }
    }
  },

  /**
   * 发布文章
   * @param id 文章ID
   * @returns 返回发布后的文章对象
   */
  async publishArticle(id: number): Promise<Article> {
    const response = await request.post<{ data: { article: Article } }>(`/articles/${id}/publish`, {}, {
      loadingMessage: '正在发布文章...'
    })
    return response.data.data.article
  },

  /**
   * 取消发布文章
   * @param id 文章ID
   * @returns 返回取消发布后的文章对象
   */
  async unpublishArticle(id: number): Promise<Article> {
    const response = await request.post<{ data: { article: Article } }>(`/articles/${id}/unpublish`, {}, {
      loadingMessage: '正在取消发布文章...'
    })
    return response.data.data.article
  }
}

export default articleService
