"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.createTable('audit_logs', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false,
            comment: '日志ID'
        },
        user_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true,
            comment: '操作用户ID',
            references: {
                model: 'users',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'SET NULL'
        },
        action: {
            type: sequelize_1.DataTypes.STRING(100),
            allowNull: false,
            comment: '操作类型'
        },
        resource: {
            type: sequelize_1.DataTypes.STRING(100),
            allowNull: false,
            comment: '操作资源'
        },
        resource_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: true,
            comment: '资源ID'
        },
        old_values: {
            type: sequelize_1.DataTypes.JSON,
            allowNull: true,
            comment: '修改前的值（JSON格式）'
        },
        new_values: {
            type: sequelize_1.DataTypes.JSON,
            allowNull: true,
            comment: '修改后的值（JSON格式）'
        },
        ip_address: {
            type: sequelize_1.DataTypes.STRING(45),
            allowNull: true,
            comment: 'IP地址'
        },
        user_agent: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true,
            comment: '用户代理'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '创建时间'
        }
    }, {
        charset: 'utf8mb4',
        collate: 'utf8mb4_unicode_ci',
        comment: '审计日志表'
    });
    await queryInterface.addIndex('audit_logs', ['user_id'], {
        name: 'idx_audit_logs_user_id'
    });
    await queryInterface.addIndex('audit_logs', ['action'], {
        name: 'idx_audit_logs_action'
    });
    await queryInterface.addIndex('audit_logs', ['resource'], {
        name: 'idx_audit_logs_resource'
    });
    await queryInterface.addIndex('audit_logs', ['resource', 'resource_id'], {
        name: 'idx_audit_logs_resource_id'
    });
    await queryInterface.addIndex('audit_logs', ['created_at'], {
        name: 'idx_audit_logs_created_at'
    });
    await queryInterface.addIndex('audit_logs', ['user_id', 'created_at'], {
        name: 'idx_audit_logs_user_created'
    });
    console.log('✅ Created audit_logs table with indexes');
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.dropTable('audit_logs');
    console.log('✅ Dropped audit_logs table');
};
exports.down = down;
//# sourceMappingURL=014-create-audit-logs.js.map