"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.addColumn('users', 'login_attempts', {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '登录尝试次数'
    });
    await queryInterface.addColumn('users', 'locked_until', {
        type: sequelize_1.DataTypes.DATE,
        allowNull: true,
        comment: '账户锁定到期时间'
    });
    await queryInterface.addColumn('users', 'profile_picture', {
        type: sequelize_1.DataTypes.STRING(500),
        allowNull: true,
        comment: '头像URL'
    });
    await queryInterface.addColumn('users', 'bio', {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
        comment: '个人简介'
    });
    await queryInterface.addColumn('users', 'website', {
        type: sequelize_1.DataTypes.STRING(200),
        allowNull: true,
        comment: '个人网站'
    });
    await queryInterface.addColumn('users', 'location', {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: true,
        comment: '所在地'
    });
    await queryInterface.addColumn('users', 'birth_date', {
        type: sequelize_1.DataTypes.DATEONLY,
        allowNull: true,
        comment: '出生日期'
    });
    await queryInterface.addColumn('users', 'gender', {
        type: sequelize_1.DataTypes.ENUM('male', 'female', 'other', 'prefer_not_to_say'),
        allowNull: true,
        comment: '性别'
    });
    await queryInterface.addColumn('users', 'timezone', {
        type: sequelize_1.DataTypes.STRING(50),
        allowNull: true,
        defaultValue: 'UTC',
        comment: '时区'
    });
    await queryInterface.addColumn('users', 'language', {
        type: sequelize_1.DataTypes.STRING(10),
        allowNull: true,
        defaultValue: 'zh-CN',
        comment: '语言偏好'
    });
    await queryInterface.addIndex('users', ['locked_until'], {
        name: 'idx_users_locked_until'
    });
    await queryInterface.addIndex('users', ['location'], {
        name: 'idx_users_location'
    });
    await queryInterface.addIndex('users', ['gender'], {
        name: 'idx_users_gender'
    });
    console.log('✅ Added user status fields and indexes');
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.removeColumn('users', 'language');
    await queryInterface.removeColumn('users', 'timezone');
    await queryInterface.removeColumn('users', 'gender');
    await queryInterface.removeColumn('users', 'birth_date');
    await queryInterface.removeColumn('users', 'location');
    await queryInterface.removeColumn('users', 'website');
    await queryInterface.removeColumn('users', 'bio');
    await queryInterface.removeColumn('users', 'profile_picture');
    await queryInterface.removeColumn('users', 'locked_until');
    await queryInterface.removeColumn('users', 'login_attempts');
    console.log('✅ Removed user status fields');
};
exports.down = down;
//# sourceMappingURL=015-add-user-status-fields.js.map