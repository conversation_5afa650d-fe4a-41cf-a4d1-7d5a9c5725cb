"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.up = up;
exports.down = down;
async function up(queryInterface) {
    console.log('🌱 创建审计日志种子数据...');
    const auditLogs = [
        {
            id: 1,
            user_id: 1,
            action: 'login',
            resource: 'auth',
            resource_id: null,
            old_data: null,
            new_data: JSON.stringify({
                ip_address: '*************',
                user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                login_time: new Date()
            }),
            ip_address: '*************',
            user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            session_id: 'sess_admin_001',
            status: 'success',
            duration: 150,
            created_at: new Date(),
            updated_at: new Date()
        },
        {
            id: 2,
            user_id: 2,
            action: 'create',
            resource: 'article',
            resource_id: 1,
            old_data: null,
            new_data: JSON.stringify({
                title: 'React Hooks 完全指南：从入门到精通',
                status: 'draft',
                category_id: 9
            }),
            ip_address: '*************',
            user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            session_id: 'sess_john_001',
            status: 'success',
            duration: 2500,
            created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        },
        {
            id: 3,
            user_id: 2,
            action: 'update',
            resource: 'article',
            resource_id: 1,
            old_data: JSON.stringify({
                status: 'draft',
                published_at: null
            }),
            new_data: JSON.stringify({
                status: 'published',
                published_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
            }),
            ip_address: '*************',
            user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            session_id: 'sess_john_001',
            status: 'success',
            duration: 800,
            created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000 + 30 * 60 * 1000),
            updated_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000 + 30 * 60 * 1000)
        },
        {
            id: 4,
            user_id: 3,
            action: 'create',
            resource: 'comment',
            resource_id: 1,
            old_data: null,
            new_data: JSON.stringify({
                content: '这篇 React Hooks 教程写得很详细！',
                article_id: 1,
                status: 'approved'
            }),
            ip_address: '*************',
            user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            session_id: 'sess_jane_001',
            status: 'success',
            duration: 1200,
            created_at: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000)
        },
        {
            id: 5,
            user_id: 2,
            action: 'create',
            resource: 'post',
            resource_id: 1,
            old_data: null,
            new_data: JSON.stringify({
                content: '今天学习了 React Hooks，感觉打开了新世界的大门！',
                visibility: 'public',
                location: '北京市朝阳区'
            }),
            ip_address: '*************',
            user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            session_id: 'sess_john_002',
            status: 'success',
            duration: 900,
            created_at: new Date(Date.now() - 6 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 6 * 60 * 60 * 1000)
        },
        {
            id: 6,
            user_id: 3,
            action: 'create',
            resource: 'post_like',
            resource_id: 1,
            old_data: null,
            new_data: JSON.stringify({
                post_id: 1,
                user_id: 3
            }),
            ip_address: '*************',
            user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            session_id: 'sess_jane_002',
            status: 'success',
            duration: 200,
            created_at: new Date(Date.now() - 4 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 4 * 60 * 60 * 1000)
        },
        {
            id: 7,
            user_id: 2,
            action: 'upload',
            resource: 'media',
            resource_id: 1,
            old_data: null,
            new_data: JSON.stringify({
                filename: 'react-hooks-diagram.png',
                size: 245760,
                mime_type: 'image/png'
            }),
            ip_address: '*************',
            user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            session_id: 'sess_john_001',
            status: 'success',
            duration: 3200,
            created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000 + 15 * 60 * 1000),
            updated_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000 + 15 * 60 * 1000)
        },
        {
            id: 8,
            user_id: 1,
            action: 'assign_role',
            resource: 'user_role',
            resource_id: 2,
            old_data: null,
            new_data: JSON.stringify({
                user_id: 2,
                role_id: 3,
                role_name: 'editor'
            }),
            ip_address: '*************',
            user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            session_id: 'sess_admin_001',
            status: 'success',
            duration: 500,
            created_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
        },
        {
            id: 9,
            user_id: 4,
            action: 'login_failed',
            resource: 'auth',
            resource_id: null,
            old_data: null,
            new_data: JSON.stringify({
                email: '<EMAIL>',
                reason: 'invalid_password',
                attempt_count: 1
            }),
            ip_address: '*************',
            user_agent: 'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36',
            session_id: null,
            status: 'failed',
            error_message: '密码错误',
            duration: 100,
            created_at: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000)
        },
        {
            id: 10,
            user_id: 4,
            action: 'login',
            resource: 'auth',
            resource_id: null,
            old_data: null,
            new_data: JSON.stringify({
                ip_address: '*************',
                user_agent: 'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36',
                login_time: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000 + 5 * 60 * 1000)
            }),
            ip_address: '*************',
            user_agent: 'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
            session_id: 'sess_mike_001',
            status: 'success',
            duration: 180,
            created_at: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000 + 5 * 60 * 1000),
            updated_at: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000 + 5 * 60 * 1000)
        },
        {
            id: 11,
            user_id: 1,
            action: 'update',
            resource: 'user',
            resource_id: 4,
            old_data: JSON.stringify({
                email_verified: false
            }),
            new_data: JSON.stringify({
                email_verified: true,
                email_verified_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
            }),
            ip_address: '*************',
            user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            session_id: 'sess_admin_002',
            status: 'success',
            duration: 300,
            created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        },
        {
            id: 12,
            user_id: 2,
            action: 'delete',
            resource: 'comment',
            resource_id: 999,
            old_data: JSON.stringify({
                content: '这是一条垃圾评论',
                status: 'rejected'
            }),
            new_data: null,
            ip_address: '*************',
            user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            session_id: 'sess_john_003',
            status: 'success',
            duration: 250,
            created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
        },
        {
            id: 13,
            user_id: 1,
            action: 'system_backup',
            resource: 'system',
            resource_id: null,
            old_data: null,
            new_data: JSON.stringify({
                backup_type: 'full',
                backup_size: '2.5GB',
                backup_location: '/backups/2024-01-15-full.sql'
            }),
            ip_address: '*************',
            user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            session_id: 'sess_admin_003',
            status: 'success',
            duration: 45000,
            created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
        },
        {
            id: 14,
            user_id: 3,
            action: 'update',
            resource: 'settings',
            resource_id: 3,
            old_data: JSON.stringify({
                theme: 'light',
                email_notifications: true
            }),
            new_data: JSON.stringify({
                theme: 'auto',
                email_notifications: false
            }),
            ip_address: '*************',
            user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            session_id: 'sess_jane_003',
            status: 'success',
            duration: 400,
            created_at: new Date(Date.now() - 2 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 2 * 60 * 60 * 1000)
        },
        {
            id: 15,
            user_id: 2,
            action: 'export',
            resource: 'article',
            resource_id: null,
            old_data: null,
            new_data: JSON.stringify({
                export_format: 'pdf',
                article_count: 5,
                export_size: '1.2MB'
            }),
            ip_address: '*************',
            user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            session_id: 'sess_john_004',
            status: 'success',
            duration: 8000,
            created_at: new Date(Date.now() - 30 * 60 * 1000),
            updated_at: new Date(Date.now() - 30 * 60 * 1000)
        }
    ];
    await queryInterface.bulkInsert('audit_logs', auditLogs);
    console.log('✅ 审计日志种子数据创建完成');
    console.log(`   - 创建了 ${auditLogs.length} 条审计日志`);
    console.log('   - 包含登录、创建、更新、删除、上传等各种操作记录');
}
async function down(queryInterface) {
    await queryInterface.bulkDelete('audit_logs', {}, {});
}
//# sourceMappingURL=008-audit-logs.js.map