/**
 * 审计日志接口定义
 */
export interface AuditLog {
  id: number
  userId?: number
  username?: string
  action: string
  resource: string
  resourceId?: number
  oldData?: string
  newData?: string
  ipAddress?: string
  userAgent?: string
  sessionId?: string
  status: 'success' | 'failed' | 'pending'
  errorMessage?: string
  duration?: number
  createdAt: string
  updatedAt: string
  user?: {
    id: number
    username: string
    email: string
  }
}

/**
 * 审计日志列表查询参数接口
 */
export interface AuditLogListParams {
  page?: number
  limit?: number
  userId?: number
  username?: string
  action?: string
  resource?: string
  status?: string
  startDate?: string
  endDate?: string
  ipAddress?: string
  orderBy?: string
  orderDirection?: 'ASC' | 'DESC'
}

/**
 * 审计日志列表响应接口
 */
export interface AuditLogListResponse {
  logs: AuditLog[]
  total: number
  totalPages: number
  currentPage: number
}

/**
 * 审计日志统计信息接口
 */
export interface AuditLogStats {
  totalLogs: number
  successLogs: number
  failedLogs: number
  averageDuration: number
  userStats: Array<{
    userId: number
    username: string
    count: number
  }>
  topActions: Array<{
    action: string
    count: number
  }>
  topResources: Array<{
    resource: string
    count: number
  }>
  hourlyStats: Array<{
    hour: number
    count: number
  }>
  statusDistribution: {
    success: number
    failed: number
    pending: number
  }
}

/**
 * 我的审计日志统计信息接口
 */
export interface MyAuditLogStats {
  totalLogs: number
  successLogs: number
  failedLogs: number
  averageDuration: number
  actionStats: Record<string, number>
  resourceStats: Record<string, number>
  dailyStats: Array<{
    date: string
    count: number
  }>
  hourlyStats: Array<{
    hour: number
    count: number
  }>
}

/**
 * 清理预览结果接口
 */
export interface CleanupPreviewResult {
  deleteCount: number
  deleteBeforeDate: string
}

/**
 * 清理结果接口
 */
export interface CleanupResult {
  deletedCount: number
  retentionDays: number
  cleanupTime: string
}
