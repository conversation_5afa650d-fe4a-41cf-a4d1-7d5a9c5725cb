#!/usr/bin/env ts-node

import { MigrationRunner } from '../database/migrations/migration-runner'

/**
 * 迁移执行脚本
 * 用于在开发和生产环境中执行数据库迁移
 */

async function main() {
  const runner = new MigrationRunner()
  
  try {
    console.log('🚀 开始执行数据库迁移...')
    console.log('=' .repeat(50))
    
    // 显示当前迁移状态
    await runner.status()
    console.log('=' .repeat(50))
    
    // 执行所有待执行的迁移
    await runner.up()
    
    console.log('=' .repeat(50))
    console.log('🎉 数据库迁移完成!')
    
    // 再次显示迁移状态确认
    await runner.status()
    
  } catch (error) {
    console.error('❌ 迁移执行失败:', error)
    process.exit(1)
  }
}

// 执行主函数
if (require.main === module) {
  main()
}
