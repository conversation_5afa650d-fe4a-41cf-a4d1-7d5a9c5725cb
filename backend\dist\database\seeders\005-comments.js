"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const up = async (queryInterface) => {
    console.log('Creating sample comments...');
    const [users] = await queryInterface.sequelize.query('SELECT id, username FROM users ORDER BY id');
    const [articles] = await queryInterface.sequelize.query('SELECT id, title FROM articles WHERE status = "published" ORDER BY id');
    const adminUser = users.find((u) => u.username === 'admin');
    const johnUser = users.find((u) => u.username === 'john_doe');
    const janeUser = users.find((u) => u.username === 'jane_smith');
    const writerUser = users.find((u) => u.username === 'tech_writer');
    const comments = [
        {
            id: 1,
            content: '欢迎来到博客世界！这个博客的设计很棒，期待更多精彩内容。',
            author_id: johnUser?.id || 2,
            article_id: articles[0]?.id || 1,
            parent_id: null,
            status: 'approved',
            author_name: '<PERSON>e',
            author_email: '<EMAIL>',
            author_ip: '*************',
            user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            created_at: new Date('2024-01-10 15:30:00'),
            updated_at: new Date('2024-01-10 15:30:00')
        },
        {
            id: 2,
            content: '感谢你的支持！我会继续努力分享更多有价值的内容。',
            author_id: adminUser?.id || 1,
            article_id: articles[0]?.id || 1,
            parent_id: 1,
            status: 'approved',
            author_name: 'Admin',
            author_email: '<EMAIL>',
            author_ip: '***********',
            user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            created_at: new Date('2024-01-10 16:00:00'),
            updated_at: new Date('2024-01-10 16:00:00')
        },
        {
            id: 3,
            content: '技术栈选择很不错，Vue.js + Node.js 的组合确实很适合个人博客。',
            author_id: janeUser?.id || 3,
            article_id: articles[0]?.id || 1,
            parent_id: null,
            status: 'approved',
            author_name: 'Jane Smith',
            author_email: '<EMAIL>',
            author_ip: '***********01',
            user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            created_at: new Date('2024-01-11 09:15:00'),
            updated_at: new Date('2024-01-11 09:15:00')
        },
        {
            id: 4,
            content: 'TypeScript 的最佳实践总结得很全面！特别是 Result 模式的错误处理，很实用。',
            author_id: writerUser?.id || 4,
            article_id: articles[1]?.id || 2,
            parent_id: null,
            status: 'approved',
            author_name: 'Tech Writer',
            author_email: '<EMAIL>',
            author_ip: '***********02',
            user_agent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
            created_at: new Date('2024-01-12 18:45:00'),
            updated_at: new Date('2024-01-12 18:45:00')
        },
        {
            id: 5,
            content: '同意！Result 模式确实比传统的 try-catch 更优雅。',
            author_id: janeUser?.id || 3,
            article_id: articles[1]?.id || 2,
            parent_id: 4,
            status: 'approved',
            author_name: 'Jane Smith',
            author_email: '<EMAIL>',
            author_ip: '***********01',
            user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            created_at: new Date('2024-01-12 19:20:00'),
            updated_at: new Date('2024-01-12 19:20:00')
        },
        {
            id: 6,
            content: '能否分享一下在大型项目中使用 TypeScript 的经验？',
            author_id: johnUser?.id || 2,
            article_id: articles[1]?.id || 2,
            parent_id: null,
            status: 'approved',
            author_name: 'John Doe',
            author_email: '<EMAIL>',
            author_ip: '*************',
            user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            created_at: new Date('2024-01-13 10:30:00'),
            updated_at: new Date('2024-01-13 10:30:00')
        },
        {
            id: 7,
            content: 'Node.js 入门指南写得很详细，对新手很友好！',
            author_id: janeUser?.id || 3,
            article_id: articles[2]?.id || 3,
            parent_id: null,
            status: 'approved',
            author_name: 'Jane Smith',
            author_email: '<EMAIL>',
            author_ip: '***********01',
            user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            created_at: new Date('2024-01-15 14:20:00'),
            updated_at: new Date('2024-01-15 14:20:00')
        },
        {
            id: 8,
            content: 'JWT 认证部分讲解得很清楚，正好解决了我的问题。',
            author_id: johnUser?.id || 2,
            article_id: articles[2]?.id || 3,
            parent_id: null,
            status: 'approved',
            author_name: 'John Doe',
            author_email: '<EMAIL>',
            author_ip: '*************',
            user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            created_at: new Date('2024-01-16 11:45:00'),
            updated_at: new Date('2024-01-16 11:45:00')
        },
        {
            id: 9,
            content: 'React Hooks 确实改变了我们编写组件的方式，useEffect 的依赖数组需要特别注意。',
            author_id: writerUser?.id || 4,
            article_id: articles[3]?.id || 4,
            parent_id: null,
            status: 'approved',
            author_name: 'Tech Writer',
            author_email: '<EMAIL>',
            author_ip: '***********02',
            user_agent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
            created_at: new Date('2024-01-18 20:15:00'),
            updated_at: new Date('2024-01-18 20:15:00')
        },
        {
            id: 10,
            content: '自定义 Hook 的例子很实用，可以很好地复用逻辑。',
            author_id: johnUser?.id || 2,
            article_id: articles[3]?.id || 4,
            parent_id: null,
            status: 'approved',
            author_name: 'John Doe',
            author_email: '<EMAIL>',
            author_ip: '*************',
            user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            created_at: new Date('2024-01-19 09:30:00'),
            updated_at: new Date('2024-01-19 09:30:00')
        },
        {
            id: 11,
            content: 'MySQL 优化技巧很实用！索引优化部分特别有帮助。',
            author_id: janeUser?.id || 3,
            article_id: articles[4]?.id || 5,
            parent_id: null,
            status: 'approved',
            author_name: 'Jane Smith',
            author_email: '<EMAIL>',
            author_ip: '***********01',
            user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            created_at: new Date('2024-01-20 16:45:00'),
            updated_at: new Date('2024-01-20 16:45:00')
        },
        {
            id: 12,
            content: '能否分享一下分库分表的经验？',
            author_id: johnUser?.id || 2,
            article_id: articles[4]?.id || 5,
            parent_id: null,
            status: 'pending',
            author_name: 'John Doe',
            author_email: '<EMAIL>',
            author_ip: '*************',
            user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            created_at: new Date('2024-01-21 10:20:00'),
            updated_at: new Date('2024-01-21 10:20:00')
        },
        {
            id: 13,
            content: 'Docker 部署指南很详细，多阶段构建的技巧很有用！',
            author_id: writerUser?.id || 4,
            article_id: articles[5]?.id || 6,
            parent_id: null,
            status: 'approved',
            author_name: 'Tech Writer',
            author_email: '<EMAIL>',
            author_ip: '***********02',
            user_agent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
            created_at: new Date('2024-01-22 18:30:00'),
            updated_at: new Date('2024-01-22 18:30:00')
        },
        {
            id: 14,
            content: 'Docker Compose 的配置示例很实用，正好用在我的项目中。',
            author_id: janeUser?.id || 3,
            article_id: articles[5]?.id || 6,
            parent_id: null,
            status: 'approved',
            author_name: 'Jane Smith',
            author_email: '<EMAIL>',
            author_ip: '***********01',
            user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            created_at: new Date('2024-01-23 14:15:00'),
            updated_at: new Date('2024-01-23 14:15:00')
        },
        {
            id: 15,
            content: '这是一条垃圾评论，包含不当内容...',
            author_id: null,
            article_id: articles[0]?.id || 1,
            parent_id: null,
            status: 'rejected',
            author_name: 'Spammer',
            author_email: '<EMAIL>',
            author_ip: '*************',
            user_agent: 'Bot/1.0',
            created_at: new Date('2024-01-24 03:45:00'),
            updated_at: new Date('2024-01-24 08:30:00')
        }
    ];
    await queryInterface.bulkInsert('comments', comments);
    console.log(`✅ Created ${comments.length} sample comments:`);
    console.log('  • Approved comments: 13');
    console.log('  • Pending comments: 1');
    console.log('  • Rejected comments: 1');
    console.log('  • Reply comments: 2');
    console.log('  • Comments across 6 published articles');
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.bulkDelete('comments', {}, {});
    console.log('✅ Removed all sample comments');
};
exports.down = down;
//# sourceMappingURL=005-comments.js.map