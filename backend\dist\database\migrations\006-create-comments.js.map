{"version": 3, "file": "006-create-comments.js", "sourceRoot": "", "sources": ["../../../src/database/migrations/006-create-comments.ts"], "names": [], "mappings": ";;;AAAA,yCAAqD;AAO9C,MAAM,EAAE,GAAG,KAAK,EAAE,cAA8B,EAAiB,EAAE;IACxE,MAAM,cAAc,CAAC,WAAW,CAAC,UAAU,EAAE;QAC3C,EAAE,EAAE;YACF,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,UAAU,EAAE,IAAI;YAChB,aAAa,EAAE,IAAI;YACnB,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,MAAM;SAChB;QACD,OAAO,EAAE;YACP,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,MAAM;SAChB;QACD,MAAM,EAAE;YACN,IAAI,EAAE,qBAAS,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC;YACvD,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,SAAS;YACvB,OAAO,EAAE,MAAM;SAChB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,eAAe;YACxB,UAAU,EAAE;gBACV,KAAK,EAAE,UAAU;gBACjB,GAAG,EAAE,IAAI;aACV;YACD,QAAQ,EAAE,SAAS;YACnB,QAAQ,EAAE,SAAS;SACpB;QACD,OAAO,EAAE;YACP,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,eAAe;YACxB,UAAU,EAAE;gBACV,KAAK,EAAE,OAAO;gBACd,GAAG,EAAE,IAAI;aACV;YACD,QAAQ,EAAE,SAAS;YACnB,QAAQ,EAAE,SAAS;SACpB;QACD,SAAS,EAAE;YACT,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,QAAQ;YACjB,UAAU,EAAE;gBACV,KAAK,EAAE,OAAO;gBACd,GAAG,EAAE,IAAI;aACV;YACD,QAAQ,EAAE,SAAS;YACnB,QAAQ,EAAE,SAAS;SACpB;QACD,SAAS,EAAE;YACT,IAAI,EAAE,qBAAS,CAAC,OAAO;YACvB,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,gBAAgB;YACzB,UAAU,EAAE;gBACV,KAAK,EAAE,UAAU;gBACjB,GAAG,EAAE,IAAI;aACV;YACD,QAAQ,EAAE,SAAS;YACnB,QAAQ,EAAE,SAAS;SACpB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;YAC3B,OAAO,EAAE,MAAM;SAChB;QACD,UAAU,EAAE;YACV,IAAI,EAAE,qBAAS,CAAC,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,qBAAS,CAAC,GAAG;YAC3B,OAAO,EAAE,MAAM;SAChB;KACF,EAAE;QACD,OAAO,EAAE,SAAS;QAClB,OAAO,EAAE,oBAAoB;QAC7B,OAAO,EAAE,KAAK;KACf,CAAC,CAAA;IAGF,MAAM,cAAc,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,YAAY,CAAC,EAAE;QACxD,IAAI,EAAE,yBAAyB;KAChC,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,SAAS,CAAC,EAAE;QACrD,IAAI,EAAE,sBAAsB;KAC7B,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,WAAW,CAAC,EAAE;QACvD,IAAI,EAAE,wBAAwB;KAC/B,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,WAAW,CAAC,EAAE;QACvD,IAAI,EAAE,wBAAwB;KAC/B,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,EAAE;QACpD,IAAI,EAAE,qBAAqB;KAC5B,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,YAAY,EAAE,QAAQ,CAAC,EAAE;QAClE,IAAI,EAAE,6BAA6B;KACpC,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC,EAAE;QAC/D,IAAI,EAAE,0BAA0B;KACjC,CAAC,CAAA;IAEF,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAA;AACtD,CAAC,CAAA;AAhHY,QAAA,EAAE,MAgHd;AAEM,MAAM,IAAI,GAAG,KAAK,EAAE,cAA8B,EAAiB,EAAE;IAC1E,MAAM,cAAc,CAAC,SAAS,CAAC,UAAU,CAAC,CAAA;IAC1C,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAA;AACzC,CAAC,CAAA;AAHY,QAAA,IAAI,QAGhB"}