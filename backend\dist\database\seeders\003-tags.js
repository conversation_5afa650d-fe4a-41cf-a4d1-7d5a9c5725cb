"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const slug_1 = require("../../utils/slug");
const up = async (queryInterface) => {
    console.log('Creating sample tags...');
    const sampleTags = [
        'JavaScript',
        'TypeScript',
        'React',
        'Vue.js',
        'Angular',
        'CSS',
        'HTML',
        'Sass',
        'Less',
        'Webpack',
        'Vite',
        'ESLint',
        'Node.js',
        'Express',
        'Koa',
        'Python',
        'Django',
        'Flask',
        'Java',
        'Spring',
        'PHP',
        'Laravel',
        'MySQL',
        'PostgreSQL',
        'MongoDB',
        'Redis',
        'SQLite',
        'Docker',
        'Kubernetes',
        'AWS',
        'Azure',
        'Git',
        'GitHub',
        'GitLab',
        'Jenkins',
        'CI/CD',
        '前端开发',
        '后端开发',
        '全栈开发',
        'Web开发',
        '移动开发',
        '数据库',
        '云计算',
        '微服务',
        'API设计',
        '性能优化',
        '代码规范',
        '项目管理',
        '敏捷开发',
        '测试驱动开发',
        '持续集成',
        '开源项目',
        '技术分享',
        '学习笔记',
        '最佳实践',
        '架构设计'
    ];
    const tags = sampleTags.map((tagName, index) => ({
        id: index + 1,
        name: tagName,
        slug: (0, slug_1.generateSlug)(tagName),
        created_at: new Date(`2024-01-06 ${String(9 + Math.floor(index / 10)).padStart(2, '0')}:${String((index % 10) * 6).padStart(2, '0')}:00`)
    }));
    await queryInterface.bulkInsert('tags', tags);
    console.log(`✅ Created ${tags.length} sample tags:`);
    console.log('  • Technical tags (English): JavaScript, React, Node.js, etc.');
    console.log('  • Technical tags (Chinese): 前端开发, 后端开发, 全栈开发, etc.');
    console.log('  • Tools and platforms: Docker, AWS, Git, etc.');
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.bulkDelete('tags', {}, {});
    console.log('✅ Removed all sample tags');
};
exports.down = down;
//# sourceMappingURL=003-tags.js.map