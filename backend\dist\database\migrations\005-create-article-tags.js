"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.createTable('article_tags', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false,
            comment: '关联ID'
        },
        article_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            comment: '文章ID',
            references: {
                model: 'articles',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        tag_id: {
            type: sequelize_1.DataTypes.INTEGER,
            allowNull: false,
            comment: '标签ID',
            references: {
                model: 'tags',
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '创建时间'
        }
    }, {
        charset: 'utf8mb4',
        collate: 'utf8mb4_unicode_ci',
        comment: '文章标签关联表'
    });
    await queryInterface.addIndex('article_tags', ['article_id'], {
        name: 'idx_article_tags_article_id'
    });
    await queryInterface.addIndex('article_tags', ['tag_id'], {
        name: 'idx_article_tags_tag_id'
    });
    await queryInterface.addIndex('article_tags', ['article_id', 'tag_id'], {
        name: 'idx_article_tags_unique',
        unique: true
    });
    console.log('✅ Created article_tags table with indexes');
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.dropTable('article_tags');
    console.log('✅ Dropped article_tags table');
};
exports.down = down;
//# sourceMappingURL=005-create-article-tags.js.map