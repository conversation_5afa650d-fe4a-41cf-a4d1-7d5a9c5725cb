import { QueryInterface } from 'sequelize'

/**
 * 分类和标签种子数据
 * 创建层级分类结构和常用标签
 */

export async function up(queryInterface: QueryInterface): Promise<void> {
  console.log('🌱 创建分类和标签种子数据...')

  // 创建分类数据（支持层级结构）
  const categories = [
    // 一级分类
    {
      id: 1,
      name: '技术分享',
      slug: 'technology',
      description: '技术相关的文章和教程',
      parent_id: null,
      sort: 1,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 2,
      name: '生活随笔',
      slug: 'life',
      description: '生活感悟和日常记录',
      parent_id: null,
      sort: 2,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 3,
      name: '学习笔记',
      slug: 'study',
      description: '学习过程中的笔记和总结',
      parent_id: null,
      sort: 3,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 4,
      name: '项目经验',
      slug: 'projects',
      description: '项目开发经验和案例分享',
      parent_id: null,
      sort: 4,
      created_at: new Date(),
      updated_at: new Date()
    },

    // 技术分享的二级分类
    {
      id: 5,
      name: '前端开发',
      slug: 'frontend',
      description: '前端技术相关内容',
      parent_id: 1,
      sort: 1,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 6,
      name: '后端开发',
      slug: 'backend',
      description: '后端技术相关内容',
      parent_id: 1,
      sort: 2,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 7,
      name: '数据库',
      slug: 'database',
      description: '数据库设计和优化',
      parent_id: 1,
      sort: 3,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 8,
      name: '运维部署',
      slug: 'devops',
      description: '运维和部署相关技术',
      parent_id: 1,
      sort: 4,
      created_at: new Date(),
      updated_at: new Date()
    },

    // 前端开发的三级分类
    {
      id: 9,
      name: 'React',
      slug: 'react',
      description: 'React框架相关技术',
      parent_id: 5,
      sort: 1,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 10,
      name: 'Vue.js',
      slug: 'vue',
      description: 'Vue.js框架相关技术',
      parent_id: 5,
      sort: 2,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 11,
      name: 'TypeScript',
      slug: 'typescript',
      description: 'TypeScript语言相关内容',
      parent_id: 5,
      sort: 3,
      created_at: new Date(),
      updated_at: new Date()
    },

    // 后端开发的三级分类
    {
      id: 12,
      name: 'Node.js',
      slug: 'nodejs',
      description: 'Node.js相关技术',
      parent_id: 6,
      sort: 1,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 13,
      name: 'Python',
      slug: 'python',
      description: 'Python语言相关内容',
      parent_id: 6,
      sort: 2,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 14,
      name: 'Java',
      slug: 'java',
      description: 'Java语言相关内容',
      parent_id: 6,
      sort: 3,
      created_at: new Date(),
      updated_at: new Date()
    }
  ]

  await queryInterface.bulkInsert('categories', categories)

  // 创建标签数据
  const tags = [
    // 技术标签
    { id: 1, name: 'JavaScript', slug: 'javascript', created_at: new Date() },
    { id: 2, name: 'TypeScript', slug: 'typescript', created_at: new Date() },
    { id: 3, name: 'React', slug: 'react', created_at: new Date() },
    { id: 4, name: 'Vue.js', slug: 'vue', created_at: new Date() },
    { id: 5, name: 'Node.js', slug: 'nodejs', created_at: new Date() },
    { id: 6, name: 'Express', slug: 'express', created_at: new Date() },
    { id: 7, name: 'Koa', slug: 'koa', created_at: new Date() },
    { id: 8, name: 'MySQL', slug: 'mysql', created_at: new Date() },
    { id: 9, name: 'PostgreSQL', slug: 'postgresql', created_at: new Date() },
    { id: 10, name: 'MongoDB', slug: 'mongodb', created_at: new Date() },
    { id: 11, name: 'Redis', slug: 'redis', created_at: new Date() },
    { id: 12, name: 'Docker', slug: 'docker', created_at: new Date() },
    { id: 13, name: 'Kubernetes', slug: 'kubernetes', created_at: new Date() },
    { id: 14, name: 'AWS', slug: 'aws', created_at: new Date() },
    { id: 15, name: '微服务', slug: 'microservices', created_at: new Date() },
    { id: 16, name: 'GraphQL', slug: 'graphql', created_at: new Date() },
    { id: 17, name: 'REST API', slug: 'rest-api', created_at: new Date() },
    { id: 18, name: 'Webpack', slug: 'webpack', created_at: new Date() },
    { id: 19, name: 'Vite', slug: 'vite', created_at: new Date() },
    { id: 20, name: 'ESLint', slug: 'eslint', created_at: new Date() },

    // 方法论标签
    { id: 21, name: '最佳实践', slug: 'best-practices', created_at: new Date() },
    { id: 22, name: '性能优化', slug: 'performance', created_at: new Date() },
    { id: 23, name: '代码重构', slug: 'refactoring', created_at: new Date() },
    { id: 24, name: '单元测试', slug: 'unit-testing', created_at: new Date() },
    { id: 25, name: '集成测试', slug: 'integration-testing', created_at: new Date() },
    { id: 26, name: '代码审查', slug: 'code-review', created_at: new Date() },
    { id: 27, name: '敏捷开发', slug: 'agile', created_at: new Date() },
    { id: 28, name: 'DevOps', slug: 'devops', created_at: new Date() },
    { id: 29, name: 'CI/CD', slug: 'cicd', created_at: new Date() },
    { id: 30, name: '架构设计', slug: 'architecture', created_at: new Date() },

    // 学习标签
    { id: 31, name: '教程', slug: 'tutorial', created_at: new Date() },
    { id: 32, name: '入门指南', slug: 'getting-started', created_at: new Date() },
    { id: 33, name: '进阶技巧', slug: 'advanced', created_at: new Date() },
    { id: 34, name: '问题解决', slug: 'troubleshooting', created_at: new Date() },
    { id: 35, name: '经验分享', slug: 'experience', created_at: new Date() },
    { id: 36, name: '学习笔记', slug: 'notes', created_at: new Date() },
    { id: 37, name: '读书笔记', slug: 'book-notes', created_at: new Date() },
    { id: 38, name: '技术总结', slug: 'summary', created_at: new Date() },

    // 生活标签
    { id: 39, name: '生活感悟', slug: 'life-thoughts', created_at: new Date() },
    { id: 40, name: '旅行', slug: 'travel', created_at: new Date() },
    { id: 41, name: '摄影', slug: 'photography', created_at: new Date() },
    { id: 42, name: '美食', slug: 'food', created_at: new Date() },
    { id: 43, name: '健身', slug: 'fitness', created_at: new Date() },
    { id: 44, name: '阅读', slug: 'reading', created_at: new Date() },
    { id: 45, name: '电影', slug: 'movies', created_at: new Date() },
    { id: 46, name: '音乐', slug: 'music', created_at: new Date() },
    { id: 47, name: '游戏', slug: 'gaming', created_at: new Date() },
    { id: 48, name: '随想', slug: 'thoughts', created_at: new Date() },

    // 项目标签
    { id: 49, name: '开源项目', slug: 'open-source', created_at: new Date() },
    { id: 50, name: '个人项目', slug: 'personal-project', created_at: new Date() },
    { id: 51, name: '团队协作', slug: 'teamwork', created_at: new Date() },
    { id: 52, name: '项目管理', slug: 'project-management', created_at: new Date() },
    { id: 53, name: '产品设计', slug: 'product-design', created_at: new Date() },
    { id: 54, name: 'UI/UX', slug: 'ui-ux', created_at: new Date() },
    { id: 55, name: '用户体验', slug: 'user-experience', created_at: new Date() }
  ]

  await queryInterface.bulkInsert('tags', tags)

  console.log('✅ 分类和标签种子数据创建完成')
  console.log(`   - 创建了 ${categories.length} 个分类（包含层级结构）`)
  console.log(`   - 创建了 ${tags.length} 个标签`)
}

export async function down(queryInterface: QueryInterface): Promise<void> {
  await queryInterface.bulkDelete('tags', {}, {})
  await queryInterface.bulkDelete('categories', {}, {})
}
