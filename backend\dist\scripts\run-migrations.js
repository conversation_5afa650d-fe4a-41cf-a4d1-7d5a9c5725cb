#!/usr/bin/env ts-node
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const migration_runner_1 = require("../database/migrations/migration-runner");
async function main() {
    const runner = new migration_runner_1.MigrationRunner();
    try {
        console.log('🚀 开始执行数据库迁移...');
        console.log('='.repeat(50));
        await runner.status();
        console.log('='.repeat(50));
        await runner.up();
        console.log('='.repeat(50));
        console.log('🎉 数据库迁移完成!');
        await runner.status();
    }
    catch (error) {
        console.error('❌ 迁移执行失败:', error);
        process.exit(1);
    }
}
if (require.main === module) {
    main();
}
//# sourceMappingURL=run-migrations.js.map