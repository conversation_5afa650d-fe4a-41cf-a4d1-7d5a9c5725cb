#!/usr/bin/env ts-node

import { SeederRunner } from '../database/seeders/seeder-runner'

/**
 * 种子数据执行脚本
 * 用于在开发和生产环境中执行数据库种子数据
 */

async function main() {
  const runner = new SeederRunner()
  
  try {
    console.log('🌱 开始执行数据库种子数据...')
    console.log('=' .repeat(50))
    
    // 显示当前种子数据状态
    await runner.status()
    console.log('=' .repeat(50))
    
    // 执行所有待执行的种子数据
    await runner.seed()
    
    console.log('=' .repeat(50))
    console.log('🎉 数据库种子数据完成!')
    
    // 再次显示种子数据状态确认
    await runner.status()
    
  } catch (error) {
    console.error('❌ 种子数据执行失败:', error)
    process.exit(1)
  }
}

// 执行主函数
if (require.main === module) {
  main()
}
