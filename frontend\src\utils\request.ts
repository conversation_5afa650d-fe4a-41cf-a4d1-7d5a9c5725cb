import axios, { type AxiosResponse, type AxiosRequestConfig } from 'axios'
import { useAuthStore } from '@/stores/auth'

/**
 * 创建一个 axios 实例，用于发送 API 请求
 * 这个文件提供了与现有 api.ts 兼容的 request 接口
 */
const request = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

/**
 * 请求拦截器：在请求发送前添加认证 token 到请求头中
 */
request.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()
    if (authStore.token) {
      config.headers.Authorization = `Bearer ${authStore.token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

/**
 * 响应拦截器：统一处理响应数据和错误
 */
request.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  async (error) => {
    // 处理 401 未授权错误
    if (error.response?.status === 401) {
      const authStore = useAuthStore()
      authStore.logout()
      window.location.href = '/login'
      return Promise.reject(error)
    }

    // 处理其他错误
    const message = error.response?.data?.error?.message || 
                   error.response?.data?.message || 
                   error.message || 
                   'An error occurred'
    
    return Promise.reject(new Error(message))
  }
)

export { request }
export default request
