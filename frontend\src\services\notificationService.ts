import { request } from '@/utils/request'
import type {
  Notification,
  NotificationListParams,
  NotificationListResponse,
  UnreadCountResponse,
  BatchOperationRequest,
  BatchOperationResponse,
  NotificationPreference,
  UpdatePreferencesRequest,
  NotificationActionResult
} from '@/types/notification'

/**
 * 通知服务模块，提供通知相关的API接口封装
 */
export const notificationService = {
  /**
   * 获取通知列表
   */
  async getNotifications(params: NotificationListParams = {}): Promise<NotificationListResponse> {
    const response = await request.get<{
      success: boolean
      data: NotificationListResponse
    }>('/notifications', { 
      params,
      loadingMessage: '正在加载通知列表...'
    })

    if (!response.data.success) {
      throw new Error('获取通知列表失败')
    }

    return response.data.data
  },

  /**
   * 获取未读通知数量
   */
  async getUnreadCount(): Promise<number> {
    const response = await request.get<{
      success: boolean
      data: UnreadCountResponse
    }>('/notifications/unread-count')

    if (!response.data.success) {
      throw new Error('获取未读通知数量失败')
    }

    return response.data.data.count
  },

  /**
   * 标记单个通知为已读
   */
  async markAsRead(notificationId: number): Promise<Notification> {
    const response = await request.put<{
      success: boolean
      message: string
      data: Notification
    }>(`/notifications/${notificationId}/read`, {}, {
      loadingMessage: '正在标记已读...'
    })

    if (!response.data.success) {
      throw new Error(response.data.message || '标记通知已读失败')
    }

    return response.data.data
  },

  /**
   * 批量标记通知为已读
   */
  async markBatchAsRead(notificationIds: number[]): Promise<number> {
    const response = await request.put<{
      success: boolean
      message: string
      data: BatchOperationResponse
    }>('/notifications/batch/read', {
      notificationIds
    } as BatchOperationRequest, {
      loadingMessage: '正在批量标记已读...'
    })

    if (!response.data.success) {
      throw new Error(response.data.message || '批量标记通知已读失败')
    }

    return response.data.data.affectedCount
  },

  /**
   * 标记所有通知为已读
   */
  async markAllAsRead(): Promise<number> {
    const response = await request.put<{
      success: boolean
      message: string
      data: BatchOperationResponse
    }>('/notifications/all/read', {}, {
      loadingMessage: '正在标记所有通知已读...'
    })

    if (!response.data.success) {
      throw new Error(response.data.message || '标记所有通知已读失败')
    }

    return response.data.data.affectedCount
  },

  /**
   * 删除单个通知
   */
  async deleteNotification(notificationId: number): Promise<void> {
    const response = await request.delete<{
      success: boolean
      message: string
    }>(`/notifications/${notificationId}`, {
      loadingMessage: '正在删除通知...'
    })

    if (!response.data.success) {
      throw new Error(response.data.message || '删除通知失败')
    }
  },

  /**
   * 批量删除通知
   */
  async deleteBatchNotifications(notificationIds: number[]): Promise<number> {
    const response = await request.delete<{
      success: boolean
      message: string
      data: BatchOperationResponse
    }>('/notifications/batch', {
      data: {
        notificationIds
      } as BatchOperationRequest,
      loadingMessage: '正在批量删除通知...'
    })

    if (!response.data.success) {
      throw new Error(response.data.message || '批量删除通知失败')
    }

    return response.data.data.deletedCount
  },

  /**
   * 获取通知偏好设置
   */
  async getPreferences(): Promise<NotificationPreference[]> {
    const response = await request.get<{
      success: boolean
      data: NotificationPreference[]
    }>('/notifications/preferences', {
      loadingMessage: '正在加载通知设置...'
    })

    if (!response.data.success) {
      throw new Error('获取通知偏好设置失败')
    }

    return response.data.data
  },

  /**
   * 更新通知偏好设置
   */
  async updatePreferences(preferences: UpdatePreferencesRequest['preferences']): Promise<NotificationPreference[]> {
    const response = await request.put<{
      success: boolean
      message: string
      data: NotificationPreference[]
    }>('/notifications/preferences', {
      preferences
    } as UpdatePreferencesRequest, {
      loadingMessage: '正在更新通知设置...'
    })

    if (!response.data.success) {
      throw new Error(response.data.message || '更新通知偏好设置失败')
    }

    return response.data.data
  },

  /**
   * 获取通知详情
   */
  async getNotificationDetail(notificationId: number): Promise<Notification> {
    const response = await request.get<{
      success: boolean
      data: Notification
    }>(`/notifications/${notificationId}`, {
      loadingMessage: '正在加载通知详情...'
    })

    if (!response.data.success) {
      throw new Error('获取通知详情失败')
    }

    return response.data.data
  },

  /**
   * 搜索通知
   */
  async searchNotifications(params: NotificationListParams & {
    keyword?: string
  }): Promise<NotificationListResponse> {
    const response = await request.get<{
      success: boolean
      data: NotificationListResponse
    }>('/notifications/search', { 
      params,
      loadingMessage: '正在搜索通知...'
    })

    if (!response.data.success) {
      throw new Error('搜索通知失败')
    }

    return response.data.data
  },

  /**
   * 获取通知统计信息
   */
  async getNotificationStats(): Promise<{
    total: number
    unread: number
    byType: Record<string, number>
    byPriority: Record<string, number>
  }> {
    const response = await request.get<{
      success: boolean
      data: {
        total: number
        unread: number
        byType: Record<string, number>
        byPriority: Record<string, number>
      }
    }>('/notifications/stats', {
      loadingMessage: '正在加载统计信息...'
    })

    if (!response.data.success) {
      throw new Error('获取通知统计信息失败')
    }

    return response.data.data
  },

  /**
   * 清理已读通知
   */
  async cleanupReadNotifications(): Promise<number> {
    const response = await request.delete<{
      success: boolean
      message: string
      data: { deletedCount: number }
    }>('/notifications/cleanup', {
      loadingMessage: '正在清理通知...'
    })

    if (!response.data.success) {
      throw new Error(response.data.message || '清理通知失败')
    }

    return response.data.data.deletedCount
  },

  /**
   * 测试通知发送（开发环境）
   */
  async sendTestNotification(type: string): Promise<void> {
    const response = await request.post<{
      success: boolean
      message: string
    }>('/notifications/test', { type }, {
      loadingMessage: '正在发送测试通知...'
    })

    if (!response.data.success) {
      throw new Error(response.data.message || '发送测试通知失败')
    }
  }
}

export default notificationService
