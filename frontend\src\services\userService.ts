import { request } from '@/utils/request'
import type {
  User,
  Role,
  Permission,
  CreateUserRequest,
  UpdateUserRequest,
  UserListParams,
  UserListResponse,
  UserStats,
  UserPermissions
} from '@/types/user'

// 用户管理服务
export class UserService {
  // 获取用户列表
  static async getUsers(params: UserListParams = {}): Promise<UserListResponse> {
    const response = await request.get('/api/admin/users', { params })
    return response.data
  }

  // 获取用户详情
  static async getUserById(id: number): Promise<User> {
    const response = await request.get(`/api/admin/users/${id}`)
    return response.data
  }

  // 创建用户
  static async createUser(data: CreateUserRequest): Promise<User> {
    const response = await request.post('/api/admin/users', data)
    return response.data
  }

  // 更新用户
  static async updateUser(id: number, data: UpdateUserRequest): Promise<User> {
    const response = await request.put(`/api/admin/users/${id}`, data)
    return response.data
  }

  // 删除用户
  static async deleteUser(id: number): Promise<void> {
    await request.delete(`/api/admin/users/${id}`)
  }

  // 批量删除用户
  static async batchDeleteUsers(ids: number[]): Promise<void> {
    await request.post('/api/admin/users/batch-delete', { ids })
  }

  // 重置用户密码
  static async resetPassword(id: number, newPassword: string): Promise<void> {
    await request.post(`/api/admin/users/${id}/reset-password`, { password: newPassword })
  }

  // 更改用户状态
  static async updateUserStatus(id: number, status: 'active' | 'inactive' | 'banned'): Promise<void> {
    await request.patch(`/api/admin/users/${id}/status`, { status })
  }

  // 获取用户统计
  static async getUserStats(): Promise<UserStats> {
    const response = await request.get('/api/admin/users/stats')
    return response.data
  }

  // 获取用户权限
  static async getUserPermissions(id: number): Promise<UserPermissions> {
    const response = await request.get(`/api/admin/users/${id}/permissions`)
    return response.data
  }

  // 分配角色给用户
  static async assignRoles(id: number, roleIds: number[]): Promise<void> {
    await request.post(`/api/admin/users/${id}/roles`, { roleIds })
  }

  // 移除用户角色
  static async removeRoles(id: number, roleIds: number[]): Promise<void> {
    await request.delete(`/api/admin/users/${id}/roles`, { data: { roleIds } })
  }

  // 获取所有角色
  static async getRoles(): Promise<Role[]> {
    const response = await request.get('/api/admin/roles')
    return response.data
  }

  // 获取所有权限
  static async getPermissions(): Promise<Permission[]> {
    const response = await request.get('/api/admin/permissions')
    return response.data
  }

  // 搜索用户
  static async searchUsers(query: string, limit: number = 10): Promise<User[]> {
    const response = await request.get('/api/admin/users/search', {
      params: { q: query, limit }
    })
    return response.data
  }

  // 导出用户数据
  static async exportUsers(params: UserListParams = {}): Promise<Blob> {
    const response = await request.get('/api/admin/users/export', {
      params,
      responseType: 'blob'
    })
    return response.data
  }

  // 导入用户数据
  static async importUsers(file: File): Promise<{ success: number; failed: number; errors: string[] }> {
    const formData = new FormData()
    formData.append('file', file)

    const response = await request.post('/api/admin/users/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data
  }

  // 获取用户活动日志
  static async getUserActivityLogs(id: number, params: {
    page?: number
    limit?: number
    startDate?: string
    endDate?: string
  } = {}): Promise<{
    logs: Array<{
      id: number
      action: string
      resource: string
      resourceId?: number
      ipAddress?: string
      userAgent?: string
      status: string
      createdAt: string
    }>
    total: number
  }> {
    const response = await request.get(`/api/admin/users/${id}/activity-logs`, { params })
    return response.data
  }

  // 强制用户下线
  static async forceLogout(id: number): Promise<void> {
    await request.post(`/api/admin/users/${id}/force-logout`)
  }

  // 锁定用户账户
  static async lockUser(id: number, reason?: string): Promise<void> {
    await request.post(`/api/admin/users/${id}/lock`, { reason })
  }

  // 解锁用户账户
  static async unlockUser(id: number): Promise<void> {
    await request.post(`/api/admin/users/${id}/unlock`)
  }

  // 发送密码重置邮件
  static async sendPasswordResetEmail(id: number): Promise<void> {
    await request.post(`/api/admin/users/${id}/send-password-reset`)
  }

  // 验证用户名是否可用
  static async checkUsernameAvailability(username: string, excludeId?: number): Promise<{ available: boolean }> {
    const response = await request.get('/api/admin/users/check-username', {
      params: { username, excludeId }
    })
    return response.data
  }

  // 验证邮箱是否可用
  static async checkEmailAvailability(email: string, excludeId?: number): Promise<{ available: boolean }> {
    const response = await request.get('/api/admin/users/check-email', {
      params: { email, excludeId }
    })
    return response.data
  }

  // 获取用户登录历史
  static async getUserLoginHistory(id: number, params: {
    page?: number
    limit?: number
    startDate?: string
    endDate?: string
  } = {}): Promise<{
    history: Array<{
      id: number
      ipAddress: string
      userAgent: string
      location?: string
      loginAt: string
      logoutAt?: string
      status: 'success' | 'failed'
    }>
    total: number
  }> {
    const response = await request.get(`/api/admin/users/${id}/login-history`, { params })
    return response.data
  }

  // 获取用户设备信息
  static async getUserDevices(id: number): Promise<Array<{
    id: string
    deviceType: string
    browser: string
    os: string
    lastActiveAt: string
    isCurrentDevice: boolean
  }>> {
    const response = await request.get(`/api/admin/users/${id}/devices`)
    return response.data
  }

  // 移除用户设备
  static async removeUserDevice(id: number, deviceId: string): Promise<void> {
    await request.delete(`/api/admin/users/${id}/devices/${deviceId}`)
  }
}

export default UserService
