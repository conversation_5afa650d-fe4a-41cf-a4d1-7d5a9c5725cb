"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const up = async (queryInterface) => {
    console.log('Creating sample media files...');
    const [users] = await queryInterface.sequelize.query('SELECT id, username FROM users ORDER BY id');
    const adminUser = users.find((u) => u.username === 'admin');
    const johnUser = users.find((u) => u.username === 'john_doe');
    const janeUser = users.find((u) => u.username === 'jane_smith');
    const writerUser = users.find((u) => u.username === 'tech_writer');
    const mediaFiles = [
        {
            id: 1,
            filename: 'hero-banner-2024.jpg',
            original_name: 'hero-banner-2024.jpg',
            mime_type: 'image/jpeg',
            size: 2097152,
            url: '/uploads/images/hero-banner-2024.jpg',
            thumbnail_url: '/uploads/images/thumbnails/hero-banner-2024-thumb.jpg',
            width: 1920,
            height: 1080,
            media_type: 'image',
            tags: JSON.stringify(['横幅', '设计', '首页']),
            description: '网站首页横幅图片，展示博客主题和风格',
            is_public: true,
            uploaded_by: adminUser?.id || 1,
            created_at: new Date('2024-01-05 10:00:00'),
            updated_at: new Date('2024-01-05 10:00:00')
        },
        {
            id: 2,
            filename: 'blog-cover-tech.png',
            original_name: 'blog-cover-tech.png',
            mime_type: 'image/png',
            size: 1572864,
            url: '/uploads/images/blog-cover-tech.png',
            thumbnail_url: '/uploads/images/thumbnails/blog-cover-tech-thumb.png',
            width: 1200,
            height: 630,
            media_type: 'image',
            tags: JSON.stringify(['封面', '技术', '博客']),
            description: '技术博客封面图片，用于社交媒体分享',
            is_public: true,
            uploaded_by: johnUser?.id || 2,
            created_at: new Date('2024-01-08 14:30:00'),
            updated_at: new Date('2024-01-08 14:30:00')
        },
        {
            id: 3,
            filename: 'avatar-placeholder.svg',
            original_name: 'avatar-placeholder.svg',
            mime_type: 'image/svg+xml',
            size: 8192,
            url: '/uploads/images/avatar-placeholder.svg',
            thumbnail_url: '/uploads/images/thumbnails/avatar-placeholder-thumb.svg',
            width: 200,
            height: 200,
            media_type: 'image',
            tags: JSON.stringify(['头像', '图标', '默认']),
            description: '默认用户头像占位符',
            is_public: true,
            uploaded_by: adminUser?.id || 1,
            created_at: new Date('2024-01-05 11:00:00'),
            updated_at: new Date('2024-01-05 11:00:00')
        },
        {
            id: 4,
            filename: 'gallery-photo-1.jpg',
            original_name: 'gallery-photo-1.jpg',
            mime_type: 'image/jpeg',
            size: 3145728,
            url: '/uploads/images/gallery-photo-1.jpg',
            thumbnail_url: '/uploads/images/thumbnails/gallery-photo-1-thumb.jpg',
            width: 2560,
            height: 1440,
            media_type: 'image',
            tags: JSON.stringify(['摄影', '风景', '画廊']),
            description: '风景摄影作品，展示自然美景',
            is_public: true,
            uploaded_by: janeUser?.id || 3,
            created_at: new Date('2024-01-12 16:45:00'),
            updated_at: new Date('2024-01-12 16:45:00')
        },
        {
            id: 5,
            filename: 'intro-video-2024.mp4',
            original_name: 'intro-video-2024.mp4',
            mime_type: 'video/mp4',
            size: 52428800,
            url: '/uploads/videos/intro-video-2024.mp4',
            thumbnail_url: '/uploads/videos/thumbnails/intro-video-2024-thumb.jpg',
            width: 1920,
            height: 1080,
            media_type: 'video',
            tags: JSON.stringify(['介绍', '视频', '1080p']),
            description: '产品介绍视频，展示博客系统功能',
            is_public: true,
            uploaded_by: adminUser?.id || 1,
            created_at: new Date('2024-01-10 09:30:00'),
            updated_at: new Date('2024-01-10 09:30:00')
        },
        {
            id: 6,
            filename: 'tutorial-basics.webm',
            original_name: 'tutorial-basics.webm',
            mime_type: 'video/webm',
            size: 31457280,
            url: '/uploads/videos/tutorial-basics.webm',
            thumbnail_url: '/uploads/videos/thumbnails/tutorial-basics-thumb.jpg',
            width: 1280,
            height: 720,
            media_type: 'video',
            tags: JSON.stringify(['教程', '基础', '720p']),
            description: '基础教程视频，介绍系统使用方法',
            is_public: true,
            uploaded_by: writerUser?.id || 4,
            created_at: new Date('2024-01-15 11:20:00'),
            updated_at: new Date('2024-01-15 11:20:00')
        },
        {
            id: 7,
            filename: 'background-music.mp3',
            original_name: 'background-music.mp3',
            mime_type: 'audio/mpeg',
            size: 4194304,
            url: '/uploads/audio/background-music.mp3',
            thumbnail_url: null,
            width: null,
            height: null,
            media_type: 'audio',
            tags: JSON.stringify(['音乐', '背景', 'MP3']),
            description: '网站背景音乐，营造轻松氛围',
            is_public: true,
            uploaded_by: janeUser?.id || 3,
            created_at: new Date('2024-01-18 13:15:00'),
            updated_at: new Date('2024-01-18 13:15:00')
        },
        {
            id: 8,
            filename: 'podcast-episode-1.wav',
            original_name: 'podcast-episode-1.wav',
            mime_type: 'audio/wav',
            size: 25165824,
            url: '/uploads/audio/podcast-episode-1.wav',
            thumbnail_url: null,
            width: null,
            height: null,
            media_type: 'audio',
            tags: JSON.stringify(['播客', '技术', 'WAV']),
            description: '技术播客第一期，讨论前端开发趋势',
            is_public: true,
            uploaded_by: johnUser?.id || 2,
            created_at: new Date('2024-01-20 15:30:00'),
            updated_at: new Date('2024-01-20 15:30:00')
        },
        {
            id: 9,
            filename: 'user-manual-v2.pdf',
            original_name: 'user-manual-v2.pdf',
            mime_type: 'application/pdf',
            size: 2097152,
            url: '/uploads/documents/user-manual-v2.pdf',
            thumbnail_url: '/uploads/documents/thumbnails/user-manual-v2-thumb.png',
            width: null,
            height: null,
            media_type: 'document',
            tags: JSON.stringify(['手册', '用户', 'PDF']),
            description: '用户操作手册第二版，详细说明系统使用方法',
            is_public: true,
            uploaded_by: writerUser?.id || 4,
            created_at: new Date('2024-01-22 10:45:00'),
            updated_at: new Date('2024-01-22 10:45:00')
        },
        {
            id: 10,
            filename: 'api-documentation.docx',
            original_name: 'api-documentation.docx',
            mime_type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            size: 1048576,
            url: '/uploads/documents/api-documentation.docx',
            thumbnail_url: '/uploads/documents/thumbnails/api-documentation-thumb.png',
            width: null,
            height: null,
            media_type: 'document',
            tags: JSON.stringify(['API', '文档', 'Word']),
            description: 'API接口文档，供开发者参考',
            is_public: false,
            uploaded_by: adminUser?.id || 1,
            created_at: new Date('2024-01-25 09:20:00'),
            updated_at: new Date('2024-01-25 09:20:00')
        },
        {
            id: 11,
            filename: 'project-proposal.pptx',
            original_name: 'project-proposal.pptx',
            mime_type: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            size: 5242880,
            url: '/uploads/documents/project-proposal.pptx',
            thumbnail_url: '/uploads/documents/thumbnails/project-proposal-thumb.png',
            width: null,
            height: null,
            media_type: 'document',
            tags: JSON.stringify(['提案', '项目', 'PowerPoint']),
            description: '项目提案演示文稿，展示系统设计理念',
            is_public: false,
            uploaded_by: adminUser?.id || 1,
            created_at: new Date('2024-01-26 14:10:00'),
            updated_at: new Date('2024-01-26 14:10:00')
        },
        {
            id: 12,
            filename: 'data-export.xlsx',
            original_name: 'data-export.xlsx',
            mime_type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            size: 3145728,
            url: '/uploads/documents/data-export.xlsx',
            thumbnail_url: '/uploads/documents/thumbnails/data-export-thumb.png',
            width: null,
            height: null,
            media_type: 'document',
            tags: JSON.stringify(['数据', '导出', 'Excel']),
            description: '数据导出表格，包含用户和文章统计信息',
            is_public: false,
            uploaded_by: adminUser?.id || 1,
            created_at: new Date('2024-01-28 11:30:00'),
            updated_at: new Date('2024-01-28 11:30:00')
        }
    ];
    await queryInterface.bulkInsert('media', mediaFiles);
    console.log(`✅ Created ${mediaFiles.length} sample media files:`);
    console.log('  • Images: 4 files (JPG, PNG, SVG)');
    console.log('  • Videos: 2 files (MP4, WebM)');
    console.log('  • Audio: 2 files (MP3, WAV)');
    console.log('  • Documents: 4 files (PDF, DOCX, PPTX, XLSX)');
    console.log('  • Public files: 8');
    console.log('  • Private files: 4');
    console.log('  • Total size: ~130MB');
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.bulkDelete('media', {}, {});
    console.log('✅ Removed all sample media files');
};
exports.down = down;
//# sourceMappingURL=006-media.js.map