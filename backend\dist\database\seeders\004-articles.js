"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const up = async (queryInterface) => {
    console.log('Creating sample articles...');
    const [users] = await queryInterface.sequelize.query('SELECT id, username FROM users ORDER BY id');
    const adminUser = users.find((u) => u.username === 'admin');
    const johnUser = users.find((u) => u.username === 'john_doe');
    const janeUser = users.find((u) => u.username === 'jane_smith');
    const writerUser = users.find((u) => u.username === 'tech_writer');
    const sampleArticles = [
        {
            id: 1,
            title: '欢迎来到我的个人博客',
            slug: 'welcome-to-my-blog',
            content: `# 欢迎来到我的个人博客

欢迎访问我的个人博客！这里是我分享技术心得、生活感悟和学习笔记的地方。

## 关于这个博客

这个博客使用现代化的技术栈构建：

- **前端**: Vue.js + TypeScript + Element Plus
- **后端**: Node.js + Express + TypeScript
- **数据库**: MySQL
- **部署**: Docker + CI/CD

## 内容分类

在这里你可以找到：

1. **技术文章** - 前端、后端、数据库等技术分享
2. **学习笔记** - 读书笔记、课程总结
3. **生活随笔** - 日常思考和感悟
4. **项目分享** - 开源项目和实践经验

## 联系方式

如果你有任何问题或建议，欢迎通过以下方式联系我：

- 邮箱: <EMAIL>
- GitHub: https://github.com/admin
- 微信: admin_blog

感谢你的访问，希望这里的内容对你有所帮助！`,
            excerpt: '欢迎访问我的个人博客！这里是我分享技术心得、生活感悟和学习笔记的地方。',
            status: 'published',
            author_id: adminUser?.id || 1,
            category_id: 5,
            published_at: new Date('2024-01-10 10:00:00'),
            created_at: new Date('2024-01-10 10:00:00'),
            updated_at: new Date('2024-01-10 10:00:00')
        },
        {
            id: 2,
            title: 'TypeScript 开发最佳实践',
            slug: 'typescript-best-practices',
            content: `# TypeScript 开发最佳实践

TypeScript 作为 JavaScript 的超集，为我们提供了强大的类型系统。本文总结了一些 TypeScript 开发的最佳实践。

## 1. 类型定义

### 接口 vs 类型别名

\`\`\`typescript
// 推荐使用接口定义对象类型
interface User {
  id: number
  name: string
  email: string
}

// 类型别名适用于联合类型
type Status = 'pending' | 'approved' | 'rejected'
\`\`\`

### 泛型的使用

\`\`\`typescript
// 泛型函数
function createResponse<T>(data: T): ApiResponse<T> {
  return {
    success: true,
    data,
    message: 'Success'
  }
}
\`\`\`

## 2. 严格模式配置

在 \`tsconfig.json\` 中启用严格模式：

\`\`\`json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true
  }
}
\`\`\`

## 3. 错误处理

使用 Result 模式处理错误：

\`\`\`typescript
type Result<T, E = Error> = 
  | { success: true; data: T }
  | { success: false; error: E }

async function fetchUser(id: number): Promise<Result<User>> {
  try {
    const user = await userService.getById(id)
    return { success: true, data: user }
  } catch (error) {
    return { success: false, error: error as Error }
  }
}
\`\`\`

## 总结

TypeScript 的类型系统虽然增加了一些复杂性，但它带来的类型安全和开发体验提升是值得的。遵循这些最佳实践，可以让你的 TypeScript 代码更加健壮和可维护。`,
            excerpt: 'TypeScript 作为 JavaScript 的超集，为我们提供了强大的类型系统。本文总结了一些 TypeScript 开发的最佳实践。',
            status: 'published',
            author_id: johnUser?.id || 2,
            category_id: 9,
            published_at: new Date('2024-01-12 14:30:00'),
            created_at: new Date('2024-01-12 14:30:00'),
            updated_at: new Date('2024-01-12 14:30:00')
        },
        {
            id: 3,
            title: 'Node.js 后端开发入门指南',
            slug: 'nodejs-backend-guide',
            content: `# Node.js 后端开发入门指南

Node.js 让 JavaScript 能够在服务器端运行，为全栈开发提供了便利。本文将介绍 Node.js 后端开发的基础知识。

## 环境搭建

### 安装 Node.js

推荐使用 nvm 管理 Node.js 版本：

\`\`\`bash
# 安装最新 LTS 版本
nvm install --lts
nvm use --lts
\`\`\`

### 初始化项目

\`\`\`bash
mkdir my-api
cd my-api
npm init -y
\`\`\`

## Express 框架

Express 是最流行的 Node.js Web 框架：

\`\`\`javascript
const express = require('express')
const app = express()

// 中间件
app.use(express.json())
app.use(express.urlencoded({ extended: true }))

// 路由
app.get('/api/users', (req, res) => {
  res.json({ users: [] })
})

app.listen(3000, () => {
  console.log('Server running on port 3000')
})
\`\`\`

## 数据库集成

### 使用 Sequelize ORM

\`\`\`javascript
const { Sequelize, DataTypes } = require('sequelize')

const sequelize = new Sequelize('database', 'username', 'password', {
  host: 'localhost',
  dialect: 'mysql'
})

const User = sequelize.define('User', {
  name: DataTypes.STRING,
  email: DataTypes.STRING
})
\`\`\`

## 认证和授权

使用 JWT 实现用户认证：

\`\`\`javascript
const jwt = require('jsonwebtoken')

// 生成 token
const token = jwt.sign({ userId: user.id }, process.env.JWT_SECRET)

// 验证中间件
const authenticateToken = (req, res, next) => {
  const token = req.headers['authorization']?.split(' ')[1]
  
  if (!token) {
    return res.status(401).json({ error: 'Access denied' })
  }
  
  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) return res.status(403).json({ error: 'Invalid token' })
    req.user = user
    next()
  })
}
\`\`\`

## 错误处理

统一的错误处理中间件：

\`\`\`javascript
app.use((err, req, res, next) => {
  console.error(err.stack)
  res.status(500).json({
    error: 'Something went wrong!',
    message: process.env.NODE_ENV === 'development' ? err.message : undefined
  })
})
\`\`\`

## 总结

Node.js 后端开发涉及多个方面，从基础的 HTTP 服务到数据库操作、认证授权等。掌握这些基础知识后，你就可以开始构建自己的后端应用了。`,
            excerpt: 'Node.js 让 JavaScript 能够在服务器端运行，为全栈开发提供了便利。本文将介绍 Node.js 后端开发的基础知识。',
            status: 'published',
            author_id: writerUser?.id || 4,
            category_id: 13,
            published_at: new Date('2024-01-15 09:15:00'),
            created_at: new Date('2024-01-15 09:15:00'),
            updated_at: new Date('2024-01-15 09:15:00')
        },
        {
            id: 4,
            title: 'React Hooks 深入理解',
            slug: 'react-hooks-deep-dive',
            content: `# React Hooks 深入理解

React Hooks 是 React 16.8 引入的新特性，让我们能够在函数组件中使用状态和其他 React 特性。

## 基础 Hooks

### useState

\`\`\`jsx
import React, { useState } from 'react'

function Counter() {
  const [count, setCount] = useState(0)

  return (
    <div>
      <p>You clicked {count} times</p>
      <button onClick={() => setCount(count + 1)}>
        Click me
      </button>
    </div>
  )
}
\`\`\`

### useEffect

\`\`\`jsx
import React, { useState, useEffect } from 'react'

function Example() {
  const [count, setCount] = useState(0)

  // 相当于 componentDidMount 和 componentDidUpdate
  useEffect(() => {
    document.title = \`You clicked \${count} times\`
  })

  return (
    <div>
      <p>You clicked {count} times</p>
      <button onClick={() => setCount(count + 1)}>
        Click me
      </button>
    </div>
  )
}
\`\`\`

## 自定义 Hooks

自定义 Hook 是一个函数，其名称以 "use" 开头：

\`\`\`jsx
import { useState, useEffect } from 'react'

function useFriendStatus(friendID) {
  const [isOnline, setIsOnline] = useState(null)

  useEffect(() => {
    function handleStatusChange(status) {
      setIsOnline(status.isOnline)
    }

    ChatAPI.subscribeToFriendStatus(friendID, handleStatusChange)
    return () => {
      ChatAPI.unsubscribeFromFriendStatus(friendID, handleStatusChange)
    }
  })

  return isOnline
}
\`\`\`

## Hook 规则

1. 只在最顶层使用 Hook
2. 只在 React 函数中调用 Hook

## 总结

React Hooks 提供了一种更简洁的方式来编写 React 组件，让函数组件拥有了类组件的能力。`,
            excerpt: 'React Hooks 是 React 16.8 引入的新特性，让我们能够在函数组件中使用状态和其他 React 特性。',
            status: 'published',
            author_id: janeUser?.id || 3,
            category_id: 11,
            published_at: new Date('2024-01-18 16:20:00'),
            created_at: new Date('2024-01-18 16:20:00'),
            updated_at: new Date('2024-01-18 16:20:00')
        },
        {
            id: 5,
            title: 'MySQL 数据库优化技巧',
            slug: 'mysql-optimization-tips',
            content: `# MySQL 数据库优化技巧

数据库性能优化是后端开发中的重要技能。本文分享一些 MySQL 数据库优化的实用技巧。

## 索引优化

### 创建合适的索引

\`\`\`sql
-- 单列索引
CREATE INDEX idx_user_email ON users(email);

-- 复合索引
CREATE INDEX idx_article_status_date ON articles(status, published_at);

-- 唯一索引
CREATE UNIQUE INDEX idx_user_username ON users(username);
\`\`\`

### 索引使用原则

1. 在 WHERE 子句中经常使用的列上创建索引
2. 在 JOIN 条件的列上创建索引
3. 避免在小表上创建索引
4. 定期分析和优化索引

## 查询优化

### 使用 EXPLAIN 分析查询

\`\`\`sql
EXPLAIN SELECT * FROM articles 
WHERE status = 'published' 
ORDER BY published_at DESC 
LIMIT 10;
\`\`\`

### 避免全表扫描

\`\`\`sql
-- 不好的查询
SELECT * FROM users WHERE YEAR(created_at) = 2024;

-- 优化后的查询
SELECT * FROM users 
WHERE created_at >= '2024-01-01' 
AND created_at < '2025-01-01';
\`\`\`

## 配置优化

### 重要的配置参数

\`\`\`ini
# my.cnf
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
query_cache_size = 64M
max_connections = 200
\`\`\`

## 总结

数据库优化是一个持续的过程，需要根据实际业务场景进行调整。定期监控和分析数据库性能是必要的。`,
            excerpt: '数据库性能优化是后端开发中的重要技能。本文分享一些 MySQL 数据库优化的实用技巧。',
            status: 'published',
            author_id: writerUser?.id || 4,
            category_id: 7,
            published_at: new Date('2024-01-20 11:30:00'),
            created_at: new Date('2024-01-20 11:30:00'),
            updated_at: new Date('2024-01-20 11:30:00')
        },
        {
            id: 6,
            title: 'Docker 容器化部署实践',
            slug: 'docker-deployment-practice',
            content: `# Docker 容器化部署实践

Docker 是现代应用部署的重要工具。本文介绍如何使用 Docker 进行应用容器化部署。

## Docker 基础

### Dockerfile 编写

\`\`\`dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 3000

CMD ["npm", "start"]
\`\`\`

### 构建镜像

\`\`\`bash
docker build -t my-blog-app .
\`\`\`

## Docker Compose

### 多服务编排

\`\`\`yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=db
    depends_on:
      - db

  db:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=rootpassword
      - MYSQL_DATABASE=blog
    volumes:
      - db_data:/var/lib/mysql

volumes:
  db_data:
\`\`\`

### 启动服务

\`\`\`bash
docker-compose up -d
\`\`\`

## 最佳实践

1. 使用多阶段构建减小镜像大小
2. 合理使用 .dockerignore
3. 不要在容器中存储数据
4. 使用健康检查

## 总结

Docker 容器化部署提供了一致的运行环境，简化了应用的部署和管理。掌握 Docker 是现代开发者的必备技能。`,
            excerpt: 'Docker 是现代应用部署的重要工具。本文介绍如何使用 Docker 进行应用容器化部署。',
            status: 'published',
            author_id: johnUser?.id || 2,
            category_id: 8,
            published_at: new Date('2024-01-22 14:45:00'),
            created_at: new Date('2024-01-22 14:45:00'),
            updated_at: new Date('2024-01-22 14:45:00')
        },
        {
            id: 7,
            title: 'CSS Grid 布局完全指南',
            slug: 'css-grid-layout-guide',
            content: `# CSS Grid 布局完全指南

CSS Grid 是一个强大的二维布局系统，让我们能够创建复杂的网页布局。

## 基础概念

### Grid 容器和项目

\`\`\`css
.container {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: 100px 200px;
  gap: 20px;
}

.item {
  background-color: #f0f0f0;
  padding: 20px;
}
\`\`\`

### 网格线命名

\`\`\`css
.container {
  display: grid;
  grid-template-columns: [start] 1fr [middle] 1fr [end];
  grid-template-rows: [header-start] 100px [header-end content-start] 1fr [content-end];
}
\`\`\`

## 响应式设计

### 使用 minmax()

\`\`\`css
.container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}
\`\`\`

### 媒体查询配合

\`\`\`css
.container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

@media (min-width: 768px) {
  .container {
    grid-template-columns: 1fr 1fr;
  }
}

@media (min-width: 1024px) {
  .container {
    grid-template-columns: 1fr 1fr 1fr;
  }
}
\`\`\`

## 实际应用

### 经典布局

\`\`\`css
.layout {
  display: grid;
  grid-template-areas: 
    "header header header"
    "sidebar main aside"
    "footer footer footer";
  grid-template-columns: 200px 1fr 200px;
  grid-template-rows: 60px 1fr 60px;
  min-height: 100vh;
}

.header { grid-area: header; }
.sidebar { grid-area: sidebar; }
.main { grid-area: main; }
.aside { grid-area: aside; }
.footer { grid-area: footer; }
\`\`\`

## 总结

CSS Grid 为现代网页布局提供了强大而灵活的解决方案。结合 Flexbox，我们可以创建出各种复杂的布局效果。`,
            excerpt: 'CSS Grid 是一个强大的二维布局系统，让我们能够创建复杂的网页布局。',
            status: 'draft',
            author_id: janeUser?.id || 3,
            category_id: 12,
            published_at: null,
            created_at: new Date('2024-01-25 10:15:00'),
            updated_at: new Date('2024-01-25 10:15:00')
        }
    ];
    await queryInterface.bulkInsert('articles', sampleArticles);
    const articleTags = [
        { article_id: 1, tag_id: 1, created_at: new Date('2024-01-10 10:01:00') },
        { article_id: 1, tag_id: 3, created_at: new Date('2024-01-10 10:01:00') },
        { article_id: 1, tag_id: 4, created_at: new Date('2024-01-10 10:01:00') },
        { article_id: 1, tag_id: 37, created_at: new Date('2024-01-10 10:01:00') },
        { article_id: 2, tag_id: 1, created_at: new Date('2024-01-12 14:31:00') },
        { article_id: 2, tag_id: 2, created_at: new Date('2024-01-12 14:31:00') },
        { article_id: 2, tag_id: 37, created_at: new Date('2024-01-12 14:31:00') },
        { article_id: 2, tag_id: 51, created_at: new Date('2024-01-12 14:31:00') },
        { article_id: 2, tag_id: 59, created_at: new Date('2024-01-12 14:31:00') },
        { article_id: 3, tag_id: 13, created_at: new Date('2024-01-15 09:16:00') },
        { article_id: 3, tag_id: 14, created_at: new Date('2024-01-15 09:16:00') },
        { article_id: 3, tag_id: 38, created_at: new Date('2024-01-15 09:16:00') },
        { article_id: 3, tag_id: 42, created_at: new Date('2024-01-15 09:16:00') },
        { article_id: 3, tag_id: 58, created_at: new Date('2024-01-15 09:16:00') },
        { article_id: 4, tag_id: 3, created_at: new Date('2024-01-18 16:21:00') },
        { article_id: 4, tag_id: 1, created_at: new Date('2024-01-18 16:21:00') },
        { article_id: 4, tag_id: 37, created_at: new Date('2024-01-18 16:21:00') },
        { article_id: 4, tag_id: 58, created_at: new Date('2024-01-18 16:21:00') },
        { article_id: 5, tag_id: 25, created_at: new Date('2024-01-20 11:31:00') },
        { article_id: 5, tag_id: 41, created_at: new Date('2024-01-20 11:31:00') },
        { article_id: 5, tag_id: 50, created_at: new Date('2024-01-20 11:31:00') },
        { article_id: 5, tag_id: 59, created_at: new Date('2024-01-20 11:31:00') },
        { article_id: 6, tag_id: 29, created_at: new Date('2024-01-22 14:46:00') },
        { article_id: 6, tag_id: 38, created_at: new Date('2024-01-22 14:46:00') },
        { article_id: 6, tag_id: 55, created_at: new Date('2024-01-22 14:46:00') },
        { article_id: 6, tag_id: 58, created_at: new Date('2024-01-22 14:46:00') },
        { article_id: 7, tag_id: 6, created_at: new Date('2024-01-25 10:16:00') },
        { article_id: 7, tag_id: 37, created_at: new Date('2024-01-25 10:16:00') },
        { article_id: 7, tag_id: 58, created_at: new Date('2024-01-25 10:16:00') }
    ];
    await queryInterface.bulkInsert('article_tags', articleTags);
    console.log(`✅ Created ${sampleArticles.length} sample articles`);
    console.log(`✅ Created ${articleTags.length} article-tag associations`);
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.bulkDelete('article_tags', {}, {});
    await queryInterface.bulkDelete('articles', {}, {});
    console.log('✅ Removed all sample articles and article-tag associations');
};
exports.down = down;
//# sourceMappingURL=004-articles.js.map