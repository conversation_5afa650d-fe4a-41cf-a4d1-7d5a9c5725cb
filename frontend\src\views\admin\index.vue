<template>
  <div class="admin-view">
    <header class="admin-header">
      <h1>管理后台</h1>
      <div class="user-info">
        <span v-if="authStore.user">欢迎, {{ authStore.user.username }}</span>
        <button @click="handleLogout" class="logout-button">退出登录</button>
      </div>
    </header>
    
    <nav class="admin-nav">
      <router-link to="/admin" class="nav-link">仪表板</router-link>
      <router-link to="/admin/articles" class="nav-link">文章管理</router-link>
      <router-link to="/admin/posts" class="nav-link">说说管理</router-link>
      <router-link to="/admin/media" class="nav-link">媒体管理</router-link>
      <router-link to="/admin/categories" class="nav-link">分类管理</router-link>
      <router-link to="/admin/tags" class="nav-link">标签管理</router-link>
      <router-link to="/admin/comments" class="nav-link">评论管理</router-link>
      <router-link to="/admin/notifications" class="nav-link">通知中心</router-link>
      <router-link
        v-if="hasPermission('user:read')"
        to="/admin/users"
        class="nav-link"
      >
        用户管理
      </router-link>
      <router-link to="/admin/roles" class="nav-link">角色管理</router-link>
      <router-link to="/admin/user-roles" class="nav-link">用户角色</router-link>
      <router-link
        v-if="hasPermission('audit_log:read')"
        to="/admin/audit-logs"
        class="nav-link"
      >
        操作日志
      </router-link>
      <router-link to="/my/audit-logs" class="nav-link">我的日志</router-link>
      <router-link to="/settings" class="nav-link">设置</router-link>
    </nav>
    
    <main class="admin-content">
      <div class="dashboard-card">
        <h2>仪表板</h2>
        <p>欢迎来到个人博客管理系统！</p>
        
        <div class="quick-actions">
          <h3>快速操作</h3>
          <div class="action-buttons">
            <router-link to="/admin/articles/new" class="action-btn primary">新建文章</router-link>
            <router-link to="/admin/articles" class="action-btn">管理文章</router-link>
          </div>
        </div>
        
        <div class="user-details" v-if="authStore.user">
          <h3>用户信息</h3>
          <p><strong>用户名:</strong> {{ authStore.user.username }}</p>
          <p><strong>邮箱:</strong> {{ authStore.user.email }}</p>
          <p><strong>用户ID:</strong> {{ authStore.user.id }}</p>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useRbacStore } from '@/stores/rbac'

const router = useRouter()
const authStore = useAuthStore()
const rbacStore = useRbacStore()

// 权限缓存
const userPermissions = ref<Set<string>>(new Set())

/**
 * 检查用户是否有指定权限
 */
const hasPermission = (permission: string): boolean => {
  return userPermissions.value.has(permission)
}

/**
 * 加载用户权限
 */
const loadUserPermissions = async () => {
  if (!authStore.user) return

  try {
    // 获取用户角色
    const userRoles = await rbacStore.getUserRolesByUserId(authStore.user.id)
    const permissions = new Set<string>()

    // 获取每个角色的权限
    for (const role of userRoles) {
      const rolePermissions = await rbacStore.fetchRolePermissions(role.id)
      rolePermissions.forEach(permission => {
        permissions.add(`${permission.resource}:${permission.action}`)
      })
    }

    userPermissions.value = permissions
  } catch (error) {
    console.error('Failed to load user permissions:', error)
  }
}

/**
 * 处理用户登出逻辑
 *
 * 该函数执行以下操作：
 * 1. 调用认证存储的登出方法清除用户认证状态
 * 2. 将用户重定向到登录页面
 */
const handleLogout = async () => {
  await authStore.logout()
  router.push('/login')
}

/**
 * 组件挂载时的初始化逻辑
 *
 * 执行以下检查和操作：
 * 1. 检查用户是否已认证，未认证则重定向到登录页
 * 2. 验证当前认证状态的有效性，无效则重定向到登录页
 * 3. 加载用户权限信息
 */
onMounted(async () => {
  // 如果用户未认证，直接跳转到登录页面
  if (!authStore.isAuthenticated) {
    router.push('/login')
    return
  }

  // 验证认证状态是否仍然有效
  const isValid = await authStore.checkAuth()
  if (!isValid) {
    router.push('/login')
    return
  }

  // 加载用户权限
  await loadUserPermissions()
})
</script>

<style scoped>
.admin-view {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.admin-header {
  background: white;
  padding: 1rem 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.admin-header h1 {
  color: #333;
  margin: 0;
  font-size: 24px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-info span {
  color: #666;
  font-size: 14px;
}

.logout-button {
  padding: 8px 16px;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.logout-button:hover {
  background-color: #c82333;
}

.admin-content {
  padding: 2rem;
}

.dashboard-card {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dashboard-card h2 {
  color: #333;
  margin: 0 0 1rem 0;
  font-size: 20px;
}

.dashboard-card p {
  color: #666;
  margin-bottom: 1rem;
}

.user-details {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e1e5e9;
}

.user-details h3 {
  color: #333;
  margin: 0 0 1rem 0;
  font-size: 18px;
}

.user-details p {
  margin: 0.5rem 0;
  color: #555;
}

.user-details strong {
  color: #333;
}

.admin-nav {
  background: white;
  padding: 0 2rem;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  gap: 2rem;
}

.nav-link {
  padding: 1rem 0;
  text-decoration: none;
  color: #666;
  border-bottom: 2px solid transparent;
  transition: color 0.2s, border-color 0.2s;
}

.nav-link:hover,
.nav-link.router-link-active {
  color: #007bff;
  border-bottom-color: #007bff;
}

.quick-actions {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e1e5e9;
}

.quick-actions h3 {
  color: #333;
  margin: 0 0 1rem 0;
  font-size: 18px;
}

.action-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.action-btn {
  padding: 10px 20px;
  text-decoration: none;
  border-radius: 4px;
  font-size: 14px;
  transition: background-color 0.2s;
  border: 1px solid #ddd;
  color: #333;
}

.action-btn:hover {
  background-color: #f8f9fa;
}

.action-btn.primary {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.action-btn.primary:hover {
  background-color: #0056b3;
  border-color: #0056b3;
}
</style>