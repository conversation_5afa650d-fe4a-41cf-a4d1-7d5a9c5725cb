/**
 * 标签接口定义
 */
export interface Tag {
  id: number
  name: string
  slug: string
  description?: string
  createdAt: string
  articleCount?: number
}

/**
 * 标签列表响应数据接口
 */
export interface TagsResponse {
  success: boolean
  tags: Tag[]
}

/**
 * 标签文章列表响应数据接口
 */
export interface TagArticlesResponse {
  success: boolean
  articles: any[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}
