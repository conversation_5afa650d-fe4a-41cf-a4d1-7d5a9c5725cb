#!/usr/bin/env ts-node
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const database_1 = require("../config/database");
const seeder_runner_1 = require("../database/seeders/seeder-runner");
class SeederTester {
    constructor() {
        this.runner = new seeder_runner_1.SeederRunner();
    }
    async getTableCount(tableName) {
        try {
            const result = await database_1.sequelize.query(`SELECT COUNT(*) as count FROM ${tableName}`, { type: 'SELECT' });
            return parseInt(result[0].count);
        }
        catch (error) {
            console.warn(`⚠️ 无法获取表 ${tableName} 的记录数:`, error);
            return 0;
        }
    }
    async validateDataIntegrity() {
        console.log('🔍 验证数据完整性...');
        const userCount = await this.getTableCount('users');
        const settingsCount = await this.getTableCount('settings');
        if (userCount !== settingsCount) {
            throw new Error(`用户数量 (${userCount}) 与设置数量 (${settingsCount}) 不匹配`);
        }
        const roleCount = await this.getTableCount('roles');
        const permissionCount = await this.getTableCount('permissions');
        const rolePermissionCount = await this.getTableCount('role_permissions');
        if (roleCount === 0 || permissionCount === 0 || rolePermissionCount === 0) {
            throw new Error('角色权限数据不完整');
        }
        const articleCount = await this.getTableCount('articles');
        const tagCount = await this.getTableCount('tags');
        const articleTagCount = await this.getTableCount('article_tags');
        if (articleCount > 0 && articleTagCount === 0) {
            throw new Error('文章存在但没有标签关联');
        }
        const commentCount = await this.getTableCount('comments');
        if (commentCount === 0) {
            console.warn('⚠️ 没有评论数据');
        }
        console.log('✅ 数据完整性验证通过');
    }
    async validateRelationships() {
        console.log('🔍 验证数据关联关系...');
        const articlesWithoutAuthor = await database_1.sequelize.query(`SELECT COUNT(*) as count FROM articles 
       WHERE author_id NOT IN (SELECT id FROM users)`, { type: 'SELECT' });
        if (parseInt(articlesWithoutAuthor[0].count) > 0) {
            throw new Error('存在没有有效作者的文章');
        }
        const commentsWithoutAuthor = await database_1.sequelize.query(`SELECT COUNT(*) as count FROM comments 
       WHERE author_id NOT IN (SELECT id FROM users)`, { type: 'SELECT' });
        if (parseInt(commentsWithoutAuthor[0].count) > 0) {
            throw new Error('存在没有有效作者的评论');
        }
        const userRolesWithoutUser = await database_1.sequelize.query(`SELECT COUNT(*) as count FROM user_roles 
       WHERE user_id NOT IN (SELECT id FROM users)`, { type: 'SELECT' });
        if (parseInt(userRolesWithoutUser[0].count) > 0) {
            throw new Error('存在没有有效用户的用户角色关联');
        }
        console.log('✅ 数据关联关系验证通过');
    }
    async validateDataQuality() {
        console.log('🔍 验证数据质量...');
        const duplicateEmails = await database_1.sequelize.query(`SELECT email, COUNT(*) as count FROM users 
       GROUP BY email HAVING COUNT(*) > 1`, { type: 'SELECT' });
        if (duplicateEmails.length > 0) {
            throw new Error(`存在重复的用户邮箱: ${duplicateEmails.map(e => e.email).join(', ')}`);
        }
        const duplicateSlugs = await database_1.sequelize.query(`SELECT slug, COUNT(*) as count FROM articles 
       GROUP BY slug HAVING COUNT(*) > 1`, { type: 'SELECT' });
        if (duplicateSlugs.length > 0) {
            throw new Error(`存在重复的文章slug: ${duplicateSlugs.map(s => s.slug).join(', ')}`);
        }
        const duplicateTagNames = await database_1.sequelize.query(`SELECT name, COUNT(*) as count FROM tags 
       GROUP BY name HAVING COUNT(*) > 1`, { type: 'SELECT' });
        if (duplicateTagNames.length > 0) {
            throw new Error(`存在重复的标签名称: ${duplicateTagNames.map(t => t.name).join(', ')}`);
        }
        console.log('✅ 数据质量验证通过');
    }
    async showDataStatistics() {
        console.log('📊 数据统计:');
        const tables = [
            'users', 'settings', 'roles', 'permissions', 'user_roles', 'role_permissions',
            'categories', 'tags', 'articles', 'article_tags', 'posts', 'post_likes',
            'comments', 'media', 'notifications', 'notification_preferences', 'audit_logs'
        ];
        const statistics = [];
        for (const table of tables) {
            const count = await this.getTableCount(table);
            statistics.push({ tableName: table, count });
        }
        statistics.sort((a, b) => a.tableName.localeCompare(b.tableName));
        console.log('\n表名                     | 记录数');
        console.log('------------------------|-------');
        statistics.forEach(stat => {
            const paddedName = stat.tableName.padEnd(23);
            const paddedCount = stat.count.toString().padStart(6);
            console.log(`${paddedName} | ${paddedCount}`);
        });
        const totalRecords = statistics.reduce((sum, stat) => sum + stat.count, 0);
        console.log('------------------------|-------');
        console.log(`${'总计'.padEnd(23)} | ${totalRecords.toString().padStart(6)}`);
    }
    async runTests() {
        console.log('🧪 开始种子数据测试...');
        console.log('='.repeat(50));
        try {
            await database_1.sequelize.authenticate();
            console.log('✅ 数据库连接成功');
            console.log('\n🌱 执行种子数据...');
            await this.runner.seed();
            console.log('\n🔍 开始验证...');
            await this.validateDataIntegrity();
            await this.validateRelationships();
            await this.validateDataQuality();
            console.log('\n📊 显示统计信息...');
            await this.showDataStatistics();
            console.log('\n' + '='.repeat(50));
            console.log('🎉 所有测试通过!');
            await this.runner.status();
        }
        catch (error) {
            console.error('\n❌ 测试失败:', error);
            throw error;
        }
    }
    async testRollback() {
        console.log('🔄 测试种子数据回滚...');
        try {
            await this.runner.unseed(2);
            console.log('✅ 种子数据回滚成功');
            await this.runner.seed();
            console.log('✅ 种子数据重新执行成功');
        }
        catch (error) {
            console.error('❌ 回滚测试失败:', error);
            throw error;
        }
    }
}
async function main() {
    const tester = new SeederTester();
    try {
        await tester.runTests();
        if (process.argv.includes('--test-rollback')) {
            await tester.testRollback();
        }
        console.log('\n🎉 种子数据测试完成!');
    }
    catch (error) {
        console.error('\n❌ 种子数据测试失败:', error);
        process.exit(1);
    }
    finally {
        await database_1.sequelize.close();
    }
}
if (require.main === module) {
    main();
}
//# sourceMappingURL=test-seeders.js.map