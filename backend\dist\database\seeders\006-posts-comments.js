"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.up = up;
exports.down = down;
async function up(queryInterface) {
    console.log('🌱 创建说说和评论种子数据...');
    const posts = [
        {
            id: 1,
            content: '今天学习了 React Hooks，感觉打开了新世界的大门！useState 和 useEffect 真的很强大，让函数组件也能拥有状态管理和生命周期的能力。准备深入研究一下自定义 Hooks 的使用。 #React #学习笔记',
            images: JSON.stringify([
                'https://example.com/images/react-hooks-1.jpg',
                'https://example.com/images/react-hooks-2.jpg'
            ]),
            visibility: 'public',
            location: '北京市朝阳区',
            author_id: 2,
            created_at: new Date(Date.now() - 6 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 6 * 60 * 60 * 1000)
        },
        {
            id: 2,
            content: '刚刚完成了一个 Node.js 项目的性能优化，通过添加 Redis 缓存和数据库查询优化，响应时间从 800ms 降到了 120ms！优化的成就感真的无与伦比',
            visibility: 'public',
            author_id: 2,
            created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
        },
        {
            id: 3,
            content: '分享一个前端开发小技巧：使用 CSS Grid 布局可以很轻松地实现复杂的网格布局，比 Flexbox 在某些场景下更加灵活。最近在重构一个老项目，用 Grid 替换了之前复杂的 float 布局，代码简洁了很多！',
            images: JSON.stringify([
                'https://example.com/images/css-grid-demo.png'
            ]),
            visibility: 'public',
            location: '上海市浦东新区',
            author_id: 3,
            created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
        },
        {
            id: 4,
            content: '今天参加了一个技术分享会，听了关于微服务架构的演讲。虽然微服务有很多优势，但也带来了复杂性。对于小团队来说，单体应用可能是更好的选择。技术选型真的需要结合实际业务场景来考虑。',
            visibility: 'public',
            author_id: 3,
            created_at: new Date(Date.now() - 12 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 12 * 60 * 60 * 1000)
        },
        {
            id: 5,
            content: '周末在家学习 TypeScript，类型系统真的很强大！虽然刚开始写起来有点繁琐，但是能在编译时就发现很多潜在的错误，长期来看绝对是值得的。准备在下个项目中全面使用 TS。',
            visibility: 'public',
            author_id: 4,
            created_at: new Date(Date.now() - 3 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 3 * 60 * 60 * 1000)
        },
        {
            id: 6,
            content: '记录一下今天解决的一个奇怪 bug：数据库查询结果在本地环境正常，但在生产环境出现异常。最后发现是时区设置的问题，生产环境的数据库时区和应用服务器时区不一致。小细节往往是大问题的根源！',
            visibility: 'public',
            author_id: 4,
            created_at: new Date(Date.now() - 18 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 18 * 60 * 60 * 1000)
        },
        {
            id: 7,
            content: '今天心情不错，分享一张在公司楼下拍的夕阳照片。工作之余也要记得欣赏生活中的美好！',
            images: JSON.stringify([
                'https://example.com/images/sunset.jpg'
            ]),
            visibility: 'public',
            location: '深圳市南山区',
            author_id: 3,
            created_at: new Date(Date.now() - 4 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 4 * 60 * 60 * 1000)
        },
        {
            id: 8,
            content: '刚刚读完《代码整洁之道》这本书，收获很大！代码不仅要能运行，更要易读、易维护。好的代码就像好的文章，能够清晰地表达作者的意图。推荐给所有程序员朋友！',
            visibility: 'public',
            author_id: 2,
            created_at: new Date(Date.now() - 8 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 8 * 60 * 60 * 1000)
        }
    ];
    await queryInterface.bulkInsert('posts', posts);
    const postLikes = [
        { post_id: 1, user_id: 1, created_at: new Date(Date.now() - 5 * 60 * 60 * 1000) },
        { post_id: 1, user_id: 3, created_at: new Date(Date.now() - 4 * 60 * 60 * 1000) },
        { post_id: 1, user_id: 4, created_at: new Date(Date.now() - 3 * 60 * 60 * 1000) },
        { post_id: 2, user_id: 1, created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 60 * 60 * 1000) },
        { post_id: 2, user_id: 3, created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000) },
        { post_id: 3, user_id: 1, created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000 + 60 * 60 * 1000) },
        { post_id: 3, user_id: 2, created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000) },
        { post_id: 3, user_id: 4, created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000 + 3 * 60 * 60 * 1000) },
        { post_id: 4, user_id: 2, created_at: new Date(Date.now() - 11 * 60 * 60 * 1000) },
        { post_id: 4, user_id: 4, created_at: new Date(Date.now() - 10 * 60 * 60 * 1000) },
        { post_id: 5, user_id: 1, created_at: new Date(Date.now() - 2 * 60 * 60 * 1000) },
        { post_id: 5, user_id: 2, created_at: new Date(Date.now() - 1 * 60 * 60 * 1000) },
        { post_id: 5, user_id: 3, created_at: new Date(Date.now() - 30 * 60 * 1000) },
        { post_id: 7, user_id: 1, created_at: new Date(Date.now() - 3 * 60 * 60 * 1000) },
        { post_id: 7, user_id: 2, created_at: new Date(Date.now() - 2 * 60 * 60 * 1000) },
        { post_id: 7, user_id: 4, created_at: new Date(Date.now() - 1 * 60 * 60 * 1000) },
        { post_id: 8, user_id: 1, created_at: new Date(Date.now() - 7 * 60 * 60 * 1000) },
        { post_id: 8, user_id: 3, created_at: new Date(Date.now() - 6 * 60 * 60 * 1000) },
        { post_id: 8, user_id: 4, created_at: new Date(Date.now() - 5 * 60 * 60 * 1000) }
    ];
    await queryInterface.bulkInsert('post_likes', postLikes);
    const comments = [
        {
            id: 1,
            content: '这篇 React Hooks 教程写得很详细！特别是自定义 Hooks 的部分，让我对 Hooks 的理解更深入了。期待更多关于 React 的文章。',
            status: 'approved',
            article_id: 1,
            post_id: null,
            author_id: 3,
            parent_id: null,
            created_at: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000)
        },
        {
            id: 2,
            content: '感谢分享！我也在学习 React Hooks，这篇文章解答了我很多疑问。useEffect 的依赖数组部分讲得特别清楚。',
            status: 'approved',
            article_id: 1,
            post_id: null,
            author_id: 4,
            parent_id: null,
            created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
        },
        {
            id: 3,
            content: '@jane_smith 很高兴这篇文章对你有帮助！如果有其他问题欢迎随时交流。',
            status: 'approved',
            article_id: 1,
            post_id: null,
            author_id: 2,
            parent_id: 1,
            created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000)
        },
        {
            id: 4,
            content: 'Node.js 性能优化这个话题很实用！我们项目也遇到过类似的性能问题，Redis 缓存确实是个好方案。能分享一下具体的缓存策略吗？',
            status: 'approved',
            article_id: 2,
            post_id: null,
            author_id: 3,
            parent_id: null,
            created_at: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000)
        },
        {
            id: 5,
            content: 'TypeScript 的类型系统确实很强大，但学习曲线也比较陡峭。这篇文章的例子很实用，收藏了！',
            status: 'approved',
            article_id: 3,
            post_id: null,
            author_id: 4,
            parent_id: null,
            created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
        },
        {
            id: 6,
            content: '同感！React Hooks 真的改变了我们写 React 的方式，函数组件变得更加强大了。',
            status: 'approved',
            article_id: null,
            post_id: 1,
            author_id: 3,
            parent_id: null,
            created_at: new Date(Date.now() - 5 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 5 * 60 * 60 * 1000)
        },
        {
            id: 7,
            content: '性能优化从 800ms 到 120ms，这个提升太明显了！能分享一下具体是怎么优化的吗？',
            status: 'approved',
            article_id: null,
            post_id: 2,
            author_id: 4,
            parent_id: null,
            created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 30 * 60 * 1000),
            updated_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 30 * 60 * 1000)
        },
        {
            id: 8,
            content: 'CSS Grid 确实比 Flexbox 在某些布局场景下更灵活！我也在学习 Grid 布局，感觉很有用。',
            status: 'approved',
            article_id: null,
            post_id: 3,
            author_id: 2,
            parent_id: null,
            created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000 + 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000 + 60 * 60 * 1000)
        },
        {
            id: 9,
            content: '微服务 vs 单体应用，这个话题很有意思。确实需要根据团队规模和业务复杂度来选择。',
            status: 'approved',
            article_id: null,
            post_id: 4,
            author_id: 2,
            parent_id: null,
            created_at: new Date(Date.now() - 11 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 11 * 60 * 60 * 1000)
        },
        {
            id: 10,
            content: 'TypeScript 的学习曲线确实有点陡，但是用习惯了就离不开了。类型安全真的很重要！',
            status: 'approved',
            article_id: null,
            post_id: 5,
            author_id: 3,
            parent_id: null,
            created_at: new Date(Date.now() - 2 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 2 * 60 * 60 * 1000)
        },
        {
            id: 11,
            content: '时区问题真的是个坑！我之前也遇到过类似的问题，调试了好久才发现。',
            status: 'approved',
            article_id: null,
            post_id: 6,
            author_id: 2,
            parent_id: null,
            created_at: new Date(Date.now() - 17 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 17 * 60 * 60 * 1000)
        },
        {
            id: 12,
            content: '照片拍得真好看！工作之余确实要多关注生活中的美好。',
            status: 'approved',
            article_id: null,
            post_id: 7,
            author_id: 4,
            parent_id: null,
            created_at: new Date(Date.now() - 3 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 3 * 60 * 60 * 1000)
        },
        {
            id: 13,
            content: '《代码整洁之道》确实是本好书！我也读过，对提升代码质量很有帮助。',
            status: 'approved',
            article_id: null,
            post_id: 8,
            author_id: 3,
            parent_id: null,
            created_at: new Date(Date.now() - 7 * 60 * 60 * 1000),
            updated_at: new Date(Date.now() - 7 * 60 * 60 * 1000)
        }
    ];
    await queryInterface.bulkInsert('comments', comments);
    console.log('✅ 说说和评论种子数据创建完成');
    console.log(`   - 创建了 ${posts.length} 条说说`);
    console.log(`   - 创建了 ${postLikes.length} 个说说点赞`);
    console.log(`   - 创建了 ${comments.length} 条评论`);
}
async function down(queryInterface) {
    await queryInterface.bulkDelete('comments', {}, {});
    await queryInterface.bulkDelete('post_likes', {}, {});
    await queryInterface.bulkDelete('posts', {}, {});
}
//# sourceMappingURL=006-posts-comments.js.map