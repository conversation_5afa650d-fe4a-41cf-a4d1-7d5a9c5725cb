import { request } from '@/utils/request'
import type {
  Notification,
  NotificationListParams,
  NotificationListResponse,
  UnreadCountResponse,
  BatchOperationRequest,
  BatchOperationResponse,
  NotificationPreference,
  UpdatePreferencesRequest
} from '@/types/notification'

/**
 * 通知相关API服务
 */
export class NotificationAPI {
  /**
   * 获取通知列表
   */
  static async getNotifications(params: NotificationListParams = {}): Promise<NotificationListResponse> {
    const response = await request.get<{
      success: boolean
      data: NotificationListResponse
    }>('/notifications', { params })

    if (!response.data.success) {
      throw new Error('获取通知列表失败')
    }

    return response.data.data
  }

  /**
   * 获取未读通知数量
   */
  static async getUnreadCount(): Promise<number> {
    const response = await request.get<{
      success: boolean
      data: UnreadCountResponse
    }>('/notifications/unread-count')

    if (!response.data.success) {
      throw new Error('获取未读通知数量失败')
    }

    return response.data.data.count
  }

  /**
   * 标记单个通知为已读
   */
  static async markAsRead(notificationId: number): Promise<Notification> {
    const response = await request.put<{
      success: boolean
      message: string
      data: Notification
    }>(`/notifications/${notificationId}/read`)

    if (!response.data.success) {
      throw new Error(response.data.message || '标记通知已读失败')
    }

    return response.data.data
  }

  /**
   * 批量标记通知为已读
   */
  static async markBatchAsRead(notificationIds: number[]): Promise<number> {
    const response = await request.put<{
      success: boolean
      message: string
      data: BatchOperationResponse
    }>('/notifications/batch/read', {
      notificationIds
    } as BatchOperationRequest)

    if (!response.data.success) {
      throw new Error(response.data.message || '批量标记通知已读失败')
    }

    return response.data.data.affectedCount
  }

  /**
   * 标记所有通知为已读
   */
  static async markAllAsRead(): Promise<number> {
    const response = await request.put<{
      success: boolean
      message: string
      data: BatchOperationResponse
    }>('/notifications/all/read')

    if (!response.data.success) {
      throw new Error(response.data.message || '标记所有通知已读失败')
    }

    return response.data.data.affectedCount
  }

  /**
   * 删除单个通知
   */
  static async deleteNotification(notificationId: number): Promise<void> {
    const response = await request.delete<{
      success: boolean
      message: string
    }>(`/notifications/${notificationId}`)

    if (!response.data.success) {
      throw new Error(response.data.message || '删除通知失败')
    }
  }

  /**
   * 批量删除通知
   */
  static async deleteBatchNotifications(notificationIds: number[]): Promise<number> {
    const response = await request.delete<{
      success: boolean
      message: string
      data: BatchOperationResponse
    }>('/notifications/batch', {
      data: {
        notificationIds
      } as BatchOperationRequest
    })

    if (!response.data.success) {
      throw new Error(response.data.message || '批量删除通知失败')
    }

    return response.data.data.deletedCount || 0
  }

  /**
   * 获取通知偏好设置
   */
  static async getPreferences(): Promise<NotificationPreference[]> {
    const response = await request.get<{
      success: boolean
      data: NotificationPreference[]
    }>('/notifications/preferences')

    if (!response.data.success) {
      throw new Error('获取通知偏好设置失败')
    }

    return response.data.data
  }

  /**
   * 更新通知偏好设置
   */
  static async updatePreferences(preferences: UpdatePreferencesRequest['preferences']): Promise<NotificationPreference[]> {
    const response = await request.put<{
      success: boolean
      message: string
      data: NotificationPreference[]
    }>('/notifications/preferences', {
      preferences
    } as UpdatePreferencesRequest)

    if (!response.data.success) {
      throw new Error(response.data.message || '更新通知偏好设置失败')
    }

    return response.data.data
  }

  /**
   * 获取通知详情
   */
  static async getNotificationDetail(notificationId: number): Promise<Notification> {
    const response = await request.get<{
      success: boolean
      data: Notification
    }>(`/notifications/${notificationId}`)

    if (!response.data.success) {
      throw new Error('获取通知详情失败')
    }

    return response.data.data
  }

  /**
   * 搜索通知
   */
  static async searchNotifications(params: NotificationListParams & {
    keyword?: string
  }): Promise<NotificationListResponse> {
    const response = await request.get<{
      success: boolean
      data: NotificationListResponse
    }>('/notifications/search', { params })

    if (!response.data.success) {
      throw new Error('搜索通知失败')
    }

    return response.data.data
  }

  /**
   * 获取通知统计信息
   */
  static async getNotificationStats(): Promise<{
    total: number
    unread: number
    byType: Record<string, number>
    byPriority: Record<string, number>
  }> {
    const response = await request.get<{
      success: boolean
      data: {
        total: number
        unread: number
        byType: Record<string, number>
        byPriority: Record<string, number>
      }
    }>('/notifications/stats')

    if (!response.data.success) {
      throw new Error('获取通知统计信息失败')
    }

    return response.data.data
  }

  /**
   * 清理已读通知
   */
  static async cleanupReadNotifications(): Promise<number> {
    const response = await request.delete<{
      success: boolean
      message: string
      data: { deletedCount: number }
    }>('/notifications/cleanup')

    if (!response.data.success) {
      throw new Error(response.data.message || '清理通知失败')
    }

    return response.data.data.deletedCount
  }

  /**
   * 测试通知发送（开发环境）
   */
  static async sendTestNotification(type: string): Promise<void> {
    const response = await request.post<{
      success: boolean
      message: string
    }>('/notifications/test', { type })

    if (!response.data.success) {
      throw new Error(response.data.message || '发送测试通知失败')
    }
  }
}

/**
 * 通知API的便捷方法
 */
export const notificationAPI = {
  // 获取通知列表
  list: NotificationAPI.getNotifications,

  // 获取未读数量
  unreadCount: NotificationAPI.getUnreadCount,

  // 标记已读
  markRead: NotificationAPI.markAsRead,
  markBatchRead: NotificationAPI.markBatchAsRead,
  markAllRead: NotificationAPI.markAllAsRead,

  // 删除通知
  delete: NotificationAPI.deleteNotification,
  deleteBatch: NotificationAPI.deleteBatchNotifications,

  // 偏好设置
  getPreferences: NotificationAPI.getPreferences,
  updatePreferences: NotificationAPI.updatePreferences,

  // 其他功能
  detail: NotificationAPI.getNotificationDetail,
  search: NotificationAPI.searchNotifications,
  stats: NotificationAPI.getNotificationStats,
  cleanup: NotificationAPI.cleanupReadNotifications,
  sendTest: NotificationAPI.sendTestNotification
}

export default notificationAPI
