"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const up = async (queryInterface) => {
    console.log('Creating sample categories...');
    await queryInterface.bulkInsert('categories', [
        {
            id: 1,
            name: '技术',
            slug: 'tech',
            description: '技术相关文章分类',
            parent_id: null,
            sort: 1,
            created_at: new Date('2024-01-05 09:00:00'),
            updated_at: new Date('2024-01-05 09:00:00')
        },
        {
            id: 2,
            name: '生活',
            slug: 'life',
            description: '生活感悟和日常分享',
            parent_id: null,
            sort: 2,
            created_at: new Date('2024-01-05 09:01:00'),
            updated_at: new Date('2024-01-05 09:01:00')
        },
        {
            id: 3,
            name: '学习',
            slug: 'study',
            description: '学习笔记和心得体会',
            parent_id: null,
            sort: 3,
            created_at: new Date('2024-01-05 09:02:00'),
            updated_at: new Date('2024-01-05 09:02:00')
        },
        {
            id: 4,
            name: '随笔',
            slug: 'essay',
            description: '随想随写，记录点滴',
            parent_id: null,
            sort: 4,
            created_at: new Date('2024-01-05 09:03:00'),
            updated_at: new Date('2024-01-05 09:03:00')
        },
        {
            id: 5,
            name: '前端开发',
            slug: 'frontend',
            description: '前端技术相关内容',
            parent_id: 1,
            sort: 1,
            created_at: new Date('2024-01-05 09:10:00'),
            updated_at: new Date('2024-01-05 09:10:00')
        },
        {
            id: 6,
            name: '后端开发',
            slug: 'backend',
            description: '后端技术相关内容',
            parent_id: 1,
            sort: 2,
            created_at: new Date('2024-01-05 09:11:00'),
            updated_at: new Date('2024-01-05 09:11:00')
        },
        {
            id: 7,
            name: '数据库',
            slug: 'database',
            description: '数据库技术和优化',
            parent_id: 1,
            sort: 3,
            created_at: new Date('2024-01-05 09:12:00'),
            updated_at: new Date('2024-01-05 09:12:00')
        },
        {
            id: 8,
            name: '运维部署',
            slug: 'devops',
            description: '运维和部署相关技术',
            parent_id: 1,
            sort: 4,
            created_at: new Date('2024-01-05 09:13:00'),
            updated_at: new Date('2024-01-05 09:13:00')
        },
        {
            id: 9,
            name: 'JavaScript',
            slug: 'javascript',
            description: 'JavaScript语言和框架',
            parent_id: 5,
            sort: 1,
            created_at: new Date('2024-01-05 09:20:00'),
            updated_at: new Date('2024-01-05 09:20:00')
        },
        {
            id: 10,
            name: 'Vue.js',
            slug: 'vuejs',
            description: 'Vue.js框架相关',
            parent_id: 5,
            sort: 2,
            created_at: new Date('2024-01-05 09:21:00'),
            updated_at: new Date('2024-01-05 09:21:00')
        },
        {
            id: 11,
            name: 'React',
            slug: 'react',
            description: 'React框架相关',
            parent_id: 5,
            sort: 3,
            created_at: new Date('2024-01-05 09:22:00'),
            updated_at: new Date('2024-01-05 09:22:00')
        },
        {
            id: 12,
            name: 'CSS',
            slug: 'css',
            description: 'CSS样式和布局',
            parent_id: 5,
            sort: 4,
            created_at: new Date('2024-01-05 09:23:00'),
            updated_at: new Date('2024-01-05 09:23:00')
        },
        {
            id: 13,
            name: 'Node.js',
            slug: 'nodejs',
            description: 'Node.js后端开发',
            parent_id: 6,
            sort: 1,
            created_at: new Date('2024-01-05 09:30:00'),
            updated_at: new Date('2024-01-05 09:30:00')
        },
        {
            id: 14,
            name: 'Python',
            slug: 'python',
            description: 'Python语言和框架',
            parent_id: 6,
            sort: 2,
            created_at: new Date('2024-01-05 09:31:00'),
            updated_at: new Date('2024-01-05 09:31:00')
        },
        {
            id: 15,
            name: 'Java',
            slug: 'java',
            description: 'Java语言和生态',
            parent_id: 6,
            sort: 3,
            created_at: new Date('2024-01-05 09:32:00'),
            updated_at: new Date('2024-01-05 09:32:00')
        },
        {
            id: 16,
            name: 'API设计',
            slug: 'api-design',
            description: 'API设计和最佳实践',
            parent_id: 6,
            sort: 4,
            created_at: new Date('2024-01-05 09:33:00'),
            updated_at: new Date('2024-01-05 09:33:00')
        },
        {
            id: 17,
            name: '旅行',
            slug: 'travel',
            description: '旅行见闻和攻略',
            parent_id: 2,
            sort: 1,
            created_at: new Date('2024-01-05 09:40:00'),
            updated_at: new Date('2024-01-05 09:40:00')
        },
        {
            id: 18,
            name: '美食',
            slug: 'food',
            description: '美食分享和制作',
            parent_id: 2,
            sort: 2,
            created_at: new Date('2024-01-05 09:41:00'),
            updated_at: new Date('2024-01-05 09:41:00')
        },
        {
            id: 19,
            name: '健康',
            slug: 'health',
            description: '健康生活和养生',
            parent_id: 2,
            sort: 3,
            created_at: new Date('2024-01-05 09:42:00'),
            updated_at: new Date('2024-01-05 09:42:00')
        },
        {
            id: 20,
            name: '读书笔记',
            slug: 'reading-notes',
            description: '读书心得和笔记',
            parent_id: 3,
            sort: 1,
            created_at: new Date('2024-01-05 09:50:00'),
            updated_at: new Date('2024-01-05 09:50:00')
        },
        {
            id: 21,
            name: '在线课程',
            slug: 'online-courses',
            description: '在线课程学习记录',
            parent_id: 3,
            sort: 2,
            created_at: new Date('2024-01-05 09:51:00'),
            updated_at: new Date('2024-01-05 09:51:00')
        },
        {
            id: 22,
            name: '技能提升',
            slug: 'skill-improvement',
            description: '技能学习和提升',
            parent_id: 3,
            sort: 3,
            created_at: new Date('2024-01-05 09:52:00'),
            updated_at: new Date('2024-01-05 09:52:00')
        }
    ]);
    console.log('✅ Created 22 categories with hierarchical structure');
    console.log('  • 4 root categories');
    console.log('  • 18 sub-categories');
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.bulkDelete('categories', {}, {});
    console.log('✅ Removed all sample categories');
};
exports.down = down;
//# sourceMappingURL=002-categories.js.map