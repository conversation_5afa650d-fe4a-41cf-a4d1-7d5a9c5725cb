{"version": 3, "file": "015-add-user-status-fields.js", "sourceRoot": "", "sources": ["../../../src/database/migrations/015-add-user-status-fields.ts"], "names": [], "mappings": ";;;AAAA,yCAAqD;AAO9C,MAAM,EAAE,GAAG,KAAK,EAAE,cAA8B,EAAiB,EAAE;IAExE,MAAM,cAAc,CAAC,SAAS,CAAC,OAAO,EAAE,gBAAgB,EAAE;QACxD,IAAI,EAAE,qBAAS,CAAC,OAAO;QACvB,SAAS,EAAE,KAAK;QAChB,YAAY,EAAE,CAAC;QACf,OAAO,EAAE,QAAQ;KAClB,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,SAAS,CAAC,OAAO,EAAE,cAAc,EAAE;QACtD,IAAI,EAAE,qBAAS,CAAC,IAAI;QACpB,SAAS,EAAE,IAAI;QACf,OAAO,EAAE,UAAU;KACpB,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,SAAS,CAAC,OAAO,EAAE,iBAAiB,EAAE;QACzD,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,GAAG,CAAC;QAC3B,SAAS,EAAE,IAAI;QACf,OAAO,EAAE,OAAO;KACjB,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,SAAS,CAAC,OAAO,EAAE,KAAK,EAAE;QAC7C,IAAI,EAAE,qBAAS,CAAC,IAAI;QACpB,SAAS,EAAE,IAAI;QACf,OAAO,EAAE,MAAM;KAChB,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,SAAS,CAAC,OAAO,EAAE,SAAS,EAAE;QACjD,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,GAAG,CAAC;QAC3B,SAAS,EAAE,IAAI;QACf,OAAO,EAAE,MAAM;KAChB,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,SAAS,CAAC,OAAO,EAAE,UAAU,EAAE;QAClD,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,GAAG,CAAC;QAC3B,SAAS,EAAE,IAAI;QACf,OAAO,EAAE,KAAK;KACf,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,SAAS,CAAC,OAAO,EAAE,YAAY,EAAE;QACpD,IAAI,EAAE,qBAAS,CAAC,QAAQ;QACxB,SAAS,EAAE,IAAI;QACf,OAAO,EAAE,MAAM;KAChB,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,SAAS,CAAC,OAAO,EAAE,QAAQ,EAAE;QAChD,IAAI,EAAE,qBAAS,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,mBAAmB,CAAC;QACpE,SAAS,EAAE,IAAI;QACf,OAAO,EAAE,IAAI;KACd,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,SAAS,CAAC,OAAO,EAAE,UAAU,EAAE;QAClD,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,EAAE,CAAC;QAC1B,SAAS,EAAE,IAAI;QACf,YAAY,EAAE,KAAK;QACnB,OAAO,EAAE,IAAI;KACd,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,SAAS,CAAC,OAAO,EAAE,UAAU,EAAE;QAClD,IAAI,EAAE,qBAAS,CAAC,MAAM,CAAC,EAAE,CAAC;QAC1B,SAAS,EAAE,IAAI;QACf,YAAY,EAAE,OAAO;QACrB,OAAO,EAAE,MAAM;KAChB,CAAC,CAAA;IAGF,MAAM,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,cAAc,CAAC,EAAE;QACvD,IAAI,EAAE,wBAAwB;KAC/B,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,UAAU,CAAC,EAAE;QACnD,IAAI,EAAE,oBAAoB;KAC3B,CAAC,CAAA;IAEF,MAAM,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,EAAE;QACjD,IAAI,EAAE,kBAAkB;KACzB,CAAC,CAAA;IAEF,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAA;AACvD,CAAC,CAAA;AA/EY,QAAA,EAAE,MA+Ed;AAEM,MAAM,IAAI,GAAG,KAAK,EAAE,cAA8B,EAAiB,EAAE;IAE1E,MAAM,cAAc,CAAC,YAAY,CAAC,OAAO,EAAE,UAAU,CAAC,CAAA;IACtD,MAAM,cAAc,CAAC,YAAY,CAAC,OAAO,EAAE,UAAU,CAAC,CAAA;IACtD,MAAM,cAAc,CAAC,YAAY,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;IACpD,MAAM,cAAc,CAAC,YAAY,CAAC,OAAO,EAAE,YAAY,CAAC,CAAA;IACxD,MAAM,cAAc,CAAC,YAAY,CAAC,OAAO,EAAE,UAAU,CAAC,CAAA;IACtD,MAAM,cAAc,CAAC,YAAY,CAAC,OAAO,EAAE,SAAS,CAAC,CAAA;IACrD,MAAM,cAAc,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;IACjD,MAAM,cAAc,CAAC,YAAY,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAA;IAC7D,MAAM,cAAc,CAAC,YAAY,CAAC,OAAO,EAAE,cAAc,CAAC,CAAA;IAC1D,MAAM,cAAc,CAAC,YAAY,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAA;IAE5D,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAA;AAC7C,CAAC,CAAA;AAdY,QAAA,IAAI,QAchB"}