# 种子数据设计文档

## 📋 概述

本文档详细描述了博客系统的种子数据设计，包括数据结构、执行顺序、数据关系和使用方法。

## 🗂️ 种子数据文件结构

```
backend/src/database/seeders/
├── 001-admin-user.ts      # 用户数据（管理员 + 普通用户）
├── 002-categories.ts      # 分类数据（层次化分类）
├── 003-tags.ts           # 标签数据（技术标签）
├── 004-articles.ts       # 文章数据（示例文章 + 标签关联）
├── 005-comments.ts       # 评论数据（评论 + 回复）
└── 006-media.ts          # 媒体数据（图片、视频、音频、文档）
```

## 📊 数据统计

| 数据类型 | 数量 | 说明 |
|---------|------|------|
| 用户 | 4个 | 1个管理员 + 3个普通用户 |
| 分类 | 22个 | 3层层次结构，涵盖技术、生活、学习等 |
| 标签 | 60个 | 技术相关标签，支持中英文 |
| 文章 | 7篇 | 包含已发布和草稿状态 |
| 评论 | 15条 | 包含普通评论和回复评论 |
| 媒体文件 | 12个 | 图片、视频、音频、文档等类型 |

## 👥 用户数据

### 管理员用户
- **用户名**: admin
- **邮箱**: <EMAIL>
- **密码**: admin123
- **角色**: 系统管理员

### 普通用户
1. **john_doe** (<EMAIL>)
2. **jane_smith** (<EMAIL>)  
3. **tech_writer** (<EMAIL>)

**默认密码**: user123

## 📂 分类数据结构

### 根分类
1. **技术** (tech)
2. **生活** (life)
3. **学习** (study)
4. **随笔** (essay)

### 技术分类子分类
- **前端开发** (frontend)
  - JavaScript
  - Vue.js
  - React
  - CSS
- **后端开发** (backend)
  - Node.js
  - Python
  - Java
  - API设计
- **数据库** (database)
- **运维部署** (devops)

### 其他分类
- **生活**: 旅行、美食、健康
- **学习**: 读书笔记、在线课程、技能提升

## 🏷️ 标签数据

### 技术标签
- 前端: JavaScript, TypeScript, React, Vue.js, CSS, HTML
- 后端: Node.js, Express, Python, Java
- 数据库: MySQL, MongoDB
- 工具: Docker, Git, Webpack, Vite
- 云服务: AWS

### 中文标签
- 前端开发、后端开发、全栈开发
- Web开发、数据库、云计算
- 性能优化、代码规范、项目管理

## 📝 文章数据

### 已发布文章 (6篇)
1. **欢迎来到我的个人博客** - 前端开发分类
2. **TypeScript 开发最佳实践** - JavaScript分类
3. **Node.js 后端开发入门指南** - Node.js分类
4. **React Hooks 深入理解** - React分类
5. **MySQL 数据库优化技巧** - 数据库分类
6. **Docker 容器化部署实践** - 运维部署分类

### 草稿文章 (1篇)
1. **CSS Grid 布局完全指南** - CSS分类

### 文章特点
- 包含完整的 Markdown 内容
- 每篇文章都有摘要 (excerpt)
- 正确关联分类和标签
- 时间戳逻辑合理
- 不同作者的文章

## 💬 评论数据

### 评论类型
- **已批准评论**: 正常显示的评论
- **待审核评论**: 需要管理员审核
- **已拒绝评论**: 被拒绝的评论（如垃圾内容）

### 评论功能展示
- 普通评论
- 回复评论（支持嵌套）
- 不同用户的评论
- 不同状态的评论

## 📁 媒体数据

### 媒体类型
- **图片**: JPG、PNG、SVG 格式，包含横幅、封面、头像等
- **视频**: MP4、WebM 格式，包含介绍视频和教程
- **音频**: MP3、WAV 格式，包含背景音乐和播客
- **文档**: PDF、DOCX、PPTX、XLSX 格式，包含手册和文档

### 媒体特性
- 完整的元数据信息（尺寸、大小、MIME类型）
- 标签系统支持分类和搜索
- 公开/私有权限控制
- 缩略图支持（适用于图片和视频）
- 用户关联和上传记录

## 🔄 执行顺序

种子数据按以下顺序执行，确保数据依赖关系正确：

1. **用户数据** - 为后续数据提供作者信息
2. **分类数据** - 为文章提供分类信息
3. **标签数据** - 为文章提供标签信息
4. **文章数据** - 创建文章并关联分类和标签
5. **评论数据** - 为文章添加评论和回复
6. **媒体数据** - 创建媒体文件记录和用户关联

## 🚀 使用方法

### 初始化数据库
```bash
# 完整初始化（包含迁移和种子数据）
npm run db:init

# 仅运行种子数据
npm run db:seed

# 强制重新运行种子数据
npm run db:seed:force

# 仅运行迁移
npm run db:migrate
```

### 直接运行种子数据
```bash
# 使用 ts-node 直接运行
ts-node src/scripts/initDb.ts --seed-only

# 强制重新运行
ts-node src/scripts/initDb.ts --seed-only --force
```

## ⚠️ 注意事项

### 安全提醒
- 默认密码仅用于开发环境
- 生产环境必须更改默认密码
- 敏感信息不应包含在种子数据中

### 数据一致性
- 所有外键关系都已正确设置
- 时间戳逻辑合理
- slug 唯一性得到保证

### 扩展性
- 可以轻松添加更多用户、文章、评论
- 支持多语言内容
- 分类结构支持无限层级

## 🔧 自定义种子数据

### 添加新用户
在 `001-admin-user.ts` 中的 `users` 数组添加新用户对象。

### 添加新分类
在 `002-categories.ts` 中添加新分类，注意设置正确的 `parent_id` 和 `sort` 值。

### 添加新文章
在 `004-articles.ts` 中的 `sampleArticles` 数组添加新文章对象。

### 添加新评论
在 `005-comments.ts` 中的 `comments` 数组添加新评论对象。

## 📈 数据质量保证

- **完整性**: 所有必需字段都有值
- **一致性**: 关联关系正确
- **真实性**: 内容贴近实际使用场景
- **多样性**: 涵盖不同类型和状态的数据
- **可测试性**: 便于功能测试和演示

## 🎯 设计目标

1. **功能完整**: 覆盖系统所有主要功能
2. **数据丰富**: 提供足够的测试数据
3. **关系正确**: 所有数据关联关系准确
4. **易于维护**: 代码结构清晰，便于修改
5. **生产就绪**: 可直接用于演示和测试
