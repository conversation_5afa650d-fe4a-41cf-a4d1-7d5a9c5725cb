import type { Tag } from './tag'
import type { Category } from './category'

/**
 * 文章信息接口
 * @property id - 文章唯一标识
 * @property title - 文章标题
 * @property slug - 文章别名
 * @property content - 文章内容
 * @property excerpt - 文章摘要（可选）
 * @property status - 文章状态（草稿或已发布）
 * @property authorId - 作者ID
 * @property categoryId - 分类ID（可选）
 * @property createdAt - 创建时间
 * @property updatedAt - 更新时间
 * @property publishedAt - 发布时间（可选）
 * @property tags - 标签列表（可选）
 * @property category - 分类信息（可选）
 */
export interface Article {
  id: number
  title: string
  slug: string
  content: string
  excerpt?: string
  status: 'draft' | 'published'
  authorId: number
  categoryId?: number
  createdAt: string
  updatedAt: string
  publishedAt?: string
  tags?: Tag[]
  category?: Category
}

/**
 * 文章列表响应数据接口
 */
export interface ArticlesResponse {
  /**
   * 请求是否成功
   */
  success: boolean
  /**
   * 文章列表数据
   */
  articles: Article[]
  /**
   * 分页信息
   */
  pagination: {
    /**
     * 当前页码
     */
    page: number
    /**
     * 每页条数
     */
    limit: number
    /**
     * 总条数
     */
    total: number
    /**
     * 总页数
     */
    totalPages: number
  }
}

/**
 * 获取文章列表的查询参数接口
 */
export interface ArticleParams {
  /**
   * 页码
   */
  page?: number
  /**
   * 每页条数
   */
  limit?: number
  /**
   * 标签筛选
   */
  tag?: string
  /**
   * 分类筛选
   */
  category?: string | number
  /**
   * 状态筛选：草稿或已发布
   */
  status?: 'draft' | 'published'
  /**
   * 搜索关键词
   */
  search?: string
  /**
   * 作者ID筛选
   */
  author?: number
  /**
   * 排序字段
   */
  sort?: string
  /**
   * 排序方向
   */
  order?: 'asc' | 'desc'
}
