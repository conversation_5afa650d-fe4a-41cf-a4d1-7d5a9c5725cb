import type { BasicUser } from './user'

/**
 * 用户信息接口（认证用）
 */
export type User = BasicUser

/**
 * 登录响应数据接口
 */
export interface LoginResponse {
  /**
   * 请求是否成功
   */
  success: boolean
  /**
   * 响应数据
   */
  data: {
    /**
     * 用户信息
     */
    user: User
    /**
     * 访问令牌
     */
    token: string
  }
  /**
   * 响应消息
   */
  message: string
}

/**
 * 获取用户资料响应数据接口
 */
export interface ProfileResponse {
  /**
   * 请求是否成功
   */
  success: boolean
  /**
   * 响应数据
   */
  data: {
    /**
     * 用户信息
     */
    user: User
  }
}

/**
 * API错误响应接口
 */
export interface ApiError {
  /**
   * 请求是否成功（固定为false）
   */
  success: false
  /**
   * 错误详情
   */
  error: {
    /**
     * 错误代码
     */
    code: string
    /**
     * 错误消息
     */
    message: string
    /**
     * 错误详细信息（可选）
     */
    details?: any
  }
}
