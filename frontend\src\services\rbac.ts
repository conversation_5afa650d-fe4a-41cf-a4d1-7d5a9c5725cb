import api, { createApiRequest, handleApiError } from './api'
import type {
  Role,
  Permission,
  CreateRoleData,
  UpdateRoleData,
  UserRole,
  AssignRoleData,
  PaginatedResponse,
  ApiResponse,
  BasicUser
} from '@/types/user'



/**
 * 角色管理API服务
 */
export class RoleService {
  /**
   * 获取角色列表
   */
  static async getRoles(params?: {
    page?: number
    limit?: number
    search?: string
    isActive?: boolean
    isSystem?: boolean
  }): Promise<PaginatedResponse<Role>> {
    try {
      const response = await createApiRequest(
        () => api.get('/roles', { params }),
        '正在加载角色列表...'
      )
      return (response as any).data || { items: [], pagination: { page: 1, limit: 10, total: 0, totalPages: 0, hasNextPage: false, hasPrevPage: false } }
    } catch (error) {
      handleApiError(error, '获取角色列表失败')
      throw error
    }
  }

  /**
   * 获取单个角色详情
   */
  static async getRole(id: number): Promise<Role> {
    try {
      const response = await createApiRequest(
        () => api.get(`/roles/${id}`),
        '正在加载角色详情...'
      )
      return (response as any).data
    } catch (error) {
      handleApiError(error, '获取角色详情失败')
      throw error
    }
  }

  /**
   * 创建角色
   */
  static async createRole(data: CreateRoleData): Promise<Role> {
    try {
      const response = await createApiRequest(
        () => api.post('/roles', data),
        '正在创建角色...'
      )
      return (response as any).data
    } catch (error) {
      handleApiError(error, '创建角色失败')
      throw error
    }
  }

  /**
   * 更新角色
   */
  static async updateRole(id: number, data: UpdateRoleData): Promise<Role> {
    try {
      const response = await createApiRequest(
        () => api.put(`/roles/${id}`, data),
        '正在更新角色...'
      )
      return (response as any).data
    } catch (error) {
      handleApiError(error, '更新角色失败')
      throw error
    }
  }

  /**
   * 删除角色
   */
  static async deleteRole(id: number): Promise<void> {
    try {
      await createApiRequest(
        () => api.delete(`/roles/${id}`),
        '正在删除角色...'
      )
    } catch (error) {
      handleApiError(error, '删除角色失败')
      throw error
    }
  }

  /**
   * 批量删除角色
   */
  static async batchDeleteRoles(ids: number[]): Promise<void> {
    try {
      await createApiRequest(
        () => api.post('/roles/batch-delete', { ids }),
        '正在批量删除角色...'
      )
    } catch (error) {
      handleApiError(error, '批量删除角色失败')
      throw error
    }
  }

  /**
   * 获取角色权限
   */
  static async getRolePermissions(id: number): Promise<Permission[]> {
    try {
      const response = await createApiRequest(
        () => api.get(`/roles/${id}/permissions`),
        '正在加载角色权限...'
      )
      return (response as any).data || []
    } catch (error) {
      handleApiError(error, '获取角色权限失败')
      throw error
    }
  }

  /**
   * 分配权限给角色
   */
  static async assignPermissions(roleId: number, permissionIds: number[]): Promise<void> {
    try {
      await createApiRequest(
        () => api.post(`/roles/${roleId}/permissions`, { permissionIds }),
        '正在分配权限...'
      )
    } catch (error) {
      handleApiError(error, '分配权限失败')
      throw error
    }
  }

  /**
   * 移除角色权限
   */
  static async removePermissions(roleId: number, permissionIds: number[]): Promise<void> {
    try {
      await createApiRequest(
        () => api.delete(`/roles/${roleId}/permissions`, { data: { permissionIds } }),
        '正在移除权限...'
      )
    } catch (error) {
      handleApiError(error, '移除权限失败')
      throw error
    }
  }

  /**
   * 获取角色用户列表
   */
  static async getRoleUsers(id: number, params?: {
    page?: number
    limit?: number
  }): Promise<PaginatedResponse<UserRole>> {
    try {
      const response = await createApiRequest(
        () => api.get(`/roles/${id}/users`, { params }),
        '正在加载角色用户列表...'
      )
      return (response as any).data || { items: [], pagination: { page: 1, limit: 10, total: 0, totalPages: 0, hasNextPage: false, hasPrevPage: false } }
    } catch (error) {
      handleApiError(error, '获取角色用户列表失败')
      throw error
    }
  }
}

/**
 * 权限管理API服务
 */
export class PermissionService {
  /**
   * 获取权限列表
   */
  static async getPermissions(params?: {
    page?: number
    limit?: number
    search?: string
    resource?: string
    action?: string
    isActive?: boolean
  }): Promise<PaginatedResponse<Permission>> {
    try {
      const response = await createApiRequest(
        () => api.get('/permissions', { params }),
        '正在加载权限列表...'
      )
      return (response as any).data || { items: [], pagination: { page: 1, limit: 10, total: 0, totalPages: 0, hasNextPage: false, hasPrevPage: false } }
    } catch (error) {
      handleApiError(error, '获取权限列表失败')
      throw error
    }
  }

  /**
   * 获取权限资源列表
   */
  static async getResources(): Promise<string[]> {
    try {
      const response = await createApiRequest(
        () => api.get('/permissions/resources'),
        '正在加载资源列表...'
      )
      return (response as any).data || []
    } catch (error) {
      handleApiError(error, '获取资源列表失败')
      throw error
    }
  }

  /**
   * 获取指定资源的操作列表
   */
  static async getActions(resource: string): Promise<string[]> {
    try {
      const response = await createApiRequest(
        () => api.get(`/permissions/resources/${resource}/actions`),
        '正在加载操作列表...'
      )
      return (response as any).data || []
    } catch (error) {
      handleApiError(error, '获取操作列表失败')
      throw error
    }
  }

  /**
   * 获取权限树形结构
   */
  static async getPermissionTree(): Promise<any[]> {
    try {
      const response = await createApiRequest(
        () => api.get('/permissions/tree'),
        '正在加载权限树...'
      )
      return (response as any).data || []
    } catch (error) {
      handleApiError(error, '获取权限树失败')
      throw error
    }
  }
}

/**
 * 用户角色管理API服务
 */
export class UserRoleService {
  /**
   * 获取用户角色分配列表
   */
  static async getUserRoles(params?: {
    page?: number
    limit?: number
    userId?: number
    roleId?: number
    search?: string
  }): Promise<PaginatedResponse<UserRole>> {
    try {
      const response = await createApiRequest(
        () => api.get('/user-roles', { params }),
        '正在加载用户角色列表...'
      )
      return (response as any).data || { items: [], pagination: { page: 1, limit: 10, total: 0, totalPages: 0, hasNextPage: false, hasPrevPage: false } }
    } catch (error) {
      handleApiError(error, '获取用户角色列表失败')
      throw error
    }
  }

  /**
   * 分配角色给用户
   */
  static async assignRole(data: AssignRoleData): Promise<UserRole> {
    try {
      const response = await createApiRequest(
        () => api.post('/user-roles', data),
        '正在分配角色...'
      )
      return (response as any).data
    } catch (error) {
      handleApiError(error, '分配角色失败')
      throw error
    }
  }

  /**
   * 移除用户角色
   */
  static async removeRole(id: number): Promise<void> {
    try {
      await createApiRequest(
        () => api.delete(`/user-roles/${id}`),
        '正在移除角色...'
      )
    } catch (error) {
      handleApiError(error, '移除角色失败')
      throw error
    }
  }

  /**
   * 批量分配角色
   */
  static async batchAssignRoles(assignments: AssignRoleData[]): Promise<UserRole[]> {
    try {
      const response = await createApiRequest(
        () => api.post('/user-roles/batch', { assignments }),
        '正在批量分配角色...'
      )
      return (response as any).data || []
    } catch (error) {
      handleApiError(error, '批量分配角色失败')
      throw error
    }
  }

  /**
   * 获取用户的角色列表
   */
  static async getUserRolesByUserId(userId: number): Promise<Role[]> {
    try {
      const response = await createApiRequest(
        () => api.get(`/user-roles/user/${userId}/roles`),
        '正在加载用户角色...'
      )
      return (response as any).data || []
    } catch (error) {
      handleApiError(error, '获取用户角色失败')
      throw error
    }
  }

  /**
   * 检查用户权限
   */
  static async checkUserPermission(userId: number, permission: string): Promise<boolean> {
    try {
      const response = await createApiRequest(
        () => api.get(`/user-roles/user/${userId}/permissions/${permission}`),
        '正在检查权限...'
      )
      return (response as any).data || false
    } catch (error) {
      handleApiError(error, '检查权限失败')
      throw error
    }
  }
}
