import { request } from '@/utils/request'
import type {
  Role,
  Permission,
  CreateRoleData,
  UpdateRoleData,
  UserRole,
  AssignRoleData,
  PaginatedResponse,
  ApiResponse,
  BasicUser
} from '@/types/user'



/**
 * 角色管理API服务
 */
export class RoleService {
  /**
   * 获取角色列表
   */
  static async getRoles(params?: {
    page?: number
    limit?: number
    search?: string
    isActive?: boolean
    isSystem?: boolean
  }): Promise<PaginatedResponse<Role>> {
    const response = await request.get<ApiResponse<PaginatedResponse<Role>>>('/roles', {
      params
    })
    return response.data.data
  }

  /**
   * 获取单个角色详情
   */
  static async getRole(id: number): Promise<Role> {
    const response = await request.get<ApiResponse<Role>>(`/roles/${id}`)
    return response.data.data
  }

  /**
   * 创建角色
   */
  static async createRole(data: CreateRoleData): Promise<Role> {
    const response = await request.post<ApiResponse<Role>>('/roles', data)
    return response.data.data
  }

  /**
   * 更新角色
   */
  static async updateRole(id: number, data: UpdateRoleData): Promise<Role> {
    const response = await request.put<ApiResponse<Role>>(`/roles/${id}`, data)
    return response.data.data
  }

  /**
   * 删除角色
   */
  static async deleteRole(id: number): Promise<void> {
    await request.delete(`/roles/${id}`)
  }

  /**
   * 批量删除角色
   */
  static async batchDeleteRoles(ids: number[]): Promise<void> {
    await request.post('/roles/batch-delete', { ids })
  }

  /**
   * 获取角色权限
   */
  static async getRolePermissions(id: number): Promise<Permission[]> {
    const response = await request.get<ApiResponse<Permission[]>>(`/roles/${id}/permissions`)
    return response.data.data
  }

  /**
   * 分配权限给角色
   */
  static async assignPermissions(roleId: number, permissionIds: number[]): Promise<void> {
    await request.post(`/roles/${roleId}/permissions`, { permissionIds })
  }

  /**
   * 移除角色权限
   */
  static async removePermissions(roleId: number, permissionIds: number[]): Promise<void> {
    await request.delete(`/roles/${roleId}/permissions`, { data: { permissionIds } })
  }

  /**
   * 获取角色用户列表
   */
  static async getRoleUsers(id: number, params?: {
    page?: number
    limit?: number
  }): Promise<PaginatedResponse<UserRole>> {
    const response = await request.get<ApiResponse<PaginatedResponse<UserRole>>>(`/roles/${id}/users`, {
      params
    })
    return response.data.data
  }
}

/**
 * 权限管理API服务
 */
export class PermissionService {
  /**
   * 获取权限列表
   */
  static async getPermissions(params?: {
    page?: number
    limit?: number
    search?: string
    resource?: string
    action?: string
    isActive?: boolean
  }): Promise<PaginatedResponse<Permission>> {
    const response = await request.get<ApiResponse<PaginatedResponse<Permission>>>('/permissions', {
      params
    })
    return response.data.data
  }

  /**
   * 获取权限资源列表
   */
  static async getResources(): Promise<string[]> {
    const response = await request.get<ApiResponse<string[]>>('/permissions/resources')
    return response.data.data
  }

  /**
   * 获取指定资源的操作列表
   */
  static async getActions(resource: string): Promise<string[]> {
    const response = await request.get<ApiResponse<string[]>>(`/permissions/resources/${resource}/actions`)
    return response.data.data
  }

  /**
   * 获取权限树形结构
   */
  static async getPermissionTree(): Promise<any[]> {
    const response = await request.get<ApiResponse<any[]>>('/permissions/tree')
    return response.data.data
  }
}

/**
 * 用户角色管理API服务
 */
export class UserRoleService {
  /**
   * 获取用户角色分配列表
   */
  static async getUserRoles(params?: {
    page?: number
    limit?: number
    userId?: number
    roleId?: number
    search?: string
  }): Promise<PaginatedResponse<UserRole>> {
    const response = await request.get<ApiResponse<PaginatedResponse<UserRole>>>('/user-roles', {
      params
    })
    return response.data.data
  }

  /**
   * 分配角色给用户
   */
  static async assignRole(data: AssignRoleData): Promise<UserRole> {
    const response = await request.post<ApiResponse<UserRole>>('/user-roles', data)
    return response.data.data
  }

  /**
   * 移除用户角色
   */
  static async removeRole(id: number): Promise<void> {
    await request.delete(`/user-roles/${id}`)
  }

  /**
   * 批量分配角色
   */
  static async batchAssignRoles(assignments: AssignRoleData[]): Promise<UserRole[]> {
    const response = await request.post<ApiResponse<UserRole[]>>('/user-roles/batch', { assignments })
    return response.data.data
  }

  /**
   * 获取用户的角色列表
   */
  static async getUserRolesByUserId(userId: number): Promise<Role[]> {
    const response = await request.get<ApiResponse<Role[]>>(`/user-roles/users/${userId}/roles`)
    return response.data.data
  }

  /**
   * 检查用户权限
   */
  static async checkUserPermission(userId: number, permission: string): Promise<boolean> {
    const response = await request.get<ApiResponse<boolean>>(`/user-roles/users/${userId}/permissions/${permission}/check`)
    return response.data.data
  }
}
