"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.down = exports.up = void 0;
const sequelize_1 = require("sequelize");
const up = async (queryInterface) => {
    await queryInterface.createTable('users', {
        id: {
            type: sequelize_1.DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false,
            comment: '用户ID'
        },
        username: {
            type: sequelize_1.DataTypes.STRING(50),
            allowNull: false,
            unique: true,
            comment: '用户名'
        },
        email: {
            type: sequelize_1.DataTypes.STRING(100),
            allowNull: false,
            unique: true,
            comment: '邮箱地址'
        },
        password_hash: {
            type: sequelize_1.DataTypes.STRING(255),
            allowNull: false,
            comment: '密码哈希'
        },
        is_active: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: true,
            comment: '是否激活'
        },
        email_verified: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false,
            comment: '邮箱是否已验证'
        },
        email_verified_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: true,
            comment: '邮箱验证时间'
        },
        last_login_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: true,
            comment: '最后登录时间'
        },
        password_reset_token: {
            type: sequelize_1.DataTypes.STRING(255),
            allowNull: true,
            comment: '密码重置令牌'
        },
        password_reset_expires: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: true,
            comment: '密码重置令牌过期时间'
        },
        email_verification_token: {
            type: sequelize_1.DataTypes.STRING(255),
            allowNull: true,
            comment: '邮箱验证令牌'
        },
        email_verification_expires: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: true,
            comment: '邮箱验证令牌过期时间'
        },
        created_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '创建时间'
        },
        updated_at: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '更新时间'
        }
    }, {
        charset: 'utf8mb4',
        collate: 'utf8mb4_unicode_ci',
        comment: '用户表'
    });
    await queryInterface.addIndex('users', ['email'], {
        name: 'idx_users_email',
        unique: true
    });
    await queryInterface.addIndex('users', ['username'], {
        name: 'idx_users_username',
        unique: true
    });
    await queryInterface.addIndex('users', ['is_active'], {
        name: 'idx_users_is_active'
    });
    await queryInterface.addIndex('users', ['email_verified'], {
        name: 'idx_users_email_verified'
    });
    await queryInterface.addIndex('users', ['password_reset_token'], {
        name: 'idx_users_password_reset_token'
    });
    await queryInterface.addIndex('users', ['email_verification_token'], {
        name: 'idx_users_email_verification_token'
    });
    console.log('✅ Created users table with indexes');
};
exports.up = up;
const down = async (queryInterface) => {
    await queryInterface.dropTable('users');
    console.log('✅ Dropped users table');
};
exports.down = down;
//# sourceMappingURL=001-create-users.js.map